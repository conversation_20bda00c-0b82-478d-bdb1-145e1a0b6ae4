from PyQt5.QtWidgets import QApplication
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from PyQt5.QtGui import QFont, QTextCursor, QTextTableFormat, QTextTable, QTextCharFormat, QTextDocument
from PyQt5.QtGui import QBrush, QColor, QPen, QTextFrameFormat, QTextLength, QTextBlockFormat
from PyQt5.QtCore import Qt, QSizeF, QMarginsF, QDateTime

class DirectPrinter:
    """فئة للطباعة المباشرة باستخدام Qt"""
    
    def __init__(self):
        """تهيئة الطابعة"""
        self.printer = QPrinter(QPrinter.HighResolution)
        # استخدام QPrinter.Portrait بدلاً من QPageLayout.Portrait
        self.printer.setOrientation(QPrinter.Portrait)
        # تعيين هوامش الصفحة (10 ملم لكل جانب)
        self.printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)
        
    def print_document(self, document, title="طباعة مستند"):
        """طباعة مستند نصي
        
        المعلمات:
            document (QTextDocument): المستند المراد طباعته
            title (str): عنوان مهمة الطباعة
            
        العائد:
            bool: True إذا تمت الطباعة بنجاح، False إذا تم إلغاؤها
        """
        try:
            # تعيين عنوان مهمة الطباعة
            self.printer.setDocName(title)
            
            # إنشاء مربع حوار الطباعة
            dialog = QPrintDialog(self.printer)
            if dialog.exec_() == QPrintDialog.Accepted:
                # طباعة المستند
                document.print_(self.printer)
                return True
            return False
        except Exception as e:
            print(f"خطأ في طباعة المستند: {str(e)}")
            return False
    
    def create_document(self, title, orders=None, dimensions=None):
        """إنشاء مستند للطباعة
        
        المعلمات:
            title (str): عنوان المستند
            orders (list): قائمة الطلبات
            dimensions (list): قائمة أبعاد السجاد
            
        العائد:
            QTextDocument: المستند المنشأ
        """
        # إنشاء مستند جديد
        document = QTextDocument()
        document.setDocumentMargin(10)
        
        # الحصول على مؤشر النص
        cursor = QTextCursor(document)
        
        # إضافة العنوان
        self.add_header(cursor, title)
        
        # إضافة جدول الطلبات إذا كان متوفراً
        if orders and len(orders) > 0:
            cursor.insertBlock()
            self.add_table(cursor, orders, "جدول الطلبات", [
                "رقم الطلب", "اسم الزبون", "رقم الهاتف", "العنوان", "رقم الوصل", "ملاحظات"
            ])
        
        # إضافة جدول أبعاد السجاد إذا كان متوفراً
        if dimensions and len(dimensions) > 0:
            cursor.insertBlock()
            self.add_table(cursor, dimensions, "جدول أبعاد السجاد", [
                "رقم الطلب", "الطول", "العرض", "المساحة الكلية"
            ])
        
        # إضافة التذييل
        self.add_footer(cursor)
        
        return document
    
    def add_header(self, cursor, title):
        """إضافة عنوان للمستند
        
        المعلمات:
            cursor (QTextCursor): مؤشر النص
            title (str): عنوان المستند
        """
        # تنسيق العنوان
        header_format = QTextBlockFormat()
        header_format.setAlignment(Qt.AlignCenter)
        cursor.insertBlock(header_format)
        
        # تنسيق نص العنوان
        header_char_format = QTextCharFormat()
        header_char_format.setFontFamily("Tahoma")
        header_char_format.setFontPointSize(16)
        header_char_format.setFontWeight(QFont.Bold)
        
        # إضافة العنوان
        cursor.insertText(title, header_char_format)
        
        # إضافة مسافة بعد العنوان
        cursor.insertBlock()
        cursor.insertBlock()
    
    def add_table(self, cursor, data, table_title, headers):
        """إضافة جدول للمستند
        
        المعلمات:
            cursor (QTextCursor): مؤشر النص
            data (list): بيانات الجدول
            table_title (str): عنوان الجدول
            headers (list): رؤوس الجدول
        """
        # إضافة عنوان الجدول
        title_format = QTextBlockFormat()
        title_format.setAlignment(Qt.AlignRight)
        cursor.insertBlock(title_format)
        
        title_char_format = QTextCharFormat()
        title_char_format.setFontFamily("Arial")
        title_char_format.setFontPointSize(14)
        title_char_format.setFontWeight(QFont.Bold)
        
        cursor.insertText(table_title, title_char_format)
        cursor.insertBlock()
        
        # إنشاء تنسيق الجدول
        table_format = QTextTableFormat()
        table_format.setBorderStyle(QTextFrameFormat.BorderStyle_Solid)
        table_format.setBorder(0.5)
        table_format.setCellPadding(4)  # زيادة الحشو داخل الخلايا
        table_format.setCellSpacing(0)
        table_format.setHeaderRowCount(1)
        
        # لا يمكن استخدام setTopBorder و setBottomBorder مع QTextTableFormat
        # table_format.setTopBorder(1.0)
        # table_format.setBottomBorder(0.5)
        
        # تعيين عرض الجدول بنسبة من عرض الصفحة
        table_format.setWidth(QTextLength(QTextLength.PercentageLength, 100))
        
        # تعيين عرض الأعمدة
        # تسلسل: 7%، العنوان: 25%، رقم الهاتف: 20%، ملاحظات: 48%
        column_widths = [48, 20, 25, 7]  # بالترتيب: ملاحظات، رقم الهاتف، العنوان، تسلسل
        table_format.setColumnWidthConstraints([QTextLength(QTextLength.PercentageLength, width) for width in column_widths])
        
        # إنشاء الجدول
        cols = len(headers)
        rows = len(data) + 1  # صف إضافي للعناوين
        table = cursor.insertTable(rows, cols, table_format)
        
        # تنسيق خلايا العنوان
        header_format = QTextCharFormat()
        header_format.setFontFamily("Arial")
        header_format.setFontPointSize(10)  # تكبير حجم خط العناوين
        header_format.setFontWeight(QFont.Bold)
        # استخدام لون أزرق فاتح للخلفية بدلاً من الرمادي
        header_format.setBackground(QBrush(QColor("#e3f2fd")))
        
        # إضافة العناوين
        for col, header in enumerate(headers):
            cell = table.cellAt(0, col)
            cell_cursor = cell.firstCursorPosition()
            cell_block_format = QTextBlockFormat()
            cell_block_format.setAlignment(Qt.AlignCenter)
            cell_cursor.setBlockFormat(cell_block_format)
            cell_cursor.insertText(header, header_format)
        
        # تنسيق خلايا البيانات
        cell_format = QTextCharFormat()
        cell_format.setFontFamily("Arial")
        cell_format.setFontPointSize(11)
        
        # إضافة البيانات
        for row, item in enumerate(data, 1):
            # إضافة خلفية لصفوف البيانات بالتناوب للتمييز بينها
            row_format = QTextCharFormat(cell_format)
            if row % 2 == 0:  # الصفوف الزوجية
                row_format.setBackground(QBrush(QColor("#f5f5f5")))
                
            for col, key in enumerate(get_table_keys(headers)):
                cell = table.cellAt(row, col)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                
                # الحصول على قيمة الخلية
                if isinstance(item, dict):
                    value = str(item.get(key, ""))
                else:
                    # إذا كانت البيانات قائمة، استخدم الفهرس
                    value = str(item[col]) if col < len(item) else ""
                
                cell_cursor.insertText(value, row_format)
    
    def add_footer(self, cursor):
        """إضافة تذييل للمستند
        
        المعلمات:
            cursor (QTextCursor): مؤشر النص
        """
        # إضافة مسافة قبل التذييل
        cursor.insertBlock()
        cursor.insertBlock()
        
        # تنسيق التذييل
        footer_format = QTextBlockFormat()
        footer_format.setAlignment(Qt.AlignCenter)
        cursor.insertBlock(footer_format)
        
        # تنسيق نص التذييل
        footer_char_format = QTextCharFormat()
        footer_char_format.setFontFamily("Arial")
        footer_char_format.setFontPointSize(10)
        footer_char_format.setFontItalic(True)
        
        # لا نضيف نص التذييل (تم حذف عبارة "شكراً لتعاملكم معنا")
    
    def print_delegate_orders_with_dimensions(self, orders, dimensions, title="كشف طلبات المندوبين"):
        """طباعة طلبات المندوبين مع أبعاد السجاد
        
        المعلمات:
            orders (list): قائمة الطلبات
            dimensions (list): قائمة أبعاد السجاد
            title (str): عنوان المستند
            
        العائد:
            bool: True إذا تمت الطباعة بنجاح، False إذا تم إلغاؤها
        """
        try:
            # إنشاء مستند جديد
            document = QTextDocument()
            document.setDocumentMargin(10)
            
            # الحصول على مؤشر النص
            cursor = QTextCursor(document)
            
            # إضافة العنوان
            self.add_header(cursor, title)
            
            # تقليل المسافة بين التاريخ والجدول
            block_format = QTextBlockFormat()
            block_format.setTopMargin(2)
            block_format.setBottomMargin(2)
            cursor.insertBlock(block_format)
            
            # إنشاء تنسيق الجدول
            table_format = QTextTableFormat()
            table_format.setBorderStyle(QTextFrameFormat.BorderStyle_Solid)
            table_format.setBorder(0.5)
            table_format.setCellPadding(3)  # تقليل الحشو داخل الخلايا
            table_format.setCellSpacing(0)
            table_format.setHeaderRowCount(1)
            
            # تعيين عرض الجدول بنسبة من عرض الصفحة
            table_format.setWidth(QTextLength(QTextLength.PercentageLength, 100))
            
            # إنشاء الجدول بالحقول الجديدة
            headers = ["ملاحظات", "العنوان", "رقم الهاتف", "مساحة السجاد", "السعر"]
            cols = len(headers)
            rows = len(orders) + 2  # صف إضافي للعناوين وصف للمجموع
            table = cursor.insertTable(rows, cols, table_format)
            
            # تعيين عرض الأعمدة الجديد حسب المتطلبات
            column_widths = [25, 30, 20, 10, 15]  # بالترتيب: ملاحظات، العنوان، رقم الهاتف، مساحة السجاد، السعر
            table_format.setColumnWidthConstraints([QTextLength(QTextLength.PercentageLength, width) for width in column_widths])
            
            # تنسيق خلايا العنوان
            header_format = QTextCharFormat()
            header_format.setFontFamily("Tahoma")
            header_format.setFontPointSize(11)  # حجم خط العناوين
            header_format.setFontWeight(QFont.Bold)
            # استخدام لون أزرق فاتح للخلفية
            header_format.setBackground(QBrush(QColor("#e3f2fd")))
            
            # إضافة العناوين
            for col, header in enumerate(headers):
                cell = table.cellAt(0, col)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                cell_cursor.insertText(header, header_format)
                
            # تنسيق خلايا البيانات
            cell_format = QTextCharFormat()
            cell_format.setFontFamily("Arial")
            cell_format.setFontPointSize(10)
            
            # متغيرات لحساب المجموع الكلي
            total_price_sum = 0
            total_area_sum = 0
            
            # إضافة البيانات
            for row, order in enumerate(orders, 1):
                # إضافة خلفية لصفوف البيانات بالتناوب للتمييز بينها
                row_format = QTextCharFormat(cell_format)
                if row % 2 == 0:  # الصفوف الزوجية
                    row_format.setBackground(QBrush(QColor("#f5f5f5")))
                
                # ملاحظات (العمود 0)
                cell = table.cellAt(row, 0)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                notes = order.get("notes", "")
                cell_cursor.insertText(str(notes), row_format)
                
                # العنوان (العمود 1)
                cell = table.cellAt(row, 1)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                address = order.get("address", "")
                cell_cursor.insertText(str(address), row_format)
                
                # رقم الهاتف (العمود 2)
                cell = table.cellAt(row, 2)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                phone_format = QTextCharFormat(row_format)
                phone_format.setFontWeight(QFont.Bold)
                phone_format.setForeground(QBrush(QColor("#e74c3c")))
                phone = order.get("phone", "")
                cell_cursor.insertText(str(phone), phone_format)
                
                # مساحة السجاد (العمود 3)
                cell = table.cellAt(row, 3)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                
                # سحب مساحة السجاد مباشرة من قاعدة البيانات
                total_area = float(order.get('total_area', 0) or 0)
                total_area_sum += total_area
                cell_cursor.insertText(str(total_area), row_format)
                
                # السعر (العمود 4)
                cell = table.cellAt(row, 4)
                cell_cursor = cell.firstCursorPosition()
                cell_block_format = QTextBlockFormat()
                cell_block_format.setAlignment(Qt.AlignCenter)
                cell_cursor.setBlockFormat(cell_block_format)
                price = float(order.get("total_price", 0) or 0)
                total_price_sum += price
                formatted_price = f"{int(price):,}".replace(",", "٬")
                cell_cursor.insertText(formatted_price, row_format)
            
            # إضافة صف المجموع الكلي
            total_row = len(orders) + 1
            total_row_format = QTextCharFormat(cell_format)
            total_row_format.setFontWeight(QFont.Bold)
            total_row_format.setBackground(QBrush(QColor("#e3f2fd")))
            
            # المجموع الكلي للسعر
            cell = table.cellAt(total_row, 4)
            cell_cursor = cell.firstCursorPosition()
            cell_block_format = QTextBlockFormat()
            cell_block_format.setAlignment(Qt.AlignCenter)
            cell_cursor.setBlockFormat(cell_block_format)
            formatted_total_price = f"{int(total_price_sum):,}".replace(",", "٬")
            cell_cursor.insertText(formatted_total_price, total_row_format)
            
            # المجموع الكلي للمساحة
            cell = table.cellAt(total_row, 3)
            cell_cursor = cell.firstCursorPosition()
            cell_block_format = QTextBlockFormat()
            cell_block_format.setAlignment(Qt.AlignCenter)
            cell_cursor.setBlockFormat(cell_block_format)
            cell_cursor.insertText(str(total_area_sum), total_row_format)
            
            # كلمة المجموع الكلي
            cell = table.cellAt(total_row, 2)
            cell_cursor = cell.firstCursorPosition()
            cell_block_format = QTextBlockFormat()
            cell_block_format.setAlignment(Qt.AlignCenter)
            cell_cursor.setBlockFormat(cell_block_format)
            cell_cursor.insertText("المجموع الكلي", total_row_format)
            
            # دمج الخلايا من العمود 2 إلى 4 في صف المجموع
            table.mergeCells(total_row, 2, 1, 3)  # الصف، العمود، عدد الصفوف، عدد الأعمدة
            
            # إضافة التذييل
            self.add_footer(cursor)
            
            # طباعة المستند
            return self.print_document(document, title)
        except Exception as e:
            print(f"خطأ في طباعة طلبات المندوبين: {str(e)}")
            return False

def get_table_keys(headers):
    """تحويل عناوين الجدول إلى مفاتيح للقاموس
    
    المعلمات:
        headers (list): رؤوس الجدول
        
    العائد:
        list: قائمة المفاتيح
    """
    # قاموس لتحويل العناوين العربية إلى مفاتيح إنجليزية
    header_to_key = {
        "رقم الطلب": "id",
        "اسم الزبون": "customer_name",
        "رقم الهاتف": "phone",
        "العنوان": "address",
        "رقم الوصل": "receipt_number",
        "ملاحظات": "notes",
        "الطول": "length",
        "العرض": "width",
        "المساحة الكلية": "total_area"
    }
    
    # تحويل العناوين إلى مفاتيح
    return [header_to_key.get(header, header) for header in headers]
