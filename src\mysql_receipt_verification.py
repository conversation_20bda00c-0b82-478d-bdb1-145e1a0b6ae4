"""
وحدة التحقق من بيانات الطلبات وإصلاحها لقاعدة بيانات MySQL
"""
import pymysql
import logging
import sys
import os
from datetime import datetime, timedelta

# إعداد الترميز
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

# إعداد التسجيل
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"receipt_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('receipt_verification')

class ReceiptVerifier:
    """فئة للتحقق من أرقام الوصل في قاعدة بيانات MySQL"""
    
    def __init__(self, db_name, host='localhost', user='carpet_user', password='@#57111819752#@', port=3306):
        """
        تهيئة المتحقق
        
        المعلمات:
            db_name (str): اسم قاعدة البيانات
            host (str): مضيف قاعدة البيانات
            user (str): اسم المستخدم
            password (str): كلمة المرور
            port (int): منفذ الاتصال
        """
        self.db_config = {
            'host': host,
            'user': user,
            'password': password,
            'database': db_name,
            'port': port,
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'raise_on_warnings': True
        }
        logger.info(f"استخدام قاعدة بيانات MySQL: {db_name}")
        logger.info(f"تم تهيئة المتحقق مع قاعدة البيانات: {db_name}")
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return pymysql.connect(**self.db_config)
    
    def verify_recent_receipts(self, days=1):
        """
        التحقق من أرقام الوصل الحديثة
        
        المعلمات:
            days (int): عدد الأيام للتحقق من الطلبات الحديثة
            
        العائد:
            dict: نتائج التحقق
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # حساب التاريخ قبل عدد الأيام المحدد
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # الحصول على الطلبات الحديثة
            cursor.execute("""
                SELECT id, receipt_number, created_at
                FROM orders
                WHERE created_at >= %s AND receipt_number IS NOT NULL
                ORDER BY created_at DESC
            """, (cutoff_date,))
            
            recent_orders = cursor.fetchall()
            logger.info(f"تم العثور على {len(recent_orders)} طلب حديث (آخر {days} يوم)")
            
            # التحقق من كل طلب
            fixed_count = 0
            issues = []
            
            for order_id, receipt_number, created_at in recent_orders:
                # التحقق من صحة رقم الوصل
                if receipt_number and str(receipt_number).strip():
                    # هنا يمكن إضافة منطق التحقق من صحة رقم الوصل
                    # مثلاً التحقق من أن رقم الوصل فريد
                    pass
            
            conn.close()
            
            return {
                "total": len(recent_orders),
                "fixed": fixed_count,
                "issues": issues
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من أرقام الوصل الحديثة: {str(e)}")
            return {"success": False, "message": f"خطأ: {str(e)}"}
    
    def get_next_receipt_number(self):
        """
        الحصول على رقم الوصل التالي
        
        العائد:
            int: رقم الوصل التالي
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # البحث عن أكبر رقم وصل
            cursor.execute("SELECT MAX(CAST(receipt_number AS UNSIGNED)) FROM orders WHERE receipt_number IS NOT NULL AND receipt_number != ''")
            result = cursor.fetchone()[0]
            conn.close()
            
            if result is None:
                # إذا لم يكن هناك أرقام وصل، ابدأ من 1
                next_number = 1
            else:
                try:
                    # محاولة تحويل النتيجة إلى رقم وإضافة 1
                    next_number = int(result) + 1
                except (ValueError, TypeError):
                    # إذا فشل التحويل، ابدأ من 1
                    logger.warning(f"فشل تحويل أكبر رقم وصل '{result}' إلى رقم صحيح. سيتم استخدام 1 كرقم وصل تالي.")
                    next_number = 1
            
            logger.info(f"تم تحديد رقم الوصل التالي: {next_number}")
            return next_number
        except Exception as e:
            logger.error(f"خطأ في الحصول على رقم الوصل التالي: {str(e)}")
            return 1  # القيمة الافتراضية في حالة الخطأ

def verify_recent_receipts(db_config, days=1):
    """
    دالة مساعدة للتحقق من أرقام الوصل الحديثة
    
    المعلمات:
        db_config (dict): إعدادات قاعدة البيانات
        days (int): عدد الأيام للتحقق من الطلبات الحديثة
        
    العائد:
        dict: نتائج التحقق
    """
    verifier = ReceiptVerifier(
        db_name=db_config.get('database', 'carpet_cleaning_service'),
        host=db_config.get('host', 'localhost'),
        user=db_config.get('user', 'carpet_user'),
        password=db_config.get('password', '@#57111819752#@'),
        port=db_config.get('port', 3306)
    )
    return verifier.verify_recent_receipts(days)
