from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QInputDialog, 
                           QLabel, QLineEdit, QMessageBox, QWidget)
from PyQt5.QtCore import Qt
from db.models import Database

class OrdersWindow(QDialog):
    """نافذة قائمة الطلبات الواردة"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        # تهيئة واجهة المستخدم
        self.initUI()
        
    def transfer_to_delegate(self, selected_orders):
        """نقل الطلبات المحددة إلى المندوب"""
        try:
            if not selected_orders:
                QMessageBox.warning(self, "تنبيه", "لم يتم تحديد أي طلبات للنقل")
                return
            
            # فتح نافذة إدخال اسم المندوب
            delegate_name, ok = QInputDialog.getText(
                self, 
                "إضافة مندوب",
                "أدخل اسم المندوب:",
                QLineEdit.Normal
            )
            
            if ok and delegate_name:
                # إضافة المندوب إلى قاعدة البيانات إذا لم يكن موجوداً
                conn = self.parent.db.get_connection()
                cursor = conn.cursor()
                
                try:
                    # إضافة المندوب
                    cursor.execute("""
                        INSERT OR IGNORE INTO delegates (name)
                        VALUES (?)
                    """, (delegate_name,))
                    
                    # الحصول على معرف المندوب
                    cursor.execute("SELECT id FROM delegates WHERE name = ?", (delegate_name,))
                    delegate_id = cursor.fetchone()[0]
                    
                    # نقل الطلبات المحددة إلى المندوب
                    for order_id in selected_orders:
                        # تحديث حالة الطلب وربطه بالمندوب
                        cursor.execute("""
                            UPDATE orders 
                            SET delegate_id = ?,
                                delegate_name = ?,
                                status = 'تم النقل للمندوب'
                            WHERE id = ?
                        """, (delegate_id, delegate_name, order_id))
                        
                        # إضافة الطلب إلى جدول طلبات المندوبين
                        cursor.execute("""
                            INSERT INTO delegate_orders 
                            (receipt_number, phone_number, area, carpet_count, blanket_count,
                            total_price, order_id, delegate_id, representative_name)
                            SELECT 
                                receipt_number,
                                COALESCE(phone, phone2),
                                address,
                                carpet_count,
                                blanket_count,
                                total_price,
                                id,
                                ?,
                                ?
                            FROM orders
                            WHERE id = ?
                        """, (delegate_id, delegate_name, order_id))
                    
                    conn.commit()
                    QMessageBox.information(
                        self,
                        "تم بنجاح",
                        f"تم نقل {len(selected_orders)} طلب إلى المندوب {delegate_name}"
                    )
                    
                    # إعادة تحديث عرض الطلبات
                    self.load_orders()
                    
                except Exception as e:
                    print(f"خطأ في نقل الطلبات: {str(e)}")
                    conn.rollback()
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"حدث خطأ أثناء نقل الطلبات: {str(e)}"
                    )
                finally:
                    conn.close()
        
        except Exception as e:
            print(f"خطأ في نقل الطلبات: {str(e)}")
            QMessageBox.critical(
                self,
                "خطأ", 
                "حدث خطأ أثناء تنفيذ العملية"
            )