"""
التحقق من الجداول الموجودة في قاعدة البيانات carpet_cleaning.db
"""

import sqlite3
import os

def check_sqlite_tables():
    """التحقق من الجداول الموجودة في قاعدة البيانات SQLite"""
    try:
        # التحقق من وجود ملف قاعدة البيانات
        db_path = 'carpet_cleaning.db'
        if not os.path.exists(db_path):
            print(f"ملف قاعدة البيانات SQLite غير موجود: {db_path}")
            return False
        
        print(f"ملف قاعدة البيانات SQLite موجود: {db_path}")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("جداول قاعدة البيانات SQLite:")
        for table in tables:
            table_name = table[0]
            print(f"\nجدول: {table_name}")
            
            # الحصول على هيكل الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("الأعمدة:")
            for column in columns:
                # column: (cid, name, type, notnull, dflt_value, pk)
                print(f"  {column[1]} ({column[2]}), NOT NULL: {column[3]}, DEFAULT: {column[4]}, PK: {column[5]}")
            
            # الحصول على عدد الصفوف في الجدول
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            print(f"عدد الصفوف: {row_count}")
            
            # إذا كان الجدول يحتوي على بيانات، عرض أول صف كمثال
            if row_count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                row = cursor.fetchone()
                print(f"مثال للبيانات: {row}")
        
        # إغلاق الاتصال
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في التحقق من الجداول: {str(e)}")
        return False

if __name__ == "__main__":
    check_sqlite_tables()
