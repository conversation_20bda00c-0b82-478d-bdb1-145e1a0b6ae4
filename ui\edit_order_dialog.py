from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLineEdit, QLabel, QSpinBox, QMessageBox)
from PyQt5.QtCore import Qt
from src.carpet_dimensions import CarpetDimensionsDialog
from ui.styles import get_button_style, COLORS, DIALOG_STYLE

class EditOrderDialog(QDialog):
    def __init__(self, parent=None, order_data=None, db=None):
        super().__init__(parent)
        self.setWindowTitle("تعديل الطلب")
        self.setModal(True)
        self.setMinimumWidth(400)
        self.setStyleSheet(DIALOG_STYLE)
        
        self.order_data = order_data
        self.db = db
        self.carpet_dimensions = []
        self.dimensions_dialog = None
        
        self.initUI()
        if order_data:
            self.load_order_data()
            
    def initUI(self):
        layout = QVBoxLayout()
        
        # رقم الوصل
        receipt_layout = QHBoxLayout()
        receipt_label = QLabel("رقم الوصل:")
        self.receipt_input = QLineEdit()
        receipt_layout.addWidget(receipt_label)
        receipt_layout.addWidget(self.receipt_input)
        layout.addLayout(receipt_layout)
        
        # العنوان
        address_layout = QHBoxLayout()
        address_label = QLabel("العنوان:")
        self.address_input = QLineEdit()
        address_layout.addWidget(address_label)
        address_layout.addWidget(self.address_input)
        layout.addLayout(address_layout)
        
        # عدد السجاد
        carpet_count_layout = QHBoxLayout()
        carpet_count_label = QLabel("عدد السجاد:")
        self.carpet_count_input = QSpinBox()
        self.carpet_count_input.setMinimum(1)
        self.carpet_count_input.setMaximum(100)
        carpet_count_layout.addWidget(carpet_count_label)
        carpet_count_layout.addWidget(self.carpet_count_input)
        layout.addLayout(carpet_count_layout)
        
        # السعر الإجمالي
        total_price_layout = QHBoxLayout()
        total_price_label = QLabel("السعر الإجمالي:")
        self.total_price_label = QLabel("0")
        total_price_layout.addWidget(total_price_label)
        total_price_layout.addWidget(self.total_price_label)
        layout.addLayout(total_price_layout)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(get_button_style(COLORS['success']))
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(get_button_style(COLORS['danger']))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
    def load_order_data(self):
        """تحميل بيانات الطلب"""
        if not self.order_data:
            return
            
        if 'receipt_number' in self.order_data:
            self.receipt_input.setText(str(self.order_data['receipt_number']))
        if 'address' in self.order_data:
            self.address_input.setText(self.order_data['address'])
        if 'carpet_count' in self.order_data:
            self.carpet_count_input.setValue(int(self.order_data['carpet_count']))
        if 'total_price' in self.order_data:
            self.total_price_label.setText(str(self.order_data['total_price']))
            
        # استرجاع أبعاد السجاد من قاعدة البيانات
        if self.db and 'id' in self.order_data:
            dimensions = self.db.get_carpet_dimensions(self.order_data['id'])
            if dimensions:
                self.carpet_dimensions = dimensions
                self.calculate_total_price()
                
    def show_dimensions_dialog(self):
        """عرض نافذة إدخال أبعاد السجاد"""
        count = self.carpet_count_input.value()
        if count > 0:
            self.dimensions_dialog = CarpetDimensionsDialog(self, count, self.carpet_dimensions)
            if self.dimensions_dialog.exec() == QDialog.Accepted:
                self.carpet_dimensions = self.dimensions_dialog.get_dimensions()
                self.calculate_total_price()
                
    def calculate_total_price(self):
        """حساب السعر الإجمالي"""
        total_price = 0
        for dim in self.carpet_dimensions:
            length = dim.get('length', 0)
            width = dim.get('width', 0)
            area = length * width
            # سعر المتر المربع 2000 دينار
            price = area * 2000
            total_price += price
            
        self.total_price_label.setText(str(total_price))
        
    def save_order(self):
        """حفظ التعديلات"""
        if not self.validate_inputs():
            return
            
        try:
            # تحديث بيانات الطلب
            order_data = {
                'id': self.order_data['id'],
                'receipt_number': self.receipt_input.text(),
                'address': self.address_input.text(),
                'carpet_count': self.carpet_count_input.value(),
                'total_price': float(self.total_price_label.text()),
                'dimensions': self.carpet_dimensions
            }
            
            # حفظ في قاعدة البيانات
            if self.db:
                success = self.db.update_order(order_data)
                if success:
                    QMessageBox.information(self, "نجاح", "تم حفظ التعديلات بنجاح")
                    self.accept()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حفظ التعديلات")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
            
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if not self.receipt_input.text():
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الوصل")
            return False
            
        if not self.address_input.text():
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال العنوان")
            return False
            
        if not self.carpet_dimensions:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال أبعاد السجاد")
            return False
            
        return True
        
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Tab and self.carpet_count_input.hasFocus():
            event.accept()  # منع انتشار الحدث
            self.show_dimensions_dialog()
        else:
            super().keyPressEvent(event)
