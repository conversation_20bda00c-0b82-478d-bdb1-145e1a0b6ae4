import os
import sys
import time
import threading
from datetime import datetime, timedelta
import logging

# إضافة المجلد الرئيسي إلى مسار البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import load_config
from utils.backup import create_backup, check_daily_backup, delete_old_backups

# إعداد التسجيل
if not os.path.exists('logs'):
    os.makedirs('logs')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'auto_backup_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('auto_backup')

def calculate_next_backup_time():
    """
    حساب وقت النسخ الاحتياطي التالي
    
    Returns:
        datetime: وقت النسخ الاحتياطي التالي
    """
    config = load_config()
    backup_time = config.get('backup_time', '00:00')
    
    try:
        # تحويل وقت النسخ الاحتياطي إلى ساعات ودقائق
        hour, minute = map(int, backup_time.split(':'))
        
        # تحديد وقت النسخ الاحتياطي التالي
        now = datetime.now()
        next_backup = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # إذا كان الوقت المحدد قد مر اليوم، فسيكون النسخ الاحتياطي التالي غداً
        if next_backup <= now:
            next_backup += timedelta(days=1)
        
        return next_backup
    except Exception as e:
        logger.error(f"خطأ في حساب وقت النسخ الاحتياطي التالي: {str(e)}")
        # استخدام الوقت الافتراضي (منتصف الليل)
        next_backup = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        return next_backup

def backup_scheduler():
    """
    جدولة النسخ الاحتياطي التلقائي
    """
    while True:
        try:
            config = load_config()
            
            # التحقق مما إذا كان النسخ الاحتياطي التلقائي مفعل
            if not config.get('auto_backup', True):
                # إذا كان النسخ الاحتياطي التلقائي معطل، انتظر ساعة ثم تحقق مرة أخرى
                logger.info("النسخ الاحتياطي التلقائي معطل. سيتم التحقق مرة أخرى بعد ساعة.")
                time.sleep(3600)  # انتظار ساعة
                continue
            
            # حساب وقت النسخ الاحتياطي التالي
            next_backup_time = calculate_next_backup_time()
            now = datetime.now()
            
            # حساب الوقت المتبقي حتى النسخ الاحتياطي التالي
            time_diff = (next_backup_time - now).total_seconds()
            
            logger.info(f"النسخ الاحتياطي التالي في: {next_backup_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"الوقت المتبقي: {time_diff / 3600:.2f} ساعة")
            
            # انتظار حتى وقت النسخ الاحتياطي التالي
            if time_diff > 0:
                # انتظار مع التحقق كل 5 دقائق
                sleep_interval = min(time_diff, 300)  # 5 دقائق كحد أقصى
                time.sleep(sleep_interval)
            else:
                # إنشاء نسخة احتياطية
                logger.info("بدء إنشاء النسخة الاحتياطية التلقائية...")
                success, message, backup_path = create_backup()
                
                if success:
                    logger.info(f"تم إنشاء النسخة الاحتياطية التلقائية بنجاح: {backup_path}")
                    
                    # حذف النسخ الاحتياطية القديمة
                    deleted_count, delete_message = delete_old_backups()
                    logger.info(delete_message)
                else:
                    logger.error(f"فشل إنشاء النسخة الاحتياطية التلقائية: {message}")
                
                # انتظار دقيقة قبل حساب وقت النسخ الاحتياطي التالي
                time.sleep(60)
                
        except Exception as e:
            logger.error(f"خطأ في جدولة النسخ الاحتياطي: {str(e)}")
            # انتظار 10 دقائق قبل المحاولة مرة أخرى
            time.sleep(600)

def start_backup_scheduler():
    """
    بدء جدولة النسخ الاحتياطي في خيط منفصل
    """
    # التحقق من النسخ الاحتياطي اليومي عند بدء التشغيل
    check_daily_backup()
    
    # بدء خيط جدولة النسخ الاحتياطي
    scheduler_thread = threading.Thread(target=backup_scheduler, daemon=True)
    scheduler_thread.start()
    logger.info("تم بدء جدولة النسخ الاحتياطي التلقائي")
    
    return scheduler_thread

if __name__ == "__main__":
    # يمكن تشغيل هذا الملف مباشرة لاختبار النسخ الاحتياطي
    print("بدء اختبار النسخ الاحتياطي التلقائي...")
    thread = start_backup_scheduler()
    
    try:
        # الانتظار حتى يتم إيقاف البرنامج يدوياً
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("تم إيقاف اختبار النسخ الاحتياطي التلقائي.")
