"""
فحص هيكل قاعدة البيانات MySQL الجديدة
"""

import pymysql
from utils.config import load_config

def check_mysql_schema():
    """فحص هيكل قاعدة البيانات MySQL الجديدة"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()
        
        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
        
        # الحصول على قائمة الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print("جداول قاعدة البيانات MySQL:")
        for table in tables:
            table_name = table[0]
            print(f"\nجدول: {table_name}")
            
            # الحصول على هيكل الجدول
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            print("الأعمدة:")
            for column in columns:
                # column: (Field, Type, Null, Key, Default, Extra)
                print(f"  {column[0]} ({column[1]}), NULL: {column[2]}, KEY: {column[3]}, DEFAULT: {column[4]}, EXTRA: {column[5]}")
            
            # الحصول على عدد الصفوف في الجدول
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            print(f"عدد الصفوف: {row_count}")
            
            # إذا كان الجدول يحتوي على بيانات، عرض أول صف كمثال
            if row_count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                row = cursor.fetchone()
                print(f"مثال للبيانات: {row}")
        
        # إغلاق الاتصال
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في فحص هيكل قاعدة البيانات MySQL: {str(e)}")
        return False

if __name__ == "__main__":
    check_mysql_schema()
