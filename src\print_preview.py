from PyQt5.QtWidgets import QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox, QMessageBox
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtGui import QTextDocument, QPixmap, QFont, QTextCursor, QTextTableFormat, QTextCharFormat
from PyQt5.QtCore import Qt, QDateTime, QSizeF, QUrl, QDir
import os
import traceback

class PrintPreviewManager:
    """
    فئة لإدارة معاينة الطباعة في التطبيق
    توفر واجهة موحدة لإنشاء وعرض معاينات الطباعة لمختلف أنواع المستندات
    """
    
    def __init__(self, parent=None):
        """
        تهيئة مدير معاينة الطباعة
        
        المعلمات:
            parent: النافذة الأم التي سيتم عرض نافذة المعاينة فيها
        """
        self.parent = parent
        self.setup_printer()
        
    def setup_printer(self):
        """
        إعداد الطابعة بالإعدادات الافتراضية
        """
        self.printer = QPrinter(QPrinter.HighResolution)
        self.printer.setPageSize(QPrinter.A4)
        self.printer.setOrientation(QPrinter.Portrait)
        self.printer.setPageMargins(2, 2, 2, 2, QPrinter.Millimeter)
    
    def show_preview(self, html_content, title="معاينة الطباعة"):
        """
        عرض معاينة الطباعة لمحتوى HTML
        
        المعلمات:
            html_content: محتوى HTML للطباعة
            title: عنوان نافذة المعاينة
        
        العائد:
            bool: True إذا تمت المعاينة بنجاح، False إذا حدث خطأ
        """
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setHtml(html_content)
            
            # إعداد نافذة معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(self.printer, self.parent)
            preview_dialog.setWindowTitle(title)
            preview_dialog.setMinimumSize(900, 600)
            
            # ربط حدث الطباعة بالمستند
            preview_dialog.paintRequested.connect(lambda p: document.print_(p))
            
            # عرض نافذة المعاينة
            preview_dialog.exec_()
            
            return True
        except Exception as e:
            error_msg = f"خطأ في عرض معاينة الطباعة: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            
            if self.parent:
                QMessageBox.critical(self.parent, "خطأ", error_msg)
            
            return False
    
    def print_delegate_orders(self, orders, delegate_name, logo_path=None):
        """
        طباعة قائمة طلبات المندوب
        
        المعلمات:
            orders: قائمة الطلبات (كل طلب يحتوي على: معرف، رقم الوصل، الهاتف، الهاتف2، العنوان، الملاحظات، المبلغ)
            delegate_name: اسم المندوب
            logo_path: مسار شعار الشركة (اختياري)
        
        العائد:
            bool: True إذا تمت المعاينة بنجاح، False إذا حدث خطأ
        """
        try:
            # التحقق من وجود طلبات
            if not orders:
                if self.parent:
                    QMessageBox.information(self.parent, "تنبيه", "لا توجد طلبات للطباعة")
                return False
            
            # إنشاء محتوى HTML
            html_content = self.create_delegate_orders_html(orders, delegate_name, logo_path)
            
            # عرض معاينة الطباعة
            return self.show_preview(html_content, f"معاينة طباعة طلبات المندوب: {delegate_name}")
        
        except Exception as e:
            error_msg = f"خطأ في طباعة طلبات المندوب: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            
            if self.parent:
                QMessageBox.critical(self.parent, "خطأ", error_msg)
            
            return False
    
    def create_delegate_orders_html(self, orders, delegate_name, logo_path=None):
        """
        إنشاء محتوى HTML لطباعة قائمة طلبات المندوب
        
        المعلمات:
            orders: قائمة الطلبات
            delegate_name: اسم المندوب
            logo_path: مسار شعار الشركة (اختياري)
        
        العائد:
            str: محتوى HTML
        """
        # استخدام الشعار الافتراضي إذا لم يتم تحديد شعار
        if not logo_path:
            logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icons", "al-waleed tower1.png")
        
        # تحميل الصورة وتغيير حجمها باستخدام QPixmap
        logo_pixmap = QPixmap(logo_path)
        # تغيير حجم الصورة إلى 100x100 بكسل مع الحفاظ على النسبة
        logo_pixmap = logo_pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        # حفظ الصورة المعدلة مؤقتًا
        temp_logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icons", "temp_logo.png")
        logo_pixmap.save(temp_logo_path, "PNG")
        
        # استخدام المسار المؤقت للصورة المعدلة
        logo_path = temp_logo_path.replace("\\", "/")  # تحويل المسار لتنسيق HTML
        
        # التاريخ الحالي
        current_date = QDateTime.currentDateTime().toString("yyyy-MM-dd")
        
        # بداية مستند HTML
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 0.3cm; }}
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 15px; }}
                .logo {{ width: 100px; height: auto; display: block; margin: 0 auto 5px auto; object-fit: contain; }}
                .company-name {{ font-size: 20pt; font-weight: bold; margin: 5px 0; color: #2c3e50; }}
                .title {{ font-size: 16pt; font-weight: bold; margin: 3px 0; }}
                .date {{ font-size: 11pt; margin-bottom: 10px; text-align: left; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th {{ background-color: #3498db; color: white; font-weight: bold; padding: 8px; text-align: center; border: 1px solid #ddd; }}
                td {{ padding: 8px; text-align: center; border: 1px solid #ddd; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .phone {{ color: #e74c3c; font-weight: bold; font-size: 12pt; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 15pt; color: #7f8c8d; }}
                .sequence-number {{ font-size: 9pt; }}
                .receipt-number {{ font-size: 9pt; }}
                .price {{ color: #27ae60; font-weight: bold; }}
                .total-price {{ color: #000000; font-weight: bold; }}
                .total-row {{ background-color: #d5f5e3; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <img src="{logo_path}" class="logo" alt="شعار">
                <div class="company-name">شركة الوليد لغسيل السجاد</div>
                <h1 class="title">قائمة طلبات المندوب: {delegate_name}</h1>
                <div class="date">التاريخ: &nbsp;&nbsp;{current_date}</div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th width="30%">الملاحظات</th>
                        <th width="13%">المبلغ</th>
                        <th width="19%">الهاتف</th>
                        <th width="20%">العنوان</th>
                        <th width="12%">رقم الوصل</th>
                        <th width="6%">ت</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # ترتيب الطلبات حسب العنوان أبجدياً
        sorted_orders = sorted(orders, key=lambda order: order[4] or '') # ترتيب حسب العنوان (الفهرس 4)
        
        # إجمالي المبلغ
        total_price = 0
        
        # إضافة صفوف الطلبات
        for index, order in enumerate(sorted_orders, 1):
            order_id, receipt_number, phone, phone2, address, total, notes = order
            
            # تحديث إجمالي المبلغ
            total_price += float(total) if total else 0
            
            # تنسيق المبلغ
            formatted_total = f"{float(total):,.0f}" if total else "0"
            
            # تنسيق أرقام الهاتف
            phone_html = f'<span class="phone">{phone}</span>'
            if phone2:
                phone_html += f'<br><span class="phone">{phone2}</span>'
            
            # تنسيق الملاحظات
            notes_html = notes if notes else ''
            
            # إضافة صف الطلب
            html += f"""
                <tr>
                    <td>{notes_html}</td>
                    <td><span class="price">{formatted_total}</span></td>
                    <td>{phone_html}</td>
                    <td>{address if address else ''}</td>
                    <td><span class="receipt-number">{receipt_number if receipt_number else ''}</span></td>
                    <td><span class="sequence-number">{index}</span></td>
                </tr>
            """
        
        # تنسيق إجمالي المبلغ
        formatted_total_price = f"{total_price:,.0f}"
        
        # إضافة صف الإجمالي
        html += f"""
                <tr class="total-row">
                    <td>الإجمالي</td>
                    <td><span class="total-price">{formatted_total_price}</span></td>
                    <td colspan="4"></td>
                </tr>
                </tbody>
            </table>
            
            <div class="footer">
                شركة الوليد لغسيل السجاد - جميع الحقوق محفوظة
            </div>
        </body>
        </html>
        """
        
        return html
    
    def print_completed_orders(self, orders, title="الطلبات المكتملة"):
        """
        طباعة قائمة الطلبات المكتملة
        
        المعلمات:
            orders: قائمة الطلبات المكتملة
            title: عنوان المستند
        
        العائد:
            bool: True إذا تمت المعاينة بنجاح، False إذا حدث خطأ
        """
        try:
            # التحقق من وجود طلبات
            if not orders:
                if self.parent:
                    QMessageBox.information(self.parent, "تنبيه", "لا توجد طلبات للطباعة")
                return False
            
            # إنشاء محتوى HTML
            html_content = self.create_completed_orders_html(orders, title)
            
            # عرض معاينة الطباعة
            return self.show_preview(html_content, f"معاينة طباعة {title}")
        
        except Exception as e:
            error_msg = f"خطأ في طباعة الطلبات المكتملة: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            
            if self.parent:
                QMessageBox.critical(self.parent, "خطأ", error_msg)
            
            return False
    
    def create_completed_orders_html(self, orders, title):
        """
        إنشاء محتوى HTML لطباعة قائمة الطلبات المكتملة
        
        المعلمات:
            orders: قائمة الطلبات المكتملة
            title: عنوان المستند
        
        العائد:
            str: محتوى HTML
        """
        # مسار الشعار
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icons", "al-waleed tower1.png")
        logo_path = logo_path.replace("\\", "/")  # تحويل المسار لتنسيق HTML
        
        # التاريخ الحالي
        current_date = QDateTime.currentDateTime().toString("yyyy-MM-dd")
        
        # بداية مستند HTML
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 0.5cm; }}
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 15px; }}
                .logo {{ width: 100px; height: auto; display: block; margin: 0 auto 5px auto; }}
                .company-name {{ font-size: 20pt; font-weight: bold; margin: 5px 0; color: #2c3e50; }}
                .title {{ font-size: 16pt; font-weight: bold; margin: 3px 0; }}
                .date {{ font-size: 12pt; margin-bottom: 10px; text-align: left; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th {{ background-color: #3498db; color: white; font-weight: bold; padding: 8px; text-align: center; border: 1px solid #ddd; }}
                td {{ padding: 8px; text-align: center; border: 1px solid #ddd; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .total-row {{ background-color: #d5f5e3; font-weight: bold; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 15pt; color: #7f8c8d; }}
                .sequence-number {{ font-size: 8pt; }}
                .receipt-number {{ font-size: 8pt; }}
            </style>
        </head>
        <body>
            <div class="header">
                <img src="{logo_path}" class="logo" alt="شعار">
                <div class="company-name">شركة الوليد لغسيل السجاد</div>
                <h1 class="title">{title}</h1>
                <div class="date">التاريخ: &nbsp;&nbsp;{current_date}</div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>المبلغ</th>
                        <th>عدد البطانية</th>
                        <th>عدد السجاد</th>
                        <th>رقم الوصل</th>
                        <th>التاريخ</th>
                        <th>ت</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # إجماليات
        total_price = 0
        total_carpets = 0
        total_blankets = 0
        
        # إضافة صفوف الطلبات
        for index, order in enumerate(orders, 1):
            # استخراج بيانات الطلب (يجب تعديل الفهارس حسب هيكل البيانات الفعلي)
            order_id = order[0]
            receipt_number = order[1]
            date = order[2] if len(order) > 2 else ""
            carpet_count = order[3] if len(order) > 3 else 0
            blanket_count = order[4] if len(order) > 4 else 0
            total = order[5] if len(order) > 5 else 0
            
            # تحديث الإجماليات
            total_price += float(total) if total else 0
            total_carpets += int(carpet_count) if carpet_count else 0
            total_blankets += int(blanket_count) if blanket_count else 0
            
            # تنسيق المبلغ
            formatted_total = f"{float(total):,.0f}" if total else "0"
            
            # إضافة صف الطلب
            html += f"""
                <tr>
                    <td>{formatted_total}</td>
                    <td>{blanket_count if blanket_count else 0}</td>
                    <td>{carpet_count if carpet_count else 0}</td>
                    <td><span class="receipt-number">{receipt_number if receipt_number else ''}</span></td>
                    <td>{date if date else ''}</td>
                    <td><span class="sequence-number">{index}</span></td>
                </tr>
            """
        
        # تنسيق الإجماليات
        formatted_total_price = f"{total_price:,.0f}"
        
        # إضافة صف الإجماليات
        html += f"""
                <tr class="total-row">
                    <td>{formatted_total_price}</td>
                    <td>{total_blankets}</td>
                    <td>{total_carpets}</td>
                    <td colspan="3">الإجمالي</td>
                </tr>
            </tbody>
        </table>
        
        <div class="footer">
            شركة الوليد لغسيل السجاد - جميع الحقوق محفوظة
        </div>
    </body>
    </html>
    """
        
        return html