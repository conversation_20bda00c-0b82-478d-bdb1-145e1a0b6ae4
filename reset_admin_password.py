#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإعادة تعيين كلمة مرور المدير
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from db.models import Database
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {str(e)}")
    sys.exit(1)

def reset_admin_password():
    """إعادة تعيين كلمة مرور المدير"""
    try:
        print("\n" + "=" * 50)
        print("إعادة تعيين كلمة مرور المدير".center(50))
        print("=" * 50)
        
        # الاتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # التحقق من نوع قاعدة البيانات
        db_type = getattr(db, 'db_type', 'sqlite')
        print(f"نوع قاعدة البيانات: {db_type}")
        
        # التحقق من وجود المستخدم admin
        if db_type == 'mysql':
            cursor.execute("SELECT id, username, password FROM users WHERE username = %s", ('admin',))
        else:
            cursor.execute("SELECT id, username, password FROM users WHERE username = ?", ('admin',))
        
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"تم العثور على المستخدم admin (ID: {admin_user[0]})")
            print(f"كلمة المرور الحالية: {admin_user[2]}")
            
            # تحديث كلمة المرور
            new_password = "admin123"
            if db_type == 'mysql':
                cursor.execute("UPDATE users SET password = %s WHERE username = %s", (new_password, 'admin'))
            else:
                cursor.execute("UPDATE users SET password = ? WHERE username = ?", (new_password, 'admin'))
            
            conn.commit()
            print(f"✓ تم تحديث كلمة مرور المدير إلى: {new_password}")
            
        else:
            print("لم يتم العثور على المستخدم admin")
            print("سيتم إنشاء المستخدم admin...")
            
            # إنشاء المستخدم admin
            if db_type == 'mysql':
                cursor.execute("""
                    INSERT INTO users (username, password, role) 
                    VALUES (%s, %s, %s)
                """, ('admin', 'admin123', 'admin'))
            else:
                cursor.execute("""
                    INSERT INTO users (username, password, role) 
                    VALUES (?, ?, ?)
                """, ('admin', 'admin123', 'admin'))
            
            conn.commit()
            print("✓ تم إنشاء المستخدم admin بكلمة المرور: admin123")
        
        # التحقق من جميع المستخدمين
        cursor.execute("SELECT id, username, password, role FROM users")
        users = cursor.fetchall()
        
        print(f"\nجميع المستخدمين في قاعدة البيانات ({len(users)}):")
        for user in users:
            role = user[3] if len(user) > 3 and user[3] else 'user'
            print(f"  - ID: {user[0]}, المستخدم: {user[1]}, كلمة المرور: {user[2]}, الدور: {role}")
        
        # إغلاق الاتصال
        conn.close()
        
        print("\n" + "=" * 50)
        print("تم إعادة تعيين كلمة المرور بنجاح".center(50))
        print("=" * 50)
        print("\nيمكنك الآن تسجيل الدخول باستخدام:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("=" * 50 + "\n")
        
    except Exception as e:
        print(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    reset_admin_password()
