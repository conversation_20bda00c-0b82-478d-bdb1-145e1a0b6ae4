import os
import sys
import logging
from datetime import datetime

# إضافة المجلد الرئيسي إلى مسار البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.backup import create_backup
from utils.config import load_config

# إعداد التسجيل
if not os.path.exists('logs'):
    os.makedirs('logs')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'exit_backup_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('exit_backup')

def perform_exit_backup():
    """
    إنشاء نسخة احتياطية عند إغلاق البرنامج
    
    Returns:
        bool: نجاح العملية
    """
    try:
        logger.info("بدء إنشاء نسخة احتياطية عند الخروج...")
        
        # تحميل الإعدادات
        config = load_config()
        
        # التحقق مما إذا كان النسخ الاحتياطي عند الخروج مفعل
        if not config.get('exit_backup', True):
            logger.info("النسخ الاحتياطي عند الخروج معطل")
            return False
        
        # إنشاء مجلد خاص للنسخ الاحتياطية عند الخروج
        exit_backup_dir = os.path.join(config['backup_dir'], 'exit_backups')
        if not os.path.exists(exit_backup_dir):
            os.makedirs(exit_backup_dir)
        
        # إنشاء نسخة احتياطية
        success, message, backup_path = create_backup(custom_path=exit_backup_dir)
        
        if success:
            logger.info(f"تم إنشاء نسخة احتياطية عند الخروج بنجاح: {backup_path}")
            return True
        else:
            logger.error(f"فشل إنشاء نسخة احتياطية عند الخروج: {message}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء نسخة احتياطية عند الخروج: {str(e)}")
        return False

def cleanup_old_exit_backups(keep_count=5):
    """
    حذف النسخ الاحتياطية القديمة عند الخروج
    
    Args:
        keep_count (int): عدد النسخ الاحتياطية للاحتفاظ بها
    
    Returns:
        int: عدد الملفات المحذوفة
    """
    try:
        # تحميل الإعدادات
        config = load_config()
        
        # مجلد النسخ الاحتياطية عند الخروج
        exit_backup_dir = os.path.join(config['backup_dir'], 'exit_backups')
        
        if not os.path.exists(exit_backup_dir):
            return 0
        
        # الحصول على قائمة النسخ الاحتياطية
        backups = []
        for file in os.listdir(exit_backup_dir):
            if file.startswith("carpet_cleaning_backup_") and file.endswith(".db"):
                backup_path = os.path.join(exit_backup_dir, file)
                # الحصول على تاريخ إنشاء الملف
                creation_time = os.path.getctime(backup_path)
                backups.append({
                    'path': backup_path,
                    'creation_time': creation_time
                })
        
        # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['creation_time'], reverse=True)
        
        # حذف النسخ الاحتياطية القديمة
        deleted_count = 0
        if len(backups) > keep_count:
            for backup in backups[keep_count:]:
                os.remove(backup['path'])
                deleted_count += 1
                logger.info(f"تم حذف نسخة احتياطية قديمة عند الخروج: {backup['path']}")
        
        return deleted_count
        
    except Exception as e:
        logger.error(f"خطأ في حذف النسخ الاحتياطية القديمة عند الخروج: {str(e)}")
        return 0

def handle_application_exit():
    """
    معالجة إغلاق التطبيق
    - إنشاء نسخة احتياطية
    - تنظيف النسخ الاحتياطية القديمة
    """
    try:
        # إنشاء نسخة احتياطية
        perform_exit_backup()
        
        # تنظيف النسخ الاحتياطية القديمة
        deleted_count = cleanup_old_exit_backups()
        if deleted_count > 0:
            logger.info(f"تم حذف {deleted_count} نسخة احتياطية قديمة عند الخروج")
            
    except Exception as e:
        logger.error(f"خطأ في معالجة إغلاق التطبيق: {str(e)}")

if __name__ == "__main__":
    # يمكن تشغيل هذا الملف مباشرة لاختبار النسخ الاحتياطي عند الخروج
    print("اختبار النسخ الاحتياطي عند الخروج...")
    success = perform_exit_backup()
    if success:
        print("تم إنشاء نسخة احتياطية بنجاح")
    else:
        print("فشل إنشاء نسخة احتياطية")
