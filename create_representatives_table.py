"""
إنشاء جدول المندوبين في قاعدة بيانات MySQL
"""

import pymysql
from utils.config import load_config

def create_representatives_table():
    """إنشاء جدول المندوبين في قاعدة بيانات MySQL"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()
        
        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
        
        # التحقق من وجود جدول المندوبين
        cursor.execute("SHOW TABLES LIKE 'representatives'")
        if cursor.fetchone():
            print("جدول المندوبين موجود بالفعل")
        else:
            print("جدول المندوبين غير موجود، جاري إنشاؤه...")
            
            # إنشاء جدول المندوبين
            cursor.execute("""
                CREATE TABLE representatives (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    phone VARCHAR(20),
                    address VARCHAR(255),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """)
            
            print("تم إنشاء جدول المندوبين بنجاح")
            
            # إضافة بعض المندوبين الافتراضيين
            cursor.execute("""
                INSERT INTO representatives (name, phone, address, notes)
                VALUES 
                ('مندوب 1', '07700000001', 'بغداد', 'مندوب افتراضي 1'),
                ('مندوب 2', '07700000002', 'بغداد', 'مندوب افتراضي 2'),
                ('مندوب 3', '07700000003', 'بغداد', 'مندوب افتراضي 3');
            """)
            
            print("تم إضافة المندوبين الافتراضيين بنجاح")
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في إنشاء جدول المندوبين: {str(e)}")
        return False

if __name__ == "__main__":
    create_representatives_table()
