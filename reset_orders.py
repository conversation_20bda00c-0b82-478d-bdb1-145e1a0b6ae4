#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لحذف جميع الطلبات من قاعدة البيانات
هذا السكريبت سيحذف جميع بيانات الطلبات بشكل نهائي
يرجى التأكد من أخذ نسخة احتياطية قبل تنفيذ هذا السكريبت
"""

import os
import sys
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QLabel, QPushButton, QCheckBox, QHBoxLayout
from PyQt5.QtCore import Qt

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from db.models import Database
    from utils.backup import create_backup
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {str(e)}")
    sys.exit(1)

class ConfirmationDialog(QDialog):
    """نافذة تأكيد حذف جميع الطلبات"""
    def __init__(self, parent=None):
        super(ConfirmationDialog, self).__init__(parent)
        self.setWindowTitle("تأكيد حذف جميع الطلبات")
        self.setMinimumWidth(500)
        self.setMinimumHeight(300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تخطيط النافذة
        layout = QVBoxLayout(self)
        
        # رسالة تحذير
        warning_label = QLabel("""
        <h2 style="color: red; text-align: center;">تحذير هام</h2>
        <p style="font-size: 14pt;">أنت على وشك حذف <b>جميع الطلبات</b> من قاعدة البيانات.</p>
        <p style="font-size: 14pt;">هذه العملية <b>لا يمكن التراجع عنها</b> وستؤدي إلى فقدان جميع بيانات الطلبات بشكل نهائي.</p>
        <p style="font-size: 14pt;">يرجى التأكد من أخذ نسخة احتياطية قبل المتابعة.</p>
        """)
        warning_label.setTextFormat(Qt.RichText)
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)
        
        # خيار أخذ نسخة احتياطية
        self.backup_checkbox = QCheckBox("إنشاء نسخة احتياطية قبل الحذف (موصى به)")
        self.backup_checkbox.setChecked(True)
        layout.addWidget(self.backup_checkbox)
        
        # أزرار التأكيد والإلغاء
        button_layout = QHBoxLayout()
        
        self.confirm_button = QPushButton("تأكيد الحذف")
        self.confirm_button.setStyleSheet("background-color: #d9534f; color: white; font-weight: bold; padding: 10px;")
        self.confirm_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet("padding: 10px;")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.confirm_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)

def reset_orders():
    """حذف جميع الطلبات من قاعدة البيانات"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # عرض نافذة التأكيد
    dialog = ConfirmationDialog()
    result = dialog.exec_()
    
    if result == QDialog.Accepted:
        try:
            # إنشاء نسخة احتياطية إذا تم اختيار ذلك
            if dialog.backup_checkbox.isChecked():
                print("جاري إنشاء نسخة احتياطية...")
                success, message, backup_path = create_backup()
                if success:
                    print(f"تم إنشاء نسخة احتياطية بنجاح: {backup_path}")
                else:
                    print(f"فشل في إنشاء نسخة احتياطية: {message}")
                    # عرض رسالة تحذير
                    msg_box = QMessageBox()
                    msg_box.setIcon(QMessageBox.Warning)
                    msg_box.setWindowTitle("تحذير")
                    msg_box.setText(f"فشل في إنشاء نسخة احتياطية: {message}\nهل ترغب في المتابعة بدون نسخة احتياطية؟")
                    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                    msg_box.setDefaultButton(QMessageBox.No)
                    if msg_box.exec_() == QMessageBox.No:
                        print("تم إلغاء العملية")
                        return
            
            # الاتصال بقاعدة البيانات
            db = Database()
            conn = db.get_connection()
            cursor = conn.cursor()
            
            # حذف جميع السجلات من جدول أبعاد السجاد
            print("جاري حذف جميع السجلات من جدول أبعاد السجاد...")
            cursor.execute("DELETE FROM carpet_dimensions")
            
            # حذف جميع السجلات من جدول طلبات المندوبين
            print("جاري حذف جميع السجلات من جدول طلبات المندوبين...")
            cursor.execute("DELETE FROM delegate_orders")
            
            # حذف جميع السجلات من جدول الطلبات
            print("جاري حذف جميع السجلات من جدول الطلبات...")
            cursor.execute("DELETE FROM orders")
            
            # إعادة تعيين قيمة المعرف التلقائي
            if hasattr(db, 'db_type') and db.db_type == 'mysql':
                print("جاري إعادة تعيين قيمة المعرف التلقائي (MySQL)...")
                cursor.execute("ALTER TABLE orders AUTO_INCREMENT = 1")
                cursor.execute("ALTER TABLE carpet_dimensions AUTO_INCREMENT = 1")
                cursor.execute("ALTER TABLE delegate_orders AUTO_INCREMENT = 1")
            else:
                print("جاري إعادة تعيين قيمة المعرف التلقائي (SQLite)...")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='orders'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='carpet_dimensions'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='delegate_orders'")
            
            # حفظ التغييرات
            conn.commit()
            
            # إغلاق الاتصال
            conn.close()
            
            # عرض رسالة نجاح
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("تم بنجاح")
            msg_box.setText("تم حذف جميع الطلبات من قاعدة البيانات بنجاح")
            msg_box.exec_()
            
            print("تم حذف جميع الطلبات من قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"خطأ في حذف الطلبات: {str(e)}")
            traceback.print_exc()
            
            # عرض رسالة خطأ
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("خطأ")
            msg_box.setText(f"حدث خطأ أثناء حذف الطلبات: {str(e)}")
            msg_box.exec_()
    else:
        print("تم إلغاء العملية")

if __name__ == "__main__":
    reset_orders()
