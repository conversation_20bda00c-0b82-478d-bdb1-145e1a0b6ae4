#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أداة لتحديث هيكل المشروع ونقل الملفات إلى المجلدات المناسبة
"""

import os
import shutil
import sys

def create_directories(root_dir):
    """إنشاء المجلدات الضرورية"""
    directories = ['src', 'ui', 'db', 'utils', 'scripts']
    for directory in directories:
        dir_path = os.path.join(root_dir, directory)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"تم إنشاء المجلد: {directory}")
            # إنشاء ملف __init__.py
            with open(os.path.join(dir_path, '__init__.py'), 'w', encoding='utf-8') as f:
                f.write(f'"""\nحزمة {directory} لبرنامج غسيل السجاد\n"""')

def move_files(root_dir):
    """نقل الملفات إلى المجلدات المناسبة"""
    # تصنيف الملفات
    file_categories = {
        'db': ['models.py', 'check_schema.py'],
        'ui': ['styles.py', 'delegate_styles.py', 'forms.py', 'login.py', 
               'edit_order_dialog.py', 'delegate_dialog.py', 'improved_carpet_dialog.py'],
        'utils': ['config.py', 'printing.py', 'notifications.py', 'verify_receipts.py'],
        'scripts': ['add_incomplete_orders.py', 'add_warehouse_orders.py', 'clean_database.py',
                   'check_expenses.py', 'temp_db_check.py', 'transfer_more_orders.py'],
        'src': ['main.py', 'orders.py', 'warehouse.py', 'delegate_orders_new.py',
               'delegate_distribution.py', 'completed_orders.py', 'settings.py',
               'accounts.py', 'users.py', 'carpet_dimensions.py', 'reports.py',
               'order_verification.py', 'new_order.py', 'reorder_table.py']
    }
    
    # نقل الملفات
    for category, files in file_categories.items():
        for file_name in files:
            source = os.path.join(root_dir, file_name)
            destination = os.path.join(root_dir, category, file_name)
            
            if os.path.exists(source) and not os.path.exists(destination):
                try:
                    shutil.copy2(source, destination)
                    print(f"تم نسخ الملف: {file_name} إلى {category}/")
                except Exception as e:
                    print(f"خطأ في نسخ الملف {file_name}: {str(e)}")

def create_run_file(root_dir):
    """إنشاء ملف run.py"""
    run_file_content = """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
برنامج غسيل السجاد - نقطة الدخول الرئيسية
\"\"\"

import sys
import os
import shutil

# إضافة المجلدات إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# التحقق من وجود المجلدات الضرورية
required_dirs = ['src', 'ui', 'db', 'utils', 'scripts']
for dir_name in required_dirs:
    dir_path = os.path.join(current_dir, dir_name)
    if not os.path.exists(dir_path):
        print(f"تحذير: المجلد {dir_name} غير موجود. جاري إنشاؤه...")
        os.makedirs(dir_path)

# التحقق من وجود الملفات الضرورية
required_files = {
    'src/main.py': 'الملف الرئيسي للبرنامج',
    'db/models.py': 'ملف نماذج قاعدة البيانات',
    'utils/config.py': 'ملف الإعدادات',
}

for file_path, description in required_files.items():
    full_path = os.path.join(current_dir, file_path)
    if not os.path.exists(full_path):
        print(f"خطأ: {description} غير موجود في {file_path}")
        sys.exit(1)

# استيراد الملف الرئيسي
try:
    from src.main import main
    main()
except ImportError as e:
    print(f"خطأ في استيراد الملف الرئيسي: {str(e)}")
    print("تأكد من وجود جميع الملفات في المجلدات الصحيحة.")
    sys.exit(1)
except Exception as e:
    print(f"خطأ في تشغيل البرنامج: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    run_file_path = os.path.join(root_dir, 'run.py')
    with open(run_file_path, 'w', encoding='utf-8') as f:
        f.write(run_file_content)
    print("تم إنشاء ملف run.py")

def create_readme(root_dir):
    """إنشاء ملف README.md"""
    readme_content = """# برنامج إدارة غسيل السجاد

نظام متكامل لإدارة عمليات غسيل السجاد من استلام الطلبات وحتى تسليمها للعملاء.

## الميزات الرئيسية

- إدارة دورة حياة الطلبات (الاستلام، التنظيف، التسليم)
- إدارة المندوبين وتوزيع الطلبات
- تتبع أبعاد السجاد وتفاصيل التنظيف
- إدارة الحسابات والمدفوعات
- تقارير متنوعة
- دعم كامل للغة العربية

## هيكل المشروع

- `src/`: الملفات الأساسية للبرنامج
- `ui/`: ملفات واجهة المستخدم
- `db/`: ملفات قاعدة البيانات
- `utils/`: أدوات مساعدة
- `scripts/`: نصوص برمجية مساعدة

## متطلبات النظام

- Python 3.x
- PyQt5
- SQLAlchemy
- دعم اللغة العربية

## طريقة التشغيل

```
python run.py
```

## الوثائق

- `README.md`: الملف الحالي
- `README_TOOLS.md`: توثيق الأدوات المساعدة

## الترخيص

جميع الحقوق محفوظة © 2025
"""
    
    readme_path = os.path.join(root_dir, 'README.md')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("تم إنشاء ملف README.md")

def main():
    """الدالة الرئيسية"""
    root_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(root_dir)  # الانتقال إلى المجلد الأعلى
    
    print("جاري تحديث هيكل المشروع...")
    
    # إنشاء المجلدات
    create_directories(root_dir)
    
    # نقل الملفات
    move_files(root_dir)
    
    # إنشاء ملف run.py
    create_run_file(root_dir)
    
    # إنشاء ملف README.md
    create_readme(root_dir)
    
    print("تم تحديث هيكل المشروع بنجاح!")

if __name__ == "__main__":
    main()
