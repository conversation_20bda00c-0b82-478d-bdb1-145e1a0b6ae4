#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لاختبار دالة authenticate الجديدة
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_authenticate():
    """اختبار دالة authenticate"""
    try:
        print("\n" + "=" * 50)
        print("اختبار دالة authenticate".center(50))
        print("=" * 50)
        
        # استيراد وحدة قاعدة البيانات
        from db.models import Database
        
        # إنشاء اتصال بقاعدة البيانات
        db = Database()
        
        print(f"✓ تم إنشاء كائن Database")
        print(f"نوع قاعدة البيانات: {db.db_type}")
        
        # اختبار المستخدمين المختلفين
        test_users = [
            ("admin", "admin123"),
            ("زياد", "9999"),
            ("احمد", "2222"),
            ("admin", "wrong_password"),  # كلمة مرور خاطئة
            ("nonexistent", "password")   # مستخدم غير موجود
        ]
        
        for username, password in test_users:
            print(f"\n--- اختبار المستخدم: {username} ---")
            
            try:
                user = db.authenticate(username, password)
                
                if user:
                    print(f"✓ تسجيل الدخول نجح")
                    print(f"  ID: {user['id']}")
                    print(f"  اسم المستخدم: {user['username']}")
                    print(f"  الدور: {user['role']}")
                else:
                    print(f"✗ فشل تسجيل الدخول")
                    
            except Exception as e:
                print(f"✗ خطأ في تسجيل الدخول: {str(e)}")
                traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("اكتمل اختبار دالة authenticate".center(50))
        print("=" * 50)
        
    except Exception as e:
        print(f"خطأ في اختبار دالة authenticate: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_authenticate()
