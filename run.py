#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
برنامج غسيل السجاد - نقطة الدخول الرئيسية
"""

import sys
import os
import shutil
import atexit
import traceback
import logging
import faulthandler

# تفعيل تتبع الأخطاء على مستوى النظام
faulthandler.enable()

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/program.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# إضافة المجلدات إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def handle_exception(exc_type, exc_value, exc_traceback):
    """معالج الاستثناءات غير المعالجة"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.critical("حدث خطأ غير متوقع:", exc_info=(exc_type, exc_value, exc_traceback))

# تسجيل معالج الاستثناءات
sys.excepthook = handle_exception

try:
    # تهيئة دعم اللغة العربية قبل أي عمليات طباعة
    from utils.config import setup_arabic_encoding
    setup_arabic_encoding()
    logger.info("تم تهيئة دعم اللغة العربية بنجاح")
except Exception as e:
    logger.error(f"خطأ في تهيئة دعم اللغة العربية: {str(e)}")
    logger.debug(traceback.format_exc())

# التحقق من وجود المجلدات الضرورية
required_dirs = ['src', 'ui', 'db', 'utils', 'scripts', 'logs', 'backups']
for dir_name in required_dirs:
    dir_path = os.path.join(current_dir, dir_name)
    if not os.path.exists(dir_path):
        try:
            logger.info(f"إنشاء مجلد: {dir_name}")
            os.makedirs(dir_path)
        except Exception as e:
            logger.error(f"خطأ في إنشاء المجلد {dir_name}: {str(e)}")
            logger.debug(traceback.format_exc())

# التحقق من وجود الملفات الضرورية
required_files = {
    'src/main.py': 'الملف الرئيسي للبرنامج',
    'db/models.py': 'ملف نماذج قاعدة البيانات',
    'utils/config.py': 'ملف الإعدادات',
}

for file_path, description in required_files.items():
    full_path = os.path.join(current_dir, file_path)
    if not os.path.exists(full_path):
        logger.critical(f"خطأ: {description} غير موجود في {file_path}")
        sys.exit(1)

# تهيئة النسخ الاحتياطي التلقائي
try:
    from utils.backup import setup_auto_backup
    setup_auto_backup()
    logger.info("تم تهيئة النسخ الاحتياطي التلقائي بنجاح")
except Exception as e:
    logger.error(f"خطأ في تهيئة النسخ الاحتياطي التلقائي: {str(e)}")
    logger.debug(traceback.format_exc())

# تشغيل البرنامج الرئيسي
try:
    from src.main import main
    main()
except Exception as e:
    logger.critical("خطأ في تشغيل البرنامج الرئيسي:", exc_info=True)
    sys.exit(1)