#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
برنامج غسيل السجاد - نقطة الدخول للتصحيح وتتبع الأخطاء
"""
import sys
import os
import logging
import faulthandler
import traceback
from datetime import datetime

# تفعيل تتبع الأخطاء على مستوى النظام
faulthandler.enable()

# إنشاء مجلد للتقارير إذا لم يكن موجوداً
debug_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'debug_logs')
if not os.path.exists(debug_dir):
    os.makedirs(debug_dir)

# إعداد ملف تسجيل الأخطاء
log_file = os.path.join(debug_dir, f'debug_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

# إعداد التسجيل المفصل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)
logger.info("بدء تشغيل البرنامج في وضع التصحيح")

# تسجيل معلومات النظام
logger.info(f"إصدار Python: {sys.version}")
logger.info(f"مسار التشغيل: {os.path.abspath(__file__)}")
logger.info(f"المجلد الحالي: {os.getcwd()}")
logger.info(f"متغيرات البيئة ذات الصلة:")
for key in ['PYTHONPATH', 'LANG', 'PYTHONIOENCODING']:
    logger.info(f"{key}: {os.environ.get(key, 'غير محدد')}")

def handle_exception(exc_type, exc_value, exc_traceback):
    """معالج مفصل للاستثناءات غير المعالجة"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    logger.critical("حدث خطأ غير متوقع!", exc_info=(exc_type, exc_value, exc_traceback))
    
    # تسجيل معلومات إضافية عن الخطأ
    logger.debug("معلومات إضافية عن الخطأ:")
    logger.debug(f"نوع الخطأ: {exc_type.__name__}")
    logger.debug(f"وصف الخطأ: {str(exc_value)}")
    logger.debug("تتبع الخطأ:")
    for line in traceback.extract_tb(exc_traceback).format():
        logger.debug(line)

# تسجيل معالج الاستثناءات المفصل
sys.excepthook = handle_exception

try:
    # تهيئة دعم اللغة العربية
    from utils.config import setup_arabic_encoding
    setup_arabic_encoding()
    logger.info("تم تهيئة دعم اللغة العربية بنجاح")
except Exception as e:
    logger.error(f"خطأ في تهيئة دعم اللغة العربية: {str(e)}")
    logger.debug(traceback.format_exc())

try:
    # محاولة استيراد وتشغيل البرنامج الرئيسي
    logger.info("جاري محاولة بدء البرنامج الرئيسي...")
    from src.main import main
    logger.info("تم استيراد الوحدة الرئيسية بنجاح")
    
    # تشغيل البرنامج
    logger.info("بدء تنفيذ البرنامج الرئيسي")
    main()
except ImportError as e:
    logger.critical(f"خطأ في استيراد الوحدة الرئيسية: {str(e)}")
    logger.debug(traceback.format_exc())
    sys.exit(1)
except Exception as e:
    logger.critical(f"خطأ غير متوقع أثناء تشغيل البرنامج: {str(e)}")
    logger.debug(traceback.format_exc())
    sys.exit(1)
finally:
    logger.info("انتهى تنفيذ البرنامج")