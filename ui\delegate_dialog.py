from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QComboBox, QPushButton, QMessageBox, QLineEdit, QDialogButtonBox)
from PyQt5.QtCore import Qt
from db.models import Database
from ui.styles import get_button_style

class AssignRepresentativeDialog(QDialog):
    """نافذة تعيين مندوب للطلب"""
    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.db = Database()
        self.order_id = order_id
        self.setup_ui()
        
    def setup_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعيين مندوب")
        self.setFixedWidth(400)
        
        layout = QVBoxLayout(self)
        
        # قائمة المندوبين
        self.representatives_combo = QComboBox()
        self.representatives_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
            }
        """)
        self.load_representatives()
        layout.addWidget(self.representatives_combo)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(get_button_style('success'))
        save_btn.clicked.connect(self.save)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(get_button_style('secondary'))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
    
    def load_representatives(self):
        """تحميل قائمة المندوبين"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id, name, area FROM representatives ORDER BY name")
                representatives = cursor.fetchall()
                
                self.representatives_combo.clear()
                self.representatives_combo.addItem("اختر مندوب...", None)
                
                for rep in representatives:
                    self.representatives_combo.addItem(f"{rep[1]} - {rep[2]}", rep[0])
                
        except Exception as e:
            print(f"خطأ في تحميل المندوبين: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحميل قائمة المندوبين")
    
    def save(self):
        """حفظ تعيين المندوب"""
        try:
            rep_id = self.representatives_combo.currentData()
            if not rep_id:
                QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مندوب")
                return
            
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                # تحديث الطلب وتعيين المندوب وتغيير الحالة
                cursor.execute("""
                    UPDATE orders 
                    SET pickup_representative_id = ?, 
                        status = 'قيد الاستلام'
                    WHERE id = ?
                """, (rep_id, self.order_id))
                conn.commit()
            
            QMessageBox.information(self, "نجاح", "تم تعيين المندوب بنجاح")
            self.accept()
            
        except Exception as e:
            print(f"خطأ في حفظ تعيين المندوب: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ تعيين المندوب")


class DelegateNameInputDialog(QDialog):
    """نافذة إدخال اسم مندوب للنقل الطلبات"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدخال اسم المندوب")
        self.setMinimumWidth(350)
        self.setLayoutDirection(Qt.RightToLeft)  # ضبط اتجاه الواجهة من اليمين إلى اليسار
        
        layout = QVBoxLayout(self)
        
        # إضافة نص توجيهي
        instruction_label = QLabel("أدخل اسم المندوب الذي يريد نقل الطلبات إليه:")
        instruction_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(instruction_label)
        
        # حقل إدخال اسم المندوب
        self.delegate_name_input = QLineEdit()
        self.delegate_name_input.setPlaceholderText("اسم المندوب")
        self.delegate_name_input.setStyleSheet("padding: 8px; border: 1px solid #dcdde1; border-radius: 5px; font-size: 11pt; margin: 5px 0px;")
        layout.addWidget(self.delegate_name_input)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.Ok).setText("موافق")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
        layout.addWidget(buttons)
    
    def get_delegate_name(self):
        """الحصول على اسم المندوب المدخل"""
        return self.delegate_name_input.text().strip()
