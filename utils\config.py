import sys
import os
import locale
# تم إزالة import sqlite3 - نستخدم MySQL فقط
import json
from datetime import datetime

# ملف الإعدادات
CONFIG_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app_config.json')

# الإعدادات الافتراضية
DEFAULT_CONFIG = {
    'backup_dir': os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backups'),
    'auto_backup': True,
    'backup_time': '00:00',  # وقت النسخ الاحتياطي اليومي
    'last_backup': None,  # آخر نسخة احتياطية
    'check_all_receipts': False,  # التحقق من جميع أرقام الوصل عند بدء التشغيل
    'receipt_check_days': 7,  # عدد الأيام للتحقق من أرقام الوصل الحديثة

    # إعدادات قاعدة البيانات
    'db_type': 'mysql',  # نوع قاعدة البيانات: mysql فقط
    'db_host': 'localhost',  # عنوان خادم MySQL
    'db_port': '3306',  # منفذ خادم MySQL
    'db_name': 'carpet_cleaning',  # اسم قاعدة البيانات
    'db_user': 'carpet_user',  # اسم المستخدم
    'db_password': 'password123'  # كلمة المرور
}

def load_config():
    """تحميل إعدادات البرنامج"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # دمج الإعدادات المحملة مع الإعدادات الافتراضية لضمان وجود جميع المفاتيح
                return {**DEFAULT_CONFIG, **config}
        except Exception as e:
            print(f"خطأ في تحميل ملف الإعدادات: {str(e)}")

    # إنشاء مجلد النسخ الاحتياطية الافتراضي إذا لم يكن موجوداً
    if not os.path.exists(DEFAULT_CONFIG['backup_dir']):
        try:
            os.makedirs(DEFAULT_CONFIG['backup_dir'])
        except Exception as e:
            print(f"خطأ في إنشاء مجلد النسخ الاحتياطية: {str(e)}")

    # حفظ الإعدادات الافتراضية
    save_config(DEFAULT_CONFIG)
    return DEFAULT_CONFIG

def save_config(config):
    """حفظ إعدادات البرنامج"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"خطأ في حفظ ملف الإعدادات: {str(e)}")
        return False

def update_config(key, value):
    """تحديث قيمة في ملف الإعدادات"""
    config = load_config()
    config[key] = value
    return save_config(config)

def setup_arabic_encoding():
    """تهيئة الترميز العربي في النظام"""
    try:
        # تعيين ترميز النظام
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # تعيين ترميز المخرجات القياسية
        if sys.stdout.encoding != 'utf-8':
            sys.stdout = open(sys.stdout.fileno(), mode='w', encoding='utf-8', buffering=1)
        if sys.stderr.encoding != 'utf-8':
            sys.stderr = open(sys.stderr.fileno(), mode='w', encoding='utf-8', buffering=1)

        # محاولة تعيين اللغة العربية
        arabic_locales = [
            'ar_IQ.UTF-8',
            'ar_SA.UTF-8',
            'Arabic.UTF-8',
            'ar_AE.UTF-8',
            'ar_BH.UTF-8',
            'ar_DZ.UTF-8',
            'ar_EG.UTF-8',
            'ar_IN.UTF-8',
            'ar_JO.UTF-8',
            'ar_KW.UTF-8',
            'ar_LB.UTF-8',
            'ar_LY.UTF-8',
            'ar_MA.UTF-8',
            'ar_OM.UTF-8',
            'ar_QA.UTF-8',
            'ar_SD.UTF-8',
            'ar_SY.UTF-8',
            'ar_TN.UTF-8',
            'ar_YE.UTF-8'
        ]

        locale_set = False
        for loc in arabic_locales:
            try:
                locale.setlocale(locale.LC_ALL, loc)
                locale_set = True
                print(f"تم تعيين اللغة العربية باستخدام: {loc}")
                break
            except locale.Error:
                continue

        if not locale_set:
            # إذا فشلت جميع المحاولات، نستخدم الإعدادات الافتراضية
            locale.setlocale(locale.LC_ALL, '')
            print("تحذير: تم استخدام الإعدادات الافتراضية للغة")

        # تم إزالة تهيئة SQLite - نستخدم MySQL فقط

        print("تم تهيئة دعم اللغة العربية بنجاح")

    except Exception as e:
        print(f"تحذير: حدث خطأ أثناء تهيئة الترميز العربي: {str(e)}")
        # استخدام القيم الافتراضية في حالة الفشل
        try:
            locale.setlocale(locale.LC_ALL, '')
        except:
            pass
