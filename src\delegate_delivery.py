#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
قائمة توزيع المندوبين - منفصلة عن قائمة استلام المندوبين
هذه القائمة خاصة بالطلبات الجاهزة للتوزيع من المخزن
الأزرار: مؤجل (إرجاع للمخزن) - مكتمل (نقل للطلبات المكتملة)
"""

import sys
import traceback
import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QGridLayout,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QGroupBox, QDialog, QApplication, QLineEdit, QSizePolicy, QCheckBox)
from PyQt5.QtCore import Qt, QDateTime, QTimer, QObject, QEvent
from PyQt5.QtGui import QFont, QTextDocument, QPixmap, QPainter
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from db.models import Database

# إعداد السجل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# ألوان ثابتة للواجهة
COLORS = {
    'primary': '#3498db',
    'success': '#2ecc71', 
    'warning': '#f39c12',
    'danger': '#e74c3c',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40'
}

# أنماط الجداول
TABLE_STYLE = """
    QTableWidget {
        gridline-color: #bdc3c7;
        background-color: white;
        alternate-background-color: #f8f9fa;
        selection-background-color: #3498db;
        selection-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
    }
    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #ecf0f1;
    }
    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 10px;
        border: none;
        font-weight: bold;
        font-size: 11pt;
    }
"""

def get_button_style(color):
    """إنشاء نمط الأزرار"""
    return f"""
        QPushButton {{
            background-color: {color};
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 12pt;
            font-weight: bold;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {color}dd;
        }}
        QPushButton:pressed {{
            background-color: {color}bb;
        }}
    """

class DeliveryDelegatesWindow(QMainWindow):
    """نافذة قائمة توزيع المندوبين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة توزيع المندوبين")
        self.setGeometry(100, 100, 1400, 800)
        
        # إعداد قاعدة البيانات والسجل
        self.db = Database()
        self.logger = logging.getLogger('delivery_delegates')
        
        # متغيرات الحالة
        self.current_delegate_name = None
        self.current_orders_table = None
        
        self.initUI()
        self.load_delegates()
    
    def initUI(self):
        """تهيئة واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("🚛 قائمة توزيع المندوبين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24pt;
                font-weight: bold;
                color: #2c3e50;
                margin: 20px 0px;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة المندوبين
        self.delegates_group = QGroupBox("المندوبين المتاحين للتوزيع")
        self.delegates_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0px 10px 0px 10px;
                background-color: white;
            }
        """)
        
        # تخطيط أزرار المندوبين
        self.delegates_layout = QGridLayout(self.delegates_group)
        self.delegates_layout.setContentsMargins(15, 25, 15, 15)
        self.delegates_layout.setSpacing(10)
        
        main_layout.addWidget(self.delegates_group)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet(get_button_style(COLORS['danger']))
        close_btn.clicked.connect(self.close)
        
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_btn)
        close_layout.addStretch()
        main_layout.addLayout(close_layout)
    
    def load_delegates(self):
        """تحميل المندوبين الذين لديهم طلبات جاهزة للتوزيع"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # جلب المندوبين الذين لديهم طلبات بحالة 'distribution' (جاهزة للتوزيع)
            cursor.execute("""
                SELECT d.name, COUNT(o.id) as orders_count
                FROM delegates d
                LEFT JOIN orders o ON d.name = o.delegate_name AND o.status = 'distribution'
                GROUP BY d.name
                HAVING COUNT(o.id) > 0
                ORDER BY d.name
            """)
            
            delegates = cursor.fetchall()
            conn.close()
            
            print(f"🚛 المندوبين المتاحين للتوزيع: {len(delegates)}")
            for delegate in delegates:
                print(f"   - {delegate[0]}: {delegate[1]} طلب")
            
            self.display_delegates(delegates)
            
        except Exception as e:
            print(f"خطأ في تحميل مندوبي التوزيع: {str(e)}")
            self.logger.error(f"خطأ في تحميل مندوبي التوزيع: {str(e)}")
    
    def display_delegates(self, delegates):
        """عرض أزرار المندوبين"""
        # مسح الأزرار الحالية
        for i in reversed(range(self.delegates_layout.count())):
            child = self.delegates_layout.itemAt(i).widget()
            if child:
                child.deleteLater()
        
        if not delegates:
            # عرض رسالة عدم وجود مندوبين
            no_delegates_label = QLabel("لا يوجد مندوبين لديهم طلبات جاهزة للتوزيع")
            no_delegates_label.setAlignment(Qt.AlignCenter)
            no_delegates_label.setStyleSheet("""
                QLabel {
                    font-size: 16pt;
                    color: #7f8c8d;
                    margin: 50px;
                    padding: 30px;
                    border: 2px dashed #bdc3c7;
                    border-radius: 10px;
                }
            """)
            self.delegates_layout.addWidget(no_delegates_label, 0, 0, 1, 3)
            return
        
        # إضافة أزرار المندوبين
        for index, (delegate_name, orders_count) in enumerate(delegates):
            btn = QPushButton(f"🚛 {delegate_name}\n({orders_count} طلب)")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 20px;
                    font-size: 14pt;
                    font-weight: bold;
                    min-width: 200px;
                    min-height: 80px;
                }
                QPushButton:hover {
                    background-color: #229954;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """)
            
            btn.clicked.connect(lambda checked, name=delegate_name: self.show_delegate_orders(name))
            
            # ترتيب الأزرار في شبكة (3 أزرار في كل صف)
            row = index // 3
            col = index % 3
            self.delegates_layout.addWidget(btn, row, col)
    
    def show_delegate_orders(self, delegate_name):
        """عرض طلبات المندوب للتوزيع"""
        try:
            self.current_delegate_name = delegate_name
            
            # إنشاء نافذة عرض الطلبات
            orders_dialog = QDialog(self)
            orders_dialog.setWindowTitle(f"طلبات التوزيع - {delegate_name}")
            orders_dialog.setModal(True)
            orders_dialog.resize(1200, 700)
            
            layout = QVBoxLayout(orders_dialog)
            
            # العنوان
            title_label = QLabel(f"🚛 طلبات التوزيع للمندوب: {delegate_name}")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18pt;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 10px;
                    padding: 15px;
                    background-color: #e8f5e8;
                    border-radius: 8px;
                }
            """)
            layout.addWidget(title_label)
            
            # جدول الطلبات
            orders_table = self.create_orders_table(delegate_name)
            layout.addWidget(orders_table)
            
            # أزرار الإغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.setStyleSheet(get_button_style(COLORS['danger']))
            close_btn.clicked.connect(orders_dialog.close)
            
            btn_layout = QHBoxLayout()
            btn_layout.addStretch()
            btn_layout.addWidget(close_btn)
            btn_layout.addStretch()
            layout.addLayout(btn_layout)
            
            orders_dialog.exec_()
            
        except Exception as e:
            print(f"خطأ في عرض طلبات المندوب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض طلبات المندوب: {str(e)}")
    
    def create_orders_table(self, delegate_name):
        """إنشاء جدول طلبات التوزيع"""
        try:
            # جلب طلبات المندوب
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, receipt_number, phone, phone2, address, 
                       carpet_count, blanket_count, total_price, 
                       created_at, notes
                FROM orders 
                WHERE delegate_name = %s AND status = 'distribution'
                ORDER BY created_at DESC
            """, (delegate_name,))
            
            orders = cursor.fetchall()
            conn.close()
            
            # إنشاء الجدول
            table = QTableWidget()
            table.setStyleSheet(TABLE_STYLE)
            
            # تحديد الأعمدة
            columns = [
                "ID", "رقم الوصل", "رقم الهاتف", "العنوان", "عدد السجاد",
                "عدد البطانية", "السعر الإجمالي", "تاريخ الإنشاء", "ملاحظات", 
                "مكتمل", "مؤجل"
            ]
            
            table.setColumnCount(len(columns))
            table.setRowCount(len(orders))
            table.setHorizontalHeaderLabels(columns)
            
            # ملء البيانات
            for row, order in enumerate(orders):
                order_id, receipt_number, phone, phone2, address, carpet_count, blanket_count, total_price, created_at, notes = order
                
                # معرف الطلب
                table.setItem(row, 0, QTableWidgetItem(str(order_id)))
                
                # رقم الوصل
                table.setItem(row, 1, QTableWidgetItem(str(receipt_number or "")))
                
                # رقم الهاتف
                phone_display = phone if phone else (phone2 if phone2 else "")
                table.setItem(row, 2, QTableWidgetItem(phone_display))
                
                # العنوان
                table.setItem(row, 3, QTableWidgetItem(str(address or "")))
                
                # عدد السجاد
                table.setItem(row, 4, QTableWidgetItem(str(carpet_count or 0)))
                
                # عدد البطانية
                table.setItem(row, 5, QTableWidgetItem(str(blanket_count or 0)))
                
                # السعر الإجمالي
                price_formatted = "{:,}".format(int(float(total_price or 0)))
                table.setItem(row, 6, QTableWidgetItem(price_formatted))
                
                # تاريخ الإنشاء
                table.setItem(row, 7, QTableWidgetItem(str(created_at or "")))
                
                # الملاحظات
                table.setItem(row, 8, QTableWidgetItem(str(notes or "")))
                
                # زر مكتمل
                complete_btn = QPushButton("✅ مكتمل")
                complete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px;
                        font-size: 10pt;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                """)
                complete_btn.clicked.connect(lambda checked, oid=order_id: self.complete_order(oid))
                table.setCellWidget(row, 9, complete_btn)
                
                # زر مؤجل
                defer_btn = QPushButton("⏸️ مؤجل")
                defer_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f39c12;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px;
                        font-size: 10pt;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #d35400;
                    }
                """)
                defer_btn.clicked.connect(lambda checked, oid=order_id: self.defer_order(oid))
                table.setCellWidget(row, 10, defer_btn)
            
            # تنسيق الجدول
            table.horizontalHeader().setStretchLastSection(True)
            table.resizeColumnsToContents()
            
            # حفظ مرجع للجدول
            self.current_orders_table = table
            
            return table
            
        except Exception as e:
            print(f"خطأ في إنشاء جدول الطلبات: {str(e)}")
            return QTableWidget()
    
    def complete_order(self, order_id):
        """تحويل الطلب إلى قائمة الطلبات المكتملة"""
        try:
            result = QMessageBox.question(
                self,
                "تأكيد الإكمال",
                "هل أنت متأكد من تحويل الطلب إلى قائمة الطلبات المكتملة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if result == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # تحديث حالة الطلب إلى مكتمل
                cursor.execute("""
                    UPDATE orders
                    SET status = 'completed'
                    WHERE id = %s
                """, (order_id,))
                
                # حذف من جدول delegate_orders إذا كان موجود<|im_start|>
                cursor.execute("DELETE FROM delegate_orders WHERE order_id = %s", (order_id,))
                
                conn.commit()
                conn.close()
                
                # إزالة الصف من الجدول
                self.remove_order_from_table(order_id)
                
                # تحديث قائمة المندوبين
                self.load_delegates()
                
                QMessageBox.information(self, "تم الإكمال", "تم تحويل الطلب إلى قائمة الطلبات المكتملة بنجاح")
                
        except Exception as e:
            print(f"خطأ في إكمال الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إكمال الطلب: {str(e)}")
    
    def defer_order(self, order_id):
        """تأجيل الطلب وإرجاعه إلى المخزن"""
        try:
            result = QMessageBox.question(
                self,
                "تأكيد التأجيل",
                "هل أنت متأكد من تأجيل الطلب وإرجاعه إلى المخزن؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if result == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # تحديث حالة الطلب إلى المخزن
                cursor.execute("""
                    UPDATE orders
                    SET status = 'warehouse',
                        pickup_representative_id = NULL,
                        delegate_name = NULL
                    WHERE id = %s
                """, (order_id,))
                
                # حذف من جدول delegate_orders إذا كان موجود<|im_start|>
                cursor.execute("DELETE FROM delegate_orders WHERE order_id = %s", (order_id,))
                
                conn.commit()
                conn.close()
                
                # إزالة الصف من الجدول
                self.remove_order_from_table(order_id)
                
                # تحديث قائمة المندوبين
                self.load_delegates()
                
                QMessageBox.information(self, "تم التأجيل", "تم تأجيل الطلب وإرجاعه إلى المخزن بنجاح")
                
        except Exception as e:
            print(f"خطأ في تأجيل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تأجيل الطلب: {str(e)}")
    
    def remove_order_from_table(self, order_id):
        """إزالة الطلب من الجدول الحالي"""
        if self.current_orders_table:
            for row in range(self.current_orders_table.rowCount()):
                item = self.current_orders_table.item(row, 0)
                if item and int(item.text()) == order_id:
                    self.current_orders_table.removeRow(row)
                    break

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DeliveryDelegatesWindow()
    window.show()
    sys.exit(app.exec_())
