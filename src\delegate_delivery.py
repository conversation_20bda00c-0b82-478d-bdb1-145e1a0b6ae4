#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
قائمة توزيع المندوبين - منفصلة عن قائمة استلام المندوبين
هذه القائمة خاصة بالطلبات الجاهزة للتوزيع من المخزن
الأزرار: مؤجل (إرجاع للمخزن) - مكتمل (نقل للطلبات المكتملة)
"""

import sys
import traceback
import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QGridLayout,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QGroupBox, QDialog, QApplication, QLineEdit, QSizePolicy, QCheckBox)
from PyQt5.QtCore import Qt, QDateTime, QTimer, QObject, QEvent
from PyQt5.QtGui import QFont, QTextDocument, QPixmap, QPainter
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from db.models import Database

# إعداد السجل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# ألوان ثابتة للواجهة
COLORS = {
    'primary': '#3498db',
    'success': '#2ecc71', 
    'warning': '#f39c12',
    'danger': '#e74c3c',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40'
}

# أنماط الجداول
TABLE_STYLE = """
    QTableWidget {
        gridline-color: #bdc3c7;
        background-color: white;
        alternate-background-color: #f8f9fa;
        selection-background-color: #3498db;
        selection-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
    }
    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #ecf0f1;
    }
    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 10px;
        border: none;
        font-weight: bold;
        font-size: 11pt;
    }
"""

def get_button_style(color):
    """إنشاء نمط الأزرار"""
    return f"""
        QPushButton {{
            background-color: {color};
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 12pt;
            font-weight: bold;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {color}dd;
        }}
        QPushButton:pressed {{
            background-color: {color}bb;
        }}
    """

class DeliveryDelegatesWindow(QMainWindow):
    """نافذة قائمة توزيع المندوبين - مطابقة لقائمة استلام المندوبين"""

    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة توزيع المندوبين")
        self.setGeometry(100, 100, 1400, 800)

        # إعداد قاعدة البيانات والسجل
        self.db = db if db else Database()
        self.logger = logging.getLogger('delivery_delegates')

        # متغيرات الحالة (مطابقة لقائمة استلام المندوبين)
        self.current_delegate_name = None
        self.current_orders_table = None
        self.current_dialog = None
        self.all_selected = False

        # ثوابت التصميم (مطابقة لقائمة استلام المندوبين)
        self.MAIN_MARGIN = 20
        self.CONTROL_BTN_MIN_WIDTH = 100
        self.CONTROL_BTN_MAX_WIDTH = 150
        self.ORDERS_DIALOG_WIDTH = 1400
        self.ORDERS_DIALOG_HEIGHT = 800

        self.initUI()
        self.load_delegates()
    
    def initUI(self):
        """تهيئة واجهة المستخدم - مطابقة لقائمة استلام المندوبين"""
        central_widget = QWidget()

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # العنوان الرئيسي (مطابق لقائمة استلام المندوبين مع تغيير اللون للأخضر)
        title = QLabel("قائمة توزيع المندوبين")
        title.setAlignment(Qt.AlignCenter)
        title.setFixedHeight(60)
        title.setStyleSheet('''
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: white;
                background-color: #27ae60;
                padding: 0;
                margin: 0;
                border: none;
                line-height: 60px;
            }
        ''')

        # إنشاء حاوية للمحتوى
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(self.MAIN_MARGIN, -25, self.MAIN_MARGIN, 0)  # تقريب الأزرار للأعلى
        content_layout.setSpacing(0)

        # إضافة مجموعة المندوبين إلى حاوية المحتوى
        self.delegates_group = QGroupBox()
        self.delegates_group.setStyleSheet('''
            QGroupBox {
                border: none;
                margin-top: -25px;  /* تقريب الأزرار للأعلى */
                padding: 0;
            }
        ''')

        # تعديل تخطيط أزرار المندوبين
        self.delegates_layout = QGridLayout(self.delegates_group)
        self.delegates_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهامش العلوي
        self.delegates_layout.setSpacing(5)  # مسافات صغيرة بين الأزرار

        content_layout.addWidget(self.delegates_group)

        # إضافة زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: {self.CONTROL_BTN_MIN_WIDTH}px;
                max-width: {self.CONTROL_BTN_MAX_WIDTH}px;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
        """)
        close_button.clicked.connect(self.close)
        content_layout.addWidget(close_button, 0, Qt.AlignCenter)

        # إضافة العناصر إلى التخطيط الرئيسي بالترتيب المطلوب
        main_layout.addWidget(title)
        main_layout.addWidget(content)

        self.setCentralWidget(central_widget)
    
    def get_delegates(self):
        """الحصول على قائمة المندوبين من قاعدة البيانات - مطابقة لقائمة استلام المندوبين"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # طباعة إضافية للتشخيص
            print("\nبدء استعلام جلب مندوبي التوزيع...")

            # جلب المندوبين من جدول delegates مع حساب عدد الطلبات من جدول orders مباشرة
            # البحث عن الطلبات بحالة 'distribution' (جاهزة للتوزيع)
            cursor.execute('''
                SELECT
                    d.name as delegate_name,
                    COUNT(o.id) as orders_count
                FROM delegates d
                LEFT JOIN orders o ON d.name = o.delegate_name AND o.status = 'distribution'
                GROUP BY d.name
                HAVING COUNT(o.id) > 0
                ORDER BY d.name
            ''')

            delegates = []
            for row in cursor.fetchall():
                delegate_name, orders_count = row
                delegates.append((delegate_name, orders_count))
                print(f"مندوب توزيع: {delegate_name}, عدد الطلبات: {orders_count}")

            # جلب عدد السجلات في جدول orders للتشخيص
            cursor.execute("SELECT COUNT(*) FROM orders WHERE status = 'distribution'")
            total_orders = cursor.fetchone()[0]
            print(f"إجمالي عدد الطلبات في التوزيع: {total_orders}")

            # طباعة بعض سجلات جدول orders للتشخيص
            cursor.execute("SELECT id, delegate_name, receipt_number FROM orders WHERE status = 'distribution' LIMIT 5")
            sample_orders = cursor.fetchall()
            print("\nعينة من طلبات التوزيع:")
            for order in sample_orders:
                print(f"معرف: {order[0]}, اسم المندوب: {order[1]}, رقم الوصل: {order[2]}")

            conn.close()
            return delegates

        except Exception as e:
            print(f"خطأ في جلب قائمة مندوبي التوزيع: {str(e)}")
            return []

    def load_delegates(self):
        """تحميل المندوبين من قاعدة البيانات - مطابقة لقائمة استلام المندوبين"""
        try:
            # حذف المندوبين الذين ليس لديهم طلبات
            self.delete_delegates_without_orders()

            # مسح أزرار المندوبين الحالية
            self.clear_delegates_layout()

            # جلب قائمة المندوبين
            delegates = self.get_delegates()

            # طباعة قائمة المندوبين
            print("\nقائمة مندوبي التوزيع المعروضة:")
            for i, delegate in enumerate(delegates, 1):
                delegate_name, orders_count = delegate
                print(f"{i}. {delegate_name} - عدد الطلبات: {orders_count}")

            if not delegates:
                no_delegates = QLabel('لا توجد مندوبين مسجلين بطلبات جاهزة للتوزيع')
                no_delegates.setAlignment(Qt.AlignCenter)
                no_delegates.setStyleSheet('font-size: 14pt; color: #7f8c8d; margin: 20px 0px;')
                self.delegates_layout.addWidget(no_delegates, 0, 0, 1, 6)
            else:
                # إنشاء أزرار للمندوبين
                row = 0
                col = 0
                for delegate in delegates:
                    delegate_name, orders_count = delegate
                    if delegate_name and delegate_name.strip():
                        # إنشاء حاوية للزر والمعلومات
                        delegate_widget = QWidget()
                        delegate_layout = QVBoxLayout(delegate_widget)
                        delegate_layout.setContentsMargins(5, 5, 5, 5)

                        # إنشاء الزر مع اسم المندوب وعدد الطلبات (لون أخضر للتوزيع)
                        button_text = f"{delegate_name} ({orders_count})"
                        delegate_button = QPushButton(button_text)
                        delegate_button.setStyleSheet('''
                            QPushButton {
                                background-color: #27ae60;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                padding: 8px;
                                font-size: 11pt;
                                font-weight: bold;
                                text-align: center;
                                min-width: 100px;
                                max-width: 100px;
                                min-height: 90px;
                                max-height: 90px;
                            }
                            QPushButton:hover {
                                background-color: #229954;
                            }
                        ''')

                        delegate_button.clicked.connect(
                            lambda checked, d_name=delegate_name:
                            self.show_delegate_orders(d_name)
                        )

                        # اضافة زر المندوب
                        delegate_layout.addWidget(delegate_button)

                        self.delegates_layout.addWidget(delegate_widget, row, col)

                        # انتقال للعمود التالي، أو للصف التالي إذا وصلنا إلى آخر عمود
                        col += 1
                        if col >= 6:  # 6 أزرار في كل صف
                            col = 0
                            row += 1

        except Exception as e:
            print(f"خطأ في تحميل مندوبي التوزيع: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل مندوبي التوزيع: {str(e)}")

    def clear_delegates_layout(self):
        """مسح أزرار المندوبين الحالية - مطابقة لقائمة استلام المندوبين"""
        if hasattr(self, 'delegates_layout') and self.delegates_layout:
            while self.delegates_layout.count():
                child = self.delegates_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

    def delete_delegates_without_orders(self):
        """حذف المندوبين الذين ليس لديهم طلبات - مطابقة لقائمة استلام المندوبين"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # حذف المندوبين الذين ليس لديهم طلبات في حالة التوزيع
            cursor.execute("""
                DELETE FROM delegates
                WHERE name NOT IN (
                    SELECT DISTINCT delegate_name
                    FROM orders
                    WHERE delegate_name IS NOT NULL
                    AND delegate_name != ''
                    AND status = 'distribution'
                )
            """)

            deleted_count = cursor.rowcount
            if deleted_count > 0:
                print(f"تم حذف {deleted_count} مندوب ليس لديهم طلبات توزيع")

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حذف المندوبين بدون طلبات: {str(e)}")
    

    
    def show_delegate_orders(self, delegate_name):
        """عرض طلبات المندوب المحدد للتوزيع - مطابقة لقائمة استلام المندوبين"""
        try:
            # تخزين المندوب الحالي
            self.current_delegate_name = delegate_name

            conn = self.db.get_connection()
            cursor = conn.cursor()

            print(f"\nجلب طلبات التوزيع للمندوب: {delegate_name}")

            # استعلام جديد تماماً: استرجاع البيانات من جدول orders مباشرة حيث delegate_name يطابق
            # البحث عن الطلبات بحالة 'distribution' (جاهزة للتوزيع)
            if self.db.db_type == 'mysql':
                # استخدام %s للمعاملات في MySQL
                cursor.execute("""
                    SELECT
                        o.id,
                        o.receipt_number,
                        o.phone,
                        o.address,
                        o.carpet_count,
                        o.blanket_count,
                        o.total_price,
                        o.created_at,
                        o.status,
                        o.notes
                    FROM orders o
                    WHERE o.delegate_name = %s AND o.status = 'distribution'
                    ORDER BY o.created_at DESC
                """, (delegate_name,))
            else:
                # استخدام ? للمعاملات في SQLite
                cursor.execute("""
                    SELECT
                        o.id,
                        o.receipt_number,
                        o.phone,
                        o.address,
                        o.carpet_count,
                        o.blanket_count,
                        o.total_price,
                        o.created_at,
                        o.status,
                        o.notes
                    FROM orders o
                    WHERE o.delegate_name = ? AND o.status = 'distribution'
                    ORDER BY o.created_at DESC
                """, (delegate_name,))

            orders_from_main_table = cursor.fetchall()

            # طباعة نتائج الاستعلام المباشر من جدول orders
            print(f"عدد طلبات التوزيع المسترجعة مباشرة من جدول orders: {len(orders_from_main_table)}")
            if orders_from_main_table:
                print("أول 3 طلبات توزيع من جدول orders:")
                for i, order in enumerate(orders_from_main_table[:min(3, len(orders_from_main_table))]):
                    print(f"طلب {i+1} (ID: {order[0]}): receipt_number='{order[1]}', carpet_count={order[4]}, blanket_count={order[5]}, total_price={order[6]}")

            conn.close()

            # إذا لم يكن هناك أي طلبات، نظهر رسالة
            if not orders_from_main_table:
                QMessageBox.information(self, "معلومات", f"لا توجد طلبات توزيع للمندوب {delegate_name}")
                return

            # نستخدم النتيجة النهائية لإنشاء واجهة العرض
            orders = orders_from_main_table

            # إنشاء نافذة عرض الطلبات
            orders_dialog = QDialog(self)
            orders_dialog.setWindowTitle(f"طلبات التوزيع - المندوب: {delegate_name}")
            orders_dialog.setModal(True)
            orders_dialog.setMinimumSize(self.ORDERS_DIALOG_WIDTH, self.ORDERS_DIALOG_HEIGHT)

            # تهيئة الجدول وعرض الطلبات
            self.init_orders_table(orders_dialog, orders)

            # إضافة معالج أحداث المفاتيح للنافذة
            self.setup_keyboard_navigation(orders_dialog)

            orders_dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض طلبات التوزيع للمندوب: {str(e)}")
            import traceback
            print(traceback.format_exc())  # طباعة تفاصيل الخطأ كاملة
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض طلبات التوزيع للمندوب: {str(e)}")
    
    def init_orders_table(self, orders_dialog, orders):
        """تهيئة جدول الطلبات - مطابقة لقائمة استلام المندوبين"""
        layout = QVBoxLayout()
        orders_dialog.setLayout(layout)

        # تخزين مرجع للحوار الحالي
        self.current_dialog = orders_dialog

        # إضافة حقل البحث في تخطيط منفصل
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setStyleSheet('font-size: 12pt; font-weight: bold;')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث برقم الهاتف...")
        self.search_input.setStyleSheet('''
            QLineEdit {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12pt;
                min-width: 250px;
            }
            QLineEdit:focus {
                border: 2px solid #27ae60;
            }
        ''')

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        layout.addLayout(search_layout)

        # إضافة زر تحديد/إلغاء تحديد الكل وزر الطباعة في تخطيط منفصل
        select_layout = QHBoxLayout()

        # زر تحديد الطلبات
        self.select_all_button = QPushButton("تحديد الطلبات")
        self.select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.select_all_button.clicked.connect(lambda: self.toggle_all_selections())
        select_layout.addWidget(self.select_all_button)

        # زر طباعة الطلبات المحددة
        self.print_button = QPushButton("طباعة الطلبات")
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.print_button.clicked.connect(self.print_selected_orders)
        select_layout.addWidget(self.print_button)

        select_layout.addStretch()
        layout.addLayout(select_layout)

        # إضافة متغير لتتبع حالة التحديد
        self.all_selected = False

        # تحديث قائمة الأعمدة لتشمل عمود التحديد (مطابقة لقائمة استلام المندوبين)
        columns = [
            "تحديد", "ID", "رقم الوصل", "رقم الهاتف", "العنوان", "عدد السجاد",
            "عدد البطانية", "المساحة (م²)", "السعر الإجمالي",
            "تاريخ الإنشاء", "الحالة", "ملاحظات", "النقل", "تفاصيل", "مكتمل", "مؤجل"
        ]

        # إنشاء جدول الطلبات
        orders_table = QTableWidget()
        self.current_orders_table = orders_table
        orders_table.setColumnCount(16)  # زيادة عدد الأعمدة لإضافة عمود التحديد
        orders_table.setRowCount(len(orders))

        # تعيين سلوك التحديد للجدول
        orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        orders_table.setSelectionMode(QTableWidget.SingleSelection)

        # تمكين تظليل الصفوف عند النقر عليها
        orders_table.setAlternatingRowColors(True)

        # تعيين رؤوس الأعمدة
        for i, column in enumerate(columns):
            header_item = QTableWidgetItem(column)
            orders_table.setHorizontalHeaderItem(i, header_item)

        # تنسيق الجدول (مطابق لقائمة استلام المندوبين)
        orders_table.setStyleSheet('''
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11pt;
                alternate-background-color: #f2f2f2;
            }
            QHeaderView::section {
                background-color: #27ae60;
                padding: 5px;
                border: 1px solid #bdc3c7;
                font-size: 11pt;
                font-weight: bold;
                color: white;
            }
            QTableWidget::item {
                border: 1px solid #bdc3c7;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
        ''')

        # تعيين عرض الأعمدة بشكل دقيق (بالبكسل) - مطابق لقائمة استلام المندوبين
        column_widths = {
            0: 50,   # عمود التحديد
            1: 0,    # ID
            2: 80,   # رقم الوصل
            3: 110,  # رقم الهاتف
            4: 170,  # العنوان
            5: 70,   # عدد السجاد
            6: 70,   # عدد البطانية
            7: 80,   # المساحة (م²)
            8: 80,   # السعر الإجمالي
            9: 100,  # تاريخ الإنشاء
            10: 0,   # الحالة
            11: 130, # ملاحظات
            12: 60,  # رسوم النقل
            13: 90,  # تفاصيل
            14: 90,  # مكتمل
            15: 90   # مؤجل
        }

        # تطبيق عرض محدد لكل عمود
        for col, width in column_widths.items():
            orders_table.setColumnWidth(col, width)

        # إخفاء عمود ID وعمود الحالة
        orders_table.hideColumn(1)   # إخفاء عمود ID
        orders_table.hideColumn(10)  # إخفاء عمود الحالة

        # ملء البيانات في الجدول
        self.populate_orders_table(orders_table, orders)

        # إضافة اتصال حقل البحث مع دالة التصفية
        self.search_input.textChanged.connect(lambda text: self.filter_orders(orders_table, text))

        layout.addWidget(orders_table)

    def populate_orders_table(self, orders_table, orders):
        """ملء بيانات الطلبات في الجدول - مطابقة لقائمة استلام المندوبين"""
        try:
            # ملء البيانات في الجدول
            for row, order in enumerate(orders):
                order_id, receipt_number, phone, address, carpet_count, blanket_count, total_price, created_at, status, notes = order

                # مربع التحديد (العمود 0)
                checkbox = QCheckBox()
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                orders_table.setCellWidget(row, 0, checkbox_widget)

                # معرف الطلب (العمود 1 - مخفي)
                id_item = QTableWidgetItem(str(order_id))
                id_item.setData(Qt.UserRole, order_id)
                orders_table.setItem(row, 1, id_item)

                # رقم الوصل (العمود 2)
                orders_table.setItem(row, 2, QTableWidgetItem(str(receipt_number or "")))

                # رقم الهاتف (العمود 3)
                orders_table.setItem(row, 3, QTableWidgetItem(str(phone or "")))

                # العنوان (العمود 4)
                orders_table.setItem(row, 4, QTableWidgetItem(str(address or "")))

                # عدد السجاد (العمود 5)
                orders_table.setItem(row, 5, QTableWidgetItem(str(carpet_count or 0)))

                # عدد البطانية (العمود 6)
                orders_table.setItem(row, 6, QTableWidgetItem(str(blanket_count or 0)))

                # المساحة (العمود 7) - حساب تقريبي
                area = (carpet_count or 0) * 6  # تقدير 6 متر مربع لكل سجادة
                orders_table.setItem(row, 7, QTableWidgetItem(str(area)))

                # السعر الإجمالي (العمود 8)
                price_formatted = "{:,}".format(int(float(total_price or 0)))
                orders_table.setItem(row, 8, QTableWidgetItem(price_formatted))

                # تاريخ الإنشاء (العمود 9)
                orders_table.setItem(row, 9, QTableWidgetItem(str(created_at or "")))

                # الحالة (العمود 10 - مخفي)
                orders_table.setItem(row, 10, QTableWidgetItem("جاهز للتوزيع"))

                # الملاحظات (العمود 11)
                orders_table.setItem(row, 11, QTableWidgetItem(str(notes or "")))

                # رسوم النقل (العمود 12)
                orders_table.setItem(row, 12, QTableWidgetItem("0"))

                # زر التفاصيل (العمود 13)
                details_container = QWidget()
                details_layout = QHBoxLayout(details_container)
                details_layout.setContentsMargins(2, 2, 2, 2)
                details_layout.setAlignment(Qt.AlignCenter)

                details_btn = QPushButton("تفاصيل")
                details_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        border-radius: 10px;
                        padding: 6px;
                        font-size: 9pt;
                        min-width: 60px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                details_layout.addWidget(details_btn)
                orders_table.setCellWidget(row, 13, details_container)

                # زر مكتمل (العمود 14)
                complete_container = QWidget()
                complete_layout = QHBoxLayout(complete_container)
                complete_layout.setContentsMargins(2, 2, 2, 2)
                complete_layout.setAlignment(Qt.AlignCenter)

                complete_btn = QPushButton("مكتمل")
                complete_btn.setProperty("order_id", order_id)
                complete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 10px;
                        padding: 6px;
                        font-size: 9pt;
                        min-width: 60px;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                """)
                complete_btn.clicked.connect(lambda checked, oid=order_id: self.complete_order(oid))
                complete_layout.addWidget(complete_btn)
                orders_table.setCellWidget(row, 14, complete_container)

                # زر مؤجل (العمود 15)
                defer_container = QWidget()
                defer_layout = QHBoxLayout(defer_container)
                defer_layout.setContentsMargins(2, 2, 2, 2)
                defer_layout.setAlignment(Qt.AlignCenter)

                defer_btn = QPushButton("مؤجل")
                defer_btn.setProperty("order_id", order_id)
                defer_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f39c12;
                        color: white;
                        border: none;
                        border-radius: 10px;
                        padding: 6px;
                        font-size: 9pt;
                        min-width: 60px;
                    }
                    QPushButton:hover {
                        background-color: #d35400;
                    }
                """)
                defer_btn.clicked.connect(lambda checked, oid=order_id: self.defer_order(oid))
                defer_layout.addWidget(defer_btn)
                orders_table.setCellWidget(row, 15, defer_container)

        except Exception as e:
            print(f"خطأ في ملء جدول طلبات التوزيع: {str(e)}")

    def setup_keyboard_navigation(self, orders_dialog):
        """إعداد التنقل بالمفاتيح - مطابقة لقائمة استلام المندوبين"""
        # يمكن إضافة معالجات المفاتيح هنا إذا لزم الأمر
        pass

    def toggle_all_selections(self):
        """تبديل تحديد جميع الطلبات - مطابقة لقائمة استلام المندوبين"""
        if not self.current_orders_table:
            return

        self.all_selected = not self.all_selected

        for row in range(self.current_orders_table.rowCount()):
            checkbox_widget = self.current_orders_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(self.all_selected)

        # تحديث نص الزر
        if self.all_selected:
            self.select_all_button.setText("إلغاء تحديد الطلبات")
        else:
            self.select_all_button.setText("تحديد الطلبات")

    def print_selected_orders(self):
        """طباعة الطلبات المحددة - مطابقة لقائمة استلام المندوبين"""
        if not self.current_orders_table:
            return

        selected_orders = []
        for row in range(self.current_orders_table.rowCount()):
            checkbox_widget = self.current_orders_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    order_id_item = self.current_orders_table.item(row, 1)
                    if order_id_item:
                        selected_orders.append(order_id_item.text())

        if not selected_orders:
            QMessageBox.information(self, "معلومات", "لم يتم تحديد أي طلبات للطباعة")
            return

        QMessageBox.information(self, "طباعة", f"تم تحديد {len(selected_orders)} طلب للطباعة")
        # يمكن إضافة منطق الطباعة الفعلي هنا
    
    def complete_order(self, order_id):
        """تحويل الطلب إلى قائمة الطلبات المكتملة"""
        try:
            result = QMessageBox.question(
                self,
                "تأكيد الإكمال",
                "هل أنت متأكد من تحويل الطلب إلى قائمة الطلبات المكتملة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if result == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # تحديث حالة الطلب إلى مكتمل
                cursor.execute("""
                    UPDATE orders
                    SET status = 'completed'
                    WHERE id = %s
                """, (order_id,))
                
                # حذف من جدول delegate_orders إذا كان موجود<|im_start|>
                cursor.execute("DELETE FROM delegate_orders WHERE order_id = %s", (order_id,))
                
                conn.commit()
                conn.close()
                
                # إزالة الصف من الجدول
                self.remove_order_from_table(order_id)
                
                # تحديث قائمة المندوبين
                self.load_delegates()
                
                QMessageBox.information(self, "تم الإكمال", "تم تحويل الطلب إلى قائمة الطلبات المكتملة بنجاح")
                
        except Exception as e:
            print(f"خطأ في إكمال الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إكمال الطلب: {str(e)}")
    
    def defer_order(self, order_id):
        """تأجيل الطلب وإرجاعه إلى المخزن"""
        try:
            result = QMessageBox.question(
                self,
                "تأكيد التأجيل",
                "هل أنت متأكد من تأجيل الطلب وإرجاعه إلى المخزن؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if result == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # تحديث حالة الطلب إلى المخزن
                cursor.execute("""
                    UPDATE orders
                    SET status = 'warehouse',
                        pickup_representative_id = NULL,
                        delegate_name = NULL
                    WHERE id = %s
                """, (order_id,))
                
                # حذف من جدول delegate_orders إذا كان موجود<|im_start|>
                cursor.execute("DELETE FROM delegate_orders WHERE order_id = %s", (order_id,))
                
                conn.commit()
                conn.close()
                
                # إزالة الصف من الجدول
                self.remove_order_from_table(order_id)
                
                # تحديث قائمة المندوبين
                self.load_delegates()
                
                QMessageBox.information(self, "تم التأجيل", "تم تأجيل الطلب وإرجاعه إلى المخزن بنجاح")
                
        except Exception as e:
            print(f"خطأ في تأجيل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تأجيل الطلب: {str(e)}")
    
    def remove_order_from_table(self, order_id):
        """إزالة الطلب من الجدول الحالي"""
        if self.current_orders_table:
            for row in range(self.current_orders_table.rowCount()):
                item = self.current_orders_table.item(row, 1)  # العمود الثاني يحتوي على ID
                if item and int(item.text()) == order_id:
                    self.current_orders_table.removeRow(row)
                    break

    def filter_orders(self, table, search_text):
        """تصفية الطلبات حسب رقم الهاتف"""
        try:
            search_text = search_text.strip().lower()

            for row in range(table.rowCount()):
                # البحث في رقم الهاتف (العمود 3)
                phone_item = table.item(row, 3)
                phone_text = phone_item.text().lower() if phone_item else ""

                # البحث في رقم الوصل (العمود 2)
                receipt_item = table.item(row, 2)
                receipt_text = receipt_item.text().lower() if receipt_item else ""

                # إظهار أو إخفاء الصف حسب نتيجة البحث
                if search_text == "" or search_text in phone_text or search_text in receipt_text:
                    table.setRowHidden(row, False)
                else:
                    table.setRowHidden(row, True)

        except Exception as e:
            print(f"خطأ في تصفية الطلبات: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DeliveryDelegatesWindow()
    window.show()
    sys.exit(app.exec_())
