#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def get_connection():
    """u0625u0646u0634u0627u0621 u0627u062au0635u0627u0644 u0628u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a"""
    db_path = 'carpet_cleaning.db'
    # u0625u0630u0627 u0644u0645 u064au0643u0646 u0627u0644u0645u0644u0641 u0645u0648u062cu0648u062fu064bu0627 u0641u064a u0627u0644u0645u0633u0627u0631 u0627u0644u062du0627u0644u064au060c u0646u0641u062au0631u0636 u0623u0646u0647 u0641u064a u0627u0644u0645u062cu0644u062f u0627u0644u0631u0626u064au0633u064a
    if not os.path.exists(db_path):
        db_path = os.path.join(os.path.dirname(__file__), '..', 'carpet_cleaning.db')
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def check_orders_table():
    """u0641u062du0635 u0647u064au0643u0644 u062cu062fu0648u0644 u0627u0644u0637u0644u0628u0627u062a"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # u0627u0644u062au062du0642u0642 u0645u0646 u0647u064au0643u0644 u0627u0644u062cu062fu0648u0644
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        print("u0647u064au0643u0644 u062cu062fu0648u0644 u0627u0644u0637u0644u0628u0627u062a:")
        print("-" * 70)
        print("# | u0627u0633u0645 u0627u0644u062du0642u0644 | u0627u0644u0646u0648u0639 | u0625u0644u0632u0627u0645u064a | u0627u0644u0642u064au0645u0629 u0627u0644u0627u0641u062au0631u0627u0636u064au0629 | u0645u0641u062au0627u062d u0631u0626u064au0633u064a")
        print("-" * 70)
        for col in columns:
            print(f"{col['cid']} | {col['name']} | {col['type']} | {col['notnull']} | {col['dflt_value']} | {col['pk']}")
        
        # u0627u0644u062au062du0642u0642 u0645u0646 u0627u0644u062cu062fu0627u0648u0644 u0627u0644u0645u0648u062cu0648u062fu0629 u0641u064a u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("\nu0627u0644u062cu062fu0627u0648u0644 u0627u0644u0645u0648u062cu0648u062fu0629 u0641u064a u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a:")
        print("-" * 50)
        for table in tables:
            print(table['name'])
        
    except Exception as e:
        print(f"u062du062fu062b u062eu0637u0623 u0623u062bu0646u0627u0621 u0641u062du0635 u062cu062fu0648u0644 u0627u0644u0637u0644u0628u0627u062a: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_orders_table()
