#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QCheckBox, QAbstractItemView,
                             QGroupBox, QFormLayout, QComboBox, QWidget, QSpinBox)
from PyQt5.QtPrintSupport import QPrinter, QPrintPreviewDialog
from PyQt5.QtCore import Qt, QDateTime, QObject, QEvent
from PyQt5.QtGui import QFont, QColor, QKeyEvent
import sqlite3
import logging
from ui.styles import get_button_style, COLORS, TABLE_STYLE, SEARCH_STYLE
from src.carpet_dimensions import CarpetDimensionsDialog

class RepresentativeOrdersWindow(QDialog):
    """نافذة عرض طلبات المندوب"""
    def __init__(self, rep_id, rep_name, parent=None):
        super().__init__(parent)
        self.rep_id = rep_id
        self.rep_name = rep_name
        self.setWindowTitle(f"طلبات المندوب: {rep_name}")
        self.setModal(True)
        self.setMinimumSize(1200, 600)

        # إضافة خاصية db التي سيتم تعيينها لاحقًا
        self.db = None

        # إعداد السجل - تصحيح خطأ formatter
        self.logger = logging.getLogger('rep_orders_view')
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # إعداد فلاتر البحث
        self.search_receipt_filter = None
        self.search_phone_filter = None

        # إعدادات التحكم في عرض حقل البحث وارتفاع الصفوف
        self.search_field_width = 250  # زيادة عرض حقل البحث
        self.row_height = 35  # ارتفاع الصفوف الافتراضي

        # متغير لتتبع الصف الحالي
        self.current_row = -1

        # تهيئة الواجهة
        self.initUI()

        # تفعيل خاصية تحديد الصفوف
        self.orders_table.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # إعداد التنقل باستخدام لوحة المفاتيح
        self.setup_keyboard_navigation()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # عنوان
        title_label = QLabel(f"طلبات المندوب: {self.rep_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin-bottom: 10px; color: #2c3e50;")
        main_layout.addWidget(title_label)

        # سيتم نقل عرض السعر الإجمالي إلى مستوى الأزرار لاحقاً

        # تحديث ترتيب وأسماء الأعمدة
        self.column_headers = [
            "تحديد", "تسلسل", "رقم الوصل", "العنوان", "رقم الهاتف",
            "عدد السجاد", "المساحة", "عدد البطانية",
            "السعر (د.ع)", "تاريخ", "تفاصيل", "واصل", "مؤجل"
        ]

        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(13)
        self.orders_table.setHorizontalHeaderLabels(self.column_headers)

        # إعادة تعيين أحجام الأعمدة
        for col in range(self.orders_table.columnCount()):
            self.orders_table.horizontalHeader().setSectionResizeMode(col, QHeaderView.Fixed)

        # تعيين عرض كل عمود
        self.orders_table.setColumnWidth(0, 30)    # تحديد
        self.orders_table.setColumnWidth(1, 35)    # تسلسل
        self.orders_table.setColumnWidth(2, 80)    # رقم الوصل
        self.orders_table.setColumnWidth(3, 190)   # العنوان
        self.orders_table.setColumnWidth(4, 110)   # رقم الهاتف
        self.orders_table.setColumnWidth(5, 70)    # عدد السجاد
        self.orders_table.setColumnWidth(6, 70)    # المساحة
        self.orders_table.setColumnWidth(7, 70)    # عدد البطانية
        self.orders_table.setColumnWidth(8, 80)    # السعر
        self.orders_table.setColumnWidth(9, 100)    # تاريخ
        self.orders_table.setColumnWidth(10, 90)   # تفاصيل
        self.orders_table.setColumnWidth(11, 90)   # واصل
        self.orders_table.setColumnWidth(12, 90)   # مؤجل

        # إعدادات الجدول
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setStyleSheet(TABLE_STYLE)
        self.orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.verticalHeader().setVisible(False)

        # إعدادات الجدول
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setStyleSheet(TABLE_STYLE)
        self.orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.verticalHeader().setVisible(False)

        # إعادة هيكلة التخطيط بالكامل
        # إنشاء تخطيط رئيسي للعمليات
        actions_layout = QVBoxLayout()
        actions_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        # قسم البحث - تصميم محسن
        search_layout = QHBoxLayout()
        search_layout.setAlignment(Qt.AlignCenter)  # تغيير المحاذاة إلى المركز
        search_layout.setContentsMargins(0, 0, 0, 10)  # إضافة هامش سفلي
        search_layout.setSpacing(3)  # تقليل المسافة بين كلمة البحث وحقل البحث

        # تحسين شكل وموقع كلمة البحث
        search_label = QLabel("بحث:")
        search_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2c3e50; padding-left: 5px;")

        # تحسين حقل البحث بشكل كبير
        self.search_phone = QLineEdit()
        self.search_phone.setPlaceholderText("ابحث في الطلبات (رقم الهاتف أو رقم الوصل)...")
        self.search_phone.setMinimumWidth(500)  # زيادة عرض حقل البحث بشكل كبير
        self.search_phone.setFixedWidth(500)  # تثبيت العرض
        self.search_phone.setMinimumHeight(40)  # زيادة ارتفاع حقل البحث
        self.search_phone.setStyleSheet(SEARCH_STYLE + "font-size: 13pt; padding: 8px; border: 2px solid #3498db; border-radius: 6px;")
        self.search_phone.textChanged.connect(self.apply_filters)
        self.search_phone.returnPressed.connect(self.handle_search_enter)
        # إضافة معالج لمفتاح المسافة في حقل البحث
        self.search_phone.keyPressEvent = self.search_key_press_event

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_phone)

        # إضافة تخطيط البحث مباشرة إلى التخطيط الرئيسي
        actions_layout.addLayout(search_layout)

        # قسم الأزرار والسعر الإجمالي
        buttons_price_layout = QHBoxLayout()
        buttons_price_layout.setSpacing(15)

        # قسم الأزرار (على اليمين)
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignRight)
        buttons_layout.setSpacing(15)

        # زر تحديد/إلغاء تحديد الكل
        self.select_all_btn = QPushButton("تحديد الكل")
        self.select_all_btn.setMinimumWidth(120)
        self.select_all_btn.setStyleSheet(get_button_style(COLORS['info']))
        self.select_all_btn.clicked.connect(self.toggle_select_all)
        buttons_layout.addWidget(self.select_all_btn)

        # زر طباعة الطلبات المحددة
        self.print_selected_btn = QPushButton("طباعة المحدد")
        self.print_selected_btn.setMinimumWidth(120)
        self.print_selected_btn.setStyleSheet(get_button_style(COLORS['primary']))
        self.print_selected_btn.clicked.connect(self.print_selected_orders)
        buttons_layout.addWidget(self.print_selected_btn)

        # زر إرجاع الطلبات المحددة إلى المخزن
        self.return_to_warehouse_btn = QPushButton("إرجاع إلى المخزن")
        self.return_to_warehouse_btn.setMinimumWidth(120)
        self.return_to_warehouse_btn.setStyleSheet(get_button_style(COLORS['danger']))
        self.return_to_warehouse_btn.clicked.connect(self.return_selected_to_warehouse)
        buttons_layout.addWidget(self.return_to_warehouse_btn)

        # زر تحويل الطلبات المحددة إلى مكتملة
        self.mark_as_completed_btn = QPushButton("تحويل إلى مكتملة")
        self.mark_as_completed_btn.setMinimumWidth(120)
        self.mark_as_completed_btn.setStyleSheet(get_button_style(COLORS['success']))
        self.mark_as_completed_btn.clicked.connect(self.mark_selected_as_completed)
        buttons_layout.addWidget(self.mark_as_completed_btn)

        # إضافة الأزرار إلى تخطيط الأزرار والسعر
        buttons_price_layout.addLayout(buttons_layout)

        # عرض السعر الإجمالي (على اليمين)
        total_price_layout = QHBoxLayout()
        total_price_layout.setAlignment(Qt.AlignRight)

        self.total_price_label = QLabel("السعر الإجمالي: 0 د.ع")
        self.total_price_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2980b9;")
        total_price_layout.addWidget(self.total_price_label)

        # إضافة السعر الإجمالي إلى تخطيط الأزرار والسعر
        buttons_price_layout.addLayout(total_price_layout)

        # إضافة تخطيط الأزرار والسعر إلى التخطيط الرئيسي
        actions_layout.addLayout(buttons_price_layout)

        main_layout.addLayout(actions_layout)

        main_layout.addWidget(self.orders_table)

        self.setLayout(main_layout)

    def load_orders(self):
        """تحميل طلبات المندوب"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                from db.models import Database
                self.db = Database()
                self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات")

            # الاتصال بقاعدة البيانات باستخدام كائن Database
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')
            self.logger.info(f"نوع قاعدة البيانات: {db_type}")
            self.logger.info(f"معرف المندوب: {self.rep_id}, نوع: {type(self.rep_id)}")

            # استعلام للحصول على طلبات المندوب
            query = """
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = ? AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            """

            # تعديل الاستعلام حسب نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = query.replace('?', '%s')
                self.logger.info(f"استعلام MySQL: {query}")
            else:
                self.logger.info(f"استعلام SQLite: {query}")

            # التحقق من وجود طلبات للمندوب
            try:
                # تنفيذ استعلام للتحقق من وجود طلبات للمندوب
                check_query = "SELECT COUNT(*) FROM orders WHERE pickup_representative_id = ?"
                if db_type == 'mysql':
                    check_query = check_query.replace('?', '%s')

                cursor.execute(check_query, (self.rep_id,))
                count_result = cursor.fetchone()

                if isinstance(count_result, dict):
                    count = list(count_result.values())[0]
                elif isinstance(count_result, tuple):
                    count = count_result[0]
                else:
                    count = 0

                self.logger.info(f"عدد الطلبات للمندوب (بدون فلتر الحالة): {count}")

                # تنفيذ استعلام للتحقق من وجود طلبات للمندوب بحالة محددة
                status_check_query = "SELECT COUNT(*) FROM orders WHERE pickup_representative_id = ? AND (status = 'distribution' OR status = 'distribution_delivered')"
                if db_type == 'mysql':
                    status_check_query = status_check_query.replace('?', '%s')

                cursor.execute(status_check_query, (self.rep_id,))
                status_count_result = cursor.fetchone()

                if isinstance(status_count_result, dict):
                    status_count = list(status_count_result.values())[0]
                elif isinstance(status_count_result, tuple):
                    status_count = status_count_result[0]
                else:
                    status_count = 0

                self.logger.info(f"عدد الطلبات للمندوب (مع فلتر الحالة): {status_count}")
            except Exception as e:
                self.logger.error(f"خطأ في التحقق من وجود طلبات: {str(e)}")

            # تنفيذ الاستعلام الرئيسي
            cursor.execute(query, (self.rep_id,))
            orders = cursor.fetchall()

            # تحديث الجدول
            self.orders_table.setRowCount(0)  # مسح الجدول

            # حساب مجموع السعر الإجمالي
            total_orders_price = 0

            for idx, order in enumerate(orders):
                row_position = self.orders_table.rowCount()
                self.orders_table.insertRow(row_position)

                # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
                if isinstance(order, dict):  # في حالة استخدام row_factory مع SQLite
                    order_id = order['id']
                    receipt_number = order['receipt_number']
                    address = order['address']
                    phone = order['phone']
                    phone2 = order['phone2']
                    carpet_count = order['carpet_count']
                    total_area = order['total_area']
                    blanket_count = order['blanket_count']
                    total_price = order['total_price']
                    status = order['status']
                    created_at_str = order['created_at']
                    pickup_representative_id = order['pickup_representative_id']
                    notes = order['notes']
                elif isinstance(order, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                    order_id = order[0]
                    receipt_number = order[1]
                    address = order[2]
                    phone = order[3]
                    phone2 = order[4]
                    carpet_count = order[5]
                    total_area = order[6]
                    blanket_count = order[7]
                    total_price = order[8]
                    status = order[9]
                    created_at_str = order[10]
                    pickup_representative_id = order[11]
                    notes = order[12]
                else:
                    self.logger.warning(f"نوع غير معروف: {type(order)}, محتوى: {order}")
                    continue

                # تحويل التاريخ إلى تنسيق مقروء
                try:
                    created_at = QDateTime.fromString(str(created_at_str), "yyyy-MM-dd HH:mm:ss")
                    formatted_date = created_at.toString("yyyy-MM-dd")
                except:
                    formatted_date = str(created_at_str) if created_at_str else "غير معروف"

                price = float(total_price or 0)
                formatted_price = f"{int(price):,}".replace(",", "٬")  # استخدام فاصلة عربية

                # إضافة للسعر الإجمالي
                total_orders_price += price

                # إضافة مربع التحديد للطلب
                checkbox = QCheckBox()
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_widget.setLayout(checkbox_layout)
                self.orders_table.setCellWidget(row_position, 0, checkbox_widget)

                # إضافة بيانات الطلب وتوسيطها
                sequence_item = QTableWidgetItem(str(idx + 1))
                sequence_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 1, sequence_item)

                receipt_item = QTableWidgetItem(str(receipt_number or ""))
                receipt_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 2, receipt_item)

                address_item = QTableWidgetItem(address or "")
                address_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 3, address_item)

                # إنشاء ويدجت لعرض رقمي الهاتف واحد تحت الآخر
                phone_widget = QWidget()
                phone_layout = QVBoxLayout(phone_widget)
                phone_layout.setContentsMargins(2, 2, 2, 2)
                phone_layout.setSpacing(2)

                # الهاتف الأساسي
                phone1 = str(phone) if phone else ""
                if phone1:
                    phone1_label = QLabel(phone1)
                    phone1_label.setAlignment(Qt.AlignCenter)
                    phone1_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone1_label)

                # الهاتف الثاني إذا وجد
                phone2_str = str(phone2) if phone2 else ""
                if phone2_str:
                    phone2_label = QLabel(phone2_str)
                    phone2_label.setAlignment(Qt.AlignCenter)
                    phone2_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone2_label)
                    # تعديل ارتفاع الصف ليناسب الرقمين
                    self.orders_table.setRowHeight(row_position, 60)  # ارتفاع أكبر عند وجود رقمين
                else:
                    # استخدام الارتفاع الافتراضي عند وجود رقم واحد فقط
                    self.orders_table.setRowHeight(row_position, self.row_height)

                self.orders_table.setCellWidget(row_position, 4, phone_widget)

                # إضافة عمود عدد السجاد
                carpet_count_value = carpet_count or 0
                carpet_count_item = QTableWidgetItem(str(carpet_count_value))
                carpet_count_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 5, carpet_count_item)

                # إضافة عمود المساحة
                total_area_value = total_area or 0
                formatted_area = f"{float(total_area_value):.2f} م²"
                area_item = QTableWidgetItem(formatted_area)
                area_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 6, area_item)

                # إضافة عمود عدد البطانية
                blanket_count_value = blanket_count or 0
                blanket_count_item = QTableWidgetItem(str(blanket_count_value))
                blanket_count_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 7, blanket_count_item)

                # إضافة عمود السعر
                price_item = QTableWidgetItem(formatted_price)
                price_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 8, price_item)

                # إضافة عمود التاريخ
                date_item = QTableWidgetItem(formatted_date)
                date_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 9, date_item)

                # إضافة أزرار العمليات للطلب في عمود العمليات
                details_container = QWidget()
                details_layout = QHBoxLayout(details_container)
                details_layout.setContentsMargins(1, 1, 1, 1)
                details_layout.setAlignment(Qt.AlignCenter)

                details_btn = QPushButton("تفاصيل")
                details_btn.setProperty("order_id", order_id)
                details_btn.setStyleSheet(
                    "QPushButton { background-color: #3498db; color: white; border-radius: 4px; padding: 6px; font-size: 10pt; min-width: 60px; }"
                    "QPushButton:hover { background-color: #2980b9; }"
                )
                details_btn.clicked.connect(lambda checked, id=order_id: self.show_order_details(id))
                details_layout.addWidget(details_btn)
                self.orders_table.setCellWidget(row_position, 10, details_container)

                delivered_container = QWidget()
                delivered_layout = QHBoxLayout(delivered_container)
                delivered_layout.setContentsMargins(1, 1, 1, 1)
                delivered_layout.setAlignment(Qt.AlignCenter)

                delivered_btn = QPushButton("واصل")
                delivered_btn.setProperty("order_id", order_id)
                delivered_btn.setStyleSheet(
                    "QPushButton { background-color: #2ecc71; color: white; border-radius: 4px; padding: 6px; font-size: 10pt; min-width: 60px; }"
                    "QPushButton:hover { background-color: #27ae60; }"
                )
                delivered_btn.clicked.connect(lambda checked, id=order_id: self.mark_as_completed(id))
                delivered_layout.addWidget(delivered_btn)
                self.orders_table.setCellWidget(row_position, 11, delivered_container)

                postponed_container = QWidget()
                postponed_layout = QHBoxLayout(postponed_container)
                postponed_layout.setContentsMargins(1, 1, 1, 1)
                postponed_layout.setAlignment(Qt.AlignCenter)

                postponed_btn = QPushButton("مؤجل")
                postponed_btn.setProperty("order_id", order_id)
                postponed_btn.setStyleSheet(
                    "QPushButton { background-color: #e74c3c; color: white; border-radius: 4px; padding: 6px; font-size: 10pt; min-width: 60px; }"
                    "QPushButton:hover { background-color: #c0392b; }"
                )
                postponed_btn.clicked.connect(lambda checked, id=order_id: self.return_to_warehouse(id))
                postponed_layout.addWidget(postponed_btn)
                self.orders_table.setCellWidget(row_position, 12, postponed_container)

                # تخزين معرف الطلب كخاصية للصف
                self.orders_table.item(row_position, 1).setData(Qt.UserRole, order_id)

                # لا نقوم بإعادة تعيين ارتفاع الصف هنا لأننا قمنا بتعيينه بالفعل حسب وجود رقم هاتف ثانٍ أم لا

            # عرض السعر الإجمالي للطلبات
            formatted_total_price = f"{int(total_orders_price):,}".replace(",", "٬")  # استخدام فاصلة عربية
            self.update_total_price_display(formatted_total_price)

            # تسجيل نجاح العملية
            self.logger.info(f"تم تحميل {len(orders)} طلب للمندوب {self.rep_name}")

            # تطبيق الفلاتر المحفوظة
            self.apply_filters()

            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الطلبات: {str(e)}")

    def apply_filters(self):
        """تطبيق فلاتر البحث"""
        # تحديث قيم الفلاتر
        search_text = self.search_phone.text().strip().lower()
        self.search_phone_filter = search_text

        # عرض/إخفاء الصفوف حسب الفلاتر
        visible_rows_count = 0
        total_price_filtered = 0

        for row in range(self.orders_table.rowCount()):
            show_row = True

            # البحث في رقم الهاتف ورقم الوصل معاً
            if search_text:
                receipt_number = self.orders_table.item(row, 2).text().lower()

                # البحث في أرقام الهاتف (الأول والثاني)
                phone_widget = self.orders_table.cellWidget(row, 4)
                phone_text = ""

                if phone_widget:
                    # جمع نصوص جميع الـ QLabel في الـ widget
                    for i in range(phone_widget.layout().count()):
                        label = phone_widget.layout().itemAt(i).widget()
                        if label:
                            phone_text += label.text().lower() + " "

                # إذا لم يتطابق النص مع رقم الهاتف أو رقم الوصل، أخفِ الصف
                if search_text not in phone_text and search_text not in receipt_number:
                    show_row = False

            # عرض/إخفاء الصف بناءً على نتيجة التصفية
            self.orders_table.setRowHidden(row, not show_row)

            # حساب السعر الإجمالي للصفوف المرئية فقط
            if show_row:
                visible_rows_count += 1
                price_text = self.orders_table.item(row, 8).text().replace("٬", "")
                try:
                    price = float(price_text)
                    total_price_filtered += price
                except ValueError:
                    pass

        # تحديث عرض السعر الإجمالي بعد التصفية
        formatted_total_price = f"{int(total_price_filtered):,}".replace(",", "٬")
        self.update_total_price_display(formatted_total_price)

    def update_total_price_display(self, formatted_price):
        """تحديث عرض السعر الإجمالي"""
        self.total_price_label.setText(f"السعر الإجمالي: {formatted_price} د.ع")

    def search_key_press_event(self, event):
        """معالجة أحداث المفاتيح في حقل البحث"""
        # التحقق من ضغط مفتاح المسافة
        if event.key() == Qt.Key_Space and self.search_phone.text().strip():
            search_text = self.search_phone.text().strip().lower()

            # البحث عن الطلب المطابق وتحديده
            found_and_selected = False
            for row in range(self.orders_table.rowCount()):
                if self.orders_table.isRowHidden(row):
                    continue

                # البحث في رقم الوصل والهاتف
                receipt_number = self.orders_table.item(row, 2).text().lower()

                # البحث في أرقام الهاتف
                phone_widget = self.orders_table.cellWidget(row, 4)
                phone_text = ""

                if phone_widget:
                    # جمع نصوص جميع الـ QLabel في الـ widget
                    for i in range(phone_widget.layout().count()):
                        label = phone_widget.layout().itemAt(i).widget()
                        if label:
                            phone_text += label.text().lower() + " "

                if search_text in receipt_number or search_text in phone_text:
                    # تحديد الطلب (تفعيل مربع الاختيار)
                    checkbox_widget = self.orders_table.cellWidget(row, 0)
                    if checkbox_widget:
                        checkbox = checkbox_widget.layout().itemAt(0).widget()
                        if isinstance(checkbox, QCheckBox):
                            checkbox.setChecked(True)

                    # تحديد الصف المطابق
                    self.orders_table.selectRow(row)
                    # تمرير التركيز إلى الجدول
                    self.orders_table.setFocus()

                    # مسح حقل البحث للبحث عن طلب جديد
                    self.search_phone.clear()

                    # إلغاء تصفية الطلبات لإظهار جميع الطلبات مرة أخرى
                    self.apply_filters()

                    # إعادة التركيز إلى حقل البحث
                    self.search_phone.setFocus()

                    found_and_selected = True
                    break

            # إذا لم يتم العثور على طلب مطابق، عرض رسالة
            if not found_and_selected:
                self.logger.info(f"لم يتم العثور على طلب مطابق: {search_text}")
                # يمكن إضافة رسالة للمستخدم هنا إذا لزم الأمر

            # منع معالجة الحدث الافتراضي (إضافة مسافة)
            event.accept()
        else:
            # السماح بمعالجة الحدث الافتراضي لباقي المفاتيح
            QLineEdit.keyPressEvent(self.search_phone, event)

    def handle_search_enter(self):
        """معالجة حدث الضغط على المسطرة في حقل البحث"""
        search_text = self.search_phone.text().strip()

        # البحث عن رقم الوصل المطابق
        found_receipt = False
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                receipt_number = self.orders_table.item(row, 2).text()
                if search_text == receipt_number:
                    # تحديد الصف المطابق
                    self.orders_table.selectRow(row)
                    # تمرير التركيز إلى الجدول
                    self.orders_table.setFocus()
                    found_receipt = True
                    break

        # تفريغ حقل البحث لاستقبال رقم وصل جديد
        self.search_phone.clear()

        # إعادة التركيز إلى حقل البحث
        self.search_phone.setFocus()

        # إذا لم يتم العثور على رقم الوصل، عرض رسالة
        if not found_receipt and search_text:
            self.logger.info(f"لم يتم العثور على رقم الوصل: {search_text}")
            # يمكن إضافة رسالة للمستخدم هنا إذا لزم الأمر

    def increase_search_width(self):
        """زيادة عرض حقل البحث"""
        # زيادة عرض حقل البحث بمقدار 20 بكسل
        self.search_field_width += 20
        # تطبيق العرض الجديد على حقل البحث
        self.search_phone.setMinimumWidth(self.search_field_width)
        self.search_phone.setFixedWidth(self.search_field_width)
        self.logger.info(f"تم زيادة عرض حقل البحث إلى {self.search_field_width} بكسل")

    def decrease_search_width(self):
        """تقليل عرض حقل البحث"""
        # تقليل عرض حقل البحث بمقدار 20 بكسل، مع التأكد من عدم تقليله أقل من 100 بكسل
        if self.search_field_width > 100:
            self.search_field_width -= 20
            # تطبيق العرض الجديد على حقل البحث
            self.search_phone.setMinimumWidth(self.search_field_width)
            self.search_phone.setFixedWidth(self.search_field_width)
            self.logger.info(f"تم تقليل عرض حقل البحث إلى {self.search_field_width} بكسل")
        else:
            self.logger.info("لا يمكن تقليل عرض حقل البحث أكثر من ذلك")

    def setup_keyboard_navigation(self):
        """إعداد التنقل باستخدام لوحة المفاتيح في نافذة طلبات المندوب"""
        # تعيين دالة keyPressEvent للنافذة
        self.keyPressEvent = self.window_key_press_event

        # تعيين دالة keyPressEvent للجدول
        import types
        self.orders_table.keyPressEvent = types.MethodType(self.table_key_press_event, self.orders_table)

    def table_key_press_event(self, table, event):
        """معالجة أحداث المفاتيح للجدول"""
        # الحصول على الصف الحالي
        current_row = table.currentRow()

        # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
        if event.key() == Qt.Key_Space and current_row >= 0:
            # تأكد من أن الصف غير مخفي
            if not table.isRowHidden(current_row):
                # الحصول على مربع التحديد وتحديث حالته
                checkbox_container = table.cellWidget(current_row, 0)
                if checkbox_container and checkbox_container.layout():
                    checkbox = checkbox_container.layout().itemAt(0).widget()
                    if isinstance(checkbox, QCheckBox):
                        # تبديل حالة مربع الاختيار
                        checkbox.setChecked(not checkbox.isChecked())
                        # تحديث عرض الجدول
                        table.viewport().update()
                        # منع تمرير الحدث للفئة الأساسية
                        event.accept()
                        return

        # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
        QTableWidget.keyPressEvent(table, event)

    def window_key_press_event(self, event):
        """معالجة أحداث المفاتيح للنافذة"""
        # إذا كان التركيز على حقل البحث، نترك معالجة الحدث له
        if self.search_phone.hasFocus():
            super().keyPressEvent(event)
            return

        # إذا لم يكن التركيز على الجدول، ننقل التركيز إليه
        if not self.orders_table.hasFocus():
            self.orders_table.setFocus()

        # الحصول على الصف الحالي
        current_row = self.orders_table.currentRow()

        # إذا لم يتم تحديد أي صف، نحدد الصف الأول غير المخفي
        if current_row < 0 and event.key() in [Qt.Key_Up, Qt.Key_Down, Qt.Key_Space]:
            for row in range(self.orders_table.rowCount()):
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    current_row = row
                    self.current_row = row
                    break

        # تمرير الحدث للجدول لمعالجته
        QTableWidget.keyPressEvent(self.orders_table, event)

    def showEvent(self, event):
        """يتم استدعاء هذه الدالة عند عرض النافذة"""
        # تحديث الطلبات عند فتح النافذة
        self.load_orders()
        super().showEvent(event)

    def toggle_select_all(self):
        """تبديل حالة تحديد جميع الطلبات (تحديد/إلغاء تحديد)"""
        # تحقق من حالة التحديد الحالية (هل هناك أي عناصر محددة؟)
        any_selected = False
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                checkbox_widget = self.orders_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.layout().itemAt(0).widget()
                    if checkbox.isChecked():
                        any_selected = True
                        break

        # تبديل حالة التحديد بناءً على الحالة الحالية
        new_state = not any_selected
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                checkbox_widget = self.orders_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.layout().itemAt(0).widget()
                    checkbox.setChecked(new_state)

        # تحديث نص الزر
        if new_state:
            self.select_all_btn.setText("إلغاء التحديد")
        else:
            self.select_all_btn.setText("تحديد الكل")

    def get_selected_orders(self):
        """الحصول على قائمة معرفات الطلبات المحددة"""
        selected_orders = []

        # التحقق من مربعات التحديد بدلاً من الصفوف المحددة
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                checkbox_widget = self.orders_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.layout().itemAt(0).widget()
                    if checkbox.isChecked():
                        order_id = self.orders_table.item(row, 1).data(Qt.UserRole)
                        if order_id and order_id not in selected_orders:
                            selected_orders.append(order_id)

        return selected_orders

    def print_selected_orders(self):
        """طباعة الطلبات المحددة باستخدام مدير معاينة الطباعة"""
        selected_orders = self.get_selected_orders()

        if not selected_orders:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد طلب واحد على الأقل للطباعة")
            return

        try:
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect("carpet_cleaning.db")
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # إعداد استعلام مناسب للطباعة
            order_placeholders = ','.join(['?' for _ in selected_orders])
            query = f"""
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   o.total_price, o.created_at, o.notes
            FROM orders o
            WHERE o.id IN ({order_placeholders})
            ORDER BY o.created_at DESC
            """

            cursor.execute(query, selected_orders)
            orders_data = cursor.fetchall()

            # تحويل البيانات إلى التنسيق المطلوب لمدير معاينة الطباعة
            orders_for_print = []
            for order in orders_data:
                # تنسيق البيانات للطباعة (معرف، رقم الوصل، الهاتف، الهاتف2، العنوان، المبلغ، الملاحظات)
                order_id = order['id']
                receipt_number = order['receipt_number']
                phone = order['phone']
                phone2 = order['phone2']
                address = order['address']
                total_price = order['total_price']
                notes = order['notes']

                orders_for_print.append((order_id, receipt_number, phone, phone2, address, total_price, notes))

            # استدعاء مدير معاينة الطباعة
            from src.print_preview import PrintPreviewManager

            # إنشاء كائن مدير معاينة الطباعة
            print_manager = PrintPreviewManager(self)

            # طباعة قائمة طلبات المندوب
            result = print_manager.print_delegate_orders(orders_for_print, self.rep_name)

            if not result:
                self.logger.warning("فشل في عرض معاينة الطباعة")

            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في تحضير بيانات الطباعة: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحضير بيانات الطباعة: {str(e)}")

    def return_to_warehouse(self, order_id):
        """إرجاع طلب إلى المخزن"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                from db.models import Database
                self.db = Database()
                self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات في return_to_warehouse")

            # الاتصال بقاعدة البيانات باستخدام كائن Database
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            # بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = "UPDATE orders SET status = 'warehouse' WHERE id = %s"
                self.logger.info("استخدام %s للمعاملات مع MySQL")
            else:
                # استخدام ? للمعاملات مع SQLite
                query = "UPDATE orders SET status = 'warehouse' WHERE id = ?"
                self.logger.info("استخدام ? للمعاملات مع SQLite")

            # تنفيذ الاستعلام
            cursor.execute(query, (order_id,))

            # حفظ التغييرات
            conn.commit()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            # تحديث الجدول
            self.load_orders()

            # عرض رسالة نجاح
            QMessageBox.information(self, "تم الإرجاع", "تم إرجاع الطلب إلى المخزن بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إرجاع الطلب إلى المخزن: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إرجاع الطلب إلى المخزن: {str(e)}")

    def return_selected_to_warehouse(self):
        """إرجاع الطلبات المحددة إلى المخزن"""
        selected_orders = self.get_selected_orders()

        if not selected_orders:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد طلب واحد على الأقل")
            return

        # تأكيد العملية
        result = QMessageBox.question(
            self,
            "تأكيد الإرجاع",
            f"هل أنت متأكد من إرجاع {len(selected_orders)} طلب إلى المخزن؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if result == QMessageBox.Yes:
            try:
                # التحقق من وجود اتصال بقاعدة البيانات
                if self.db is None:
                    # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                    from db.models import Database
                    self.db = Database()
                    self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات في return_selected_to_warehouse")

                # الاتصال بقاعدة البيانات باستخدام كائن Database
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                # بناء الاستعلام بناءً على نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query = "UPDATE orders SET status = 'warehouse' WHERE id = %s"
                    self.logger.info("استخدام %s للمعاملات مع MySQL")
                else:
                    # استخدام ? للمعاملات مع SQLite
                    query = "UPDATE orders SET status = 'warehouse' WHERE id = ?"
                    self.logger.info("استخدام ? للمعاملات مع SQLite")

                # تحديث حالة الطلبات المحددة مع الحفاظ على جميع المعلومات الأخرى
                for order_id in selected_orders:
                    cursor.execute(query, (order_id,))

                # حفظ التغييرات
                conn.commit()

                # إغلاق الاتصال (إذا كان SQLite)
                if db_type != 'mysql':
                    conn.close()

                # تحديث الجدول
                self.load_orders()

                # عرض رسالة نجاح
                QMessageBox.information(self, "تم الإرجاع", f"تم إرجاع {len(selected_orders)} طلب إلى المخزن بنجاح")

            except Exception as e:
                self.logger.error(f"خطأ في إرجاع الطلبات إلى المخزن: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إرجاع الطلبات إلى المخزن: {str(e)}")

    def mark_selected_as_completed(self):
        """تحويل الطلبات المحددة إلى مكتملة"""
        selected_orders = self.get_selected_orders()

        if not selected_orders:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد طلب واحد على الأقل")
            return

        # تأكيد العملية
        result = QMessageBox.question(
            self,
            "تأكيد الإكمال",
            f"هل أنت متأكد من تحويل {len(selected_orders)} طلب إلى مكتملة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if result == QMessageBox.Yes:
            try:
                # التحقق من وجود اتصال بقاعدة البيانات
                if self.db is None:
                    # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                    from db.models import Database
                    self.db = Database()
                    self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات في mark_selected_as_completed")

                # الاتصال بقاعدة البيانات باستخدام كائن Database
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                # تحديث حالة الطلبات المحددة
                import datetime
                current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                updated_count = 0

                for order_id in selected_orders:
                    # التحقق من حالة الطلب - بناء الاستعلام بناءً على نوع قاعدة البيانات
                    if db_type == 'mysql':
                        status_query = "SELECT status FROM orders WHERE id = %s"
                    else:
                        status_query = "SELECT status FROM orders WHERE id = ?"

                    cursor.execute(status_query, (order_id,))
                    result = cursor.fetchone()

                    # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
                    if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                        status = result['status']
                    elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                        status = result[0]
                    else:
                        self.logger.warning(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                        continue

                    if status in ['distribution', 'distribution_delivered']:
                        # بناء الاستعلام بناءً على نوع قاعدة البيانات
                        if db_type == 'mysql':
                            update_query = "UPDATE orders SET status = 'completed', completion_date = %s WHERE id = %s"
                            cursor.execute(update_query, (current_date, order_id))
                        else:
                            update_query = "UPDATE orders SET status = 'completed', completion_date = ? WHERE id = ?"
                            cursor.execute(update_query, (current_date, order_id))

                        updated_count += 1
                    else:
                        # إذا كان الطلب ليس في حالة توزيع أو واصل، نتخطاه ونستمر
                        continue

                # حفظ التغييرات
                conn.commit()

                # إغلاق الاتصال (إذا كان SQLite)
                if db_type != 'mysql':
                    conn.close()

                # تحديث الجدول
                self.load_orders()

                # عرض رسالة نجاح
                QMessageBox.information(self, "تم الإكمال", f"تم تحويل {updated_count} طلب إلى مكتملة بنجاح")

            except Exception as e:
                self.logger.error(f"خطأ في تحويل الطلبات إلى مكتملة: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحويل الطلبات إلى مكتملة: {str(e)}")

    # إضافة وظيفة استلام الطلبات من المخزن
    @staticmethod
    def receive_orders_from_warehouse(rep_id):
        """استلام الطلبات من المخزن وتحويلها للمندوب

        هذه الدالة يتم استدعاؤها من قبل warehouse.py لتحويل الطلبات للمندوب
        """
        try:
            if not rep_id:
                return (False, "لم يتم تحديد المندوب")

            # إنشاء اتصال بقاعدة البيانات
            from db.models import Database
            db = Database()
            conn = db.get_connection()
            cursor = conn.cursor()

            # تحديد نوع قاعدة البيانات
            db_type = getattr(db, 'db_type', 'sqlite')

            # استعلام عن عدد الطلبات التي سيتم تحويلها
            if db_type == 'mysql':
                query = """
                    SELECT COUNT(*) FROM orders
                    WHERE pickup_representative_id = %s AND status IN ('warehouse', 'مخزن', 'في المخزن')
                """
            else:
                query = """
                    SELECT COUNT(*) FROM orders
                    WHERE pickup_representative_id = ? AND status IN ('warehouse', 'مخزن', 'في المخزن')
                """

            cursor.execute(query, (rep_id,))
            result = cursor.fetchone()

            # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
            if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                count_to_update = list(result.values())[0]
            elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                count_to_update = result[0]
            else:
                logger = logging.getLogger('rep_orders_view')
                logger.warning(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                count_to_update = 0

            if count_to_update == 0:
                conn.close()
                return (False, "لا توجد طلبات في المخزن لهذا المندوب")

            # تحديث حالة الطلب إلى 'distribution'
            if db_type == 'mysql':
                update_query = """
                    UPDATE orders
                    SET status = 'distribution'
                    WHERE pickup_representative_id = %s AND status IN ('warehouse', 'مخزن', 'في المخزن')
                """
            else:
                update_query = """
                    UPDATE orders
                    SET status = 'distribution'
                    WHERE pickup_representative_id = ? AND status IN ('warehouse', 'مخزن', 'في المخزن')
                """

            cursor.execute(update_query, (rep_id,))
            affected_rows = cursor.rowcount
            conn.commit()

            # الحصول على اسم المندوب من قاعدة البيانات
            if db_type == 'mysql':
                rep_query = "SELECT name FROM representatives WHERE id = %s"
            else:
                rep_query = "SELECT name FROM representatives WHERE id = ?"

            cursor.execute(rep_query, (rep_id,))
            rep_result = cursor.fetchone()

            # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
            if isinstance(rep_result, dict):  # في حالة استخدام row_factory مع SQLite
                rep_name = rep_result['name'] if rep_result else "غير معروف"
            elif isinstance(rep_result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                rep_name = rep_result[0] if rep_result else "غير معروف"
            else:
                rep_name = "غير معروف"

            conn.close()

            # سجل الحدث
            logger = logging.getLogger('rep_orders_view')
            logger.info(f"تم تحويل {affected_rows} طلب إلى المندوب {rep_name} (ID: {rep_id})")

            if affected_rows > 0:
                return (True, f"تم تحويل {affected_rows} طلب بنجاح")
            else:
                return (False, "لم يتم تحويل أي طلبات")

        except Exception as e:
            logger = logging.getLogger('rep_orders_view')
            logger.error(f"خطأ في استلام الطلبات من المخزن: {str(e)}")
            return (False, f"حدث خطأ أثناء تحويل الطلبات: {str(e)}")

    def show_order_details(self, order_id):
        """عرض تفاصيل الطلب في نافذة منبثقة"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                from db.models import Database
                self.db = Database()
                self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات في show_order_details")

            # الاتصال بقاعدة البيانات باستخدام كائن Database
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            # استعلام للحصول على تفاصيل الطلب مع المساحة الكلية
            if db_type == 'mysql':
                query = """
                SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                       o.total_price, o.status, o.created_at, o.total_area,
                       (SELECT GROUP_CONCAT(CONCAT(length, 'x', width, '=', total_area) SEPARATOR ', ')
                        FROM carpet_dimensions
                        WHERE order_id = o.id) as carpet_dimensions,
                       (SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = o.id) as total_carpet_area,
                       (SELECT name FROM representatives WHERE id = o.pickup_representative_id) AS delivery_rep_name,
                       o.notes
                FROM orders o
                WHERE o.id = %s
                """
            else:
                query = """
                SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                       o.total_price, o.status, o.created_at, o.total_area,
                       (SELECT GROUP_CONCAT(length || 'x' || width || '=' || total_area, ', ')
                        FROM carpet_dimensions
                        WHERE order_id = o.id) as carpet_dimensions,
                       (SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = o.id) as total_carpet_area,
                       (SELECT name FROM representatives WHERE id = o.pickup_representative_id) AS delivery_rep_name,
                       o.notes
                FROM orders o
                WHERE o.id = ?
                """

            # تنفيذ الاستعلام
            cursor.execute(query, (order_id,))
            order = cursor.fetchone()

            if not order:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")
                conn.close()
                return

            # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
            if isinstance(order, dict):  # في حالة استخدام row_factory مع SQLite
                order_id = order['id']
                receipt_number = order['receipt_number']
                address = order['address']
                phone = order['phone']
                phone2 = order['phone2']
                total_price = order['total_price']
                status = order['status']
                created_at_str = order['created_at']
                total_area = order['total_area']
                carpet_dimensions = order['carpet_dimensions']
                total_carpet_area = order['total_carpet_area']
                delivery_rep_name = order['delivery_rep_name']
                notes = order['notes']
            elif isinstance(order, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                order_id = order[0]
                receipt_number = order[1]
                address = order[2]
                phone = order[3]
                phone2 = order[4]
                total_price = order[5]
                status = order[6]
                created_at_str = order[7]
                total_area = order[8]
                carpet_dimensions = order[9]
                total_carpet_area = order[10]
                delivery_rep_name = order[11]
                notes = order[12]
            else:
                self.logger.warning(f"نوع غير معروف: {type(order)}, محتوى: {order}")
                QMessageBox.warning(self, "تحذير", "حدث خطأ في قراءة بيانات الطلب")
                conn.close()
                return

            # تنسيق البيانات للعرض بطريقة آمنة
            try:
                price = float(total_price or 0)
                formatted_price = f"{int(price):,}".replace(",", "٬") + " د.ع"
                total_area_value = float(total_carpet_area or total_area or 0)
                formatted_area = f"{total_area_value:.2f} م²"
            except (ValueError, TypeError):
                formatted_price = "0 د.ع"
                formatted_area = "0.00 م²"

            # تحضير نص تفاصيل أبعاد السجاد
            dimensions_text = ""
            if carpet_dimensions:
                dimensions_list = carpet_dimensions.split(", ")
                for idx, dim in enumerate(dimensions_list, 1):
                    parts = dim.split("=")
                    if len(parts) == 2:
                        dimensions_text += f"السجادة {idx}: {parts[0]}م, المساحة = {float(parts[1]):.2f}م²<br>"
            else:
                dimensions_text = "لا توجد أبعاد مسجلة"

            # تحويل التاريخ إلى تنسيق مقروء
            if created_at_str:
                try:
                    created_at = QDateTime.fromString(str(created_at_str), "yyyy-MM-dd HH:mm:ss")
                    formatted_date = created_at.toString("yyyy-MM-dd")
                except:
                    formatted_date = str(created_at_str)
            else:
                formatted_date = "غير معروف"

            # إنشاء رسالة تفاصيل الطلب
            details = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial; font-size: 12pt; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                    th, td {{ padding: 8px; border: 1px solid #ddd; text-align: right; }}
                    th {{ background-color: #f5f5f5; font-weight: bold; }}
                </style>
            </head>
            <body>
                <h2 style="text-align: center;">تفاصيل الطلب</h2>
                <table>
                    <tr>
                        <th>رقم التسلسل:</th>
                        <td>{order_id}</td>
                    </tr>
                    <tr>
                        <th>رقم الوصل:</th>
                        <td>{receipt_number or ''}</td>
                    </tr>
                    <tr>
                        <th>العنوان:</th>
                        <td>{address or ''}</td>
                    </tr>
                    <tr>
                        <th>رقم الهاتف:</th>
                        <td>
                            {phone or ''}
                            {f"<br>{phone2}" if phone2 else ''}
                        </td>
                    </tr>
                    <tr>
                        <th>المساحة الكلية:</th>
                        <td>{formatted_area}</td>
                    </tr>
                    <tr>
                        <th>تفاصيل الأبعاد:</th>
                        <td>{dimensions_text}</td>
                    </tr>
                    <tr>
                        <th>السعر الإجمالي:</th>
                        <td>{formatted_price}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء:</th>
                        <td>{formatted_date}</td>
                    </tr>
                    <tr>
                        <th>الحالة:</th>
                        <td>{status or ''}</td>
                    </tr>
                    <tr>
                        <th>مندوب التوصيل:</th>
                        <td>{delivery_rep_name or 'غير محدد'}</td>
                    </tr>
                    <tr>
                        <th>ملاحظات:</th>
                        <td>{notes or ''}</td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # عرض رسالة التفاصيل
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تفاصيل الطلب")
            msg_box.setTextFormat(Qt.RichText)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setText(details)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()

            # إغلاق الاتصال
            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل الطلب: {str(e)}")

    def mark_as_completed(self, order_id):
        """تحويل الطلب إلى مكتمل (واصل)"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                # إنشاء اتصال بقاعدة البيانات إذا لم يكن موجودًا
                from db.models import Database
                self.db = Database()
                self.logger.info("تم إنشاء اتصال جديد بقاعدة البيانات في mark_as_completed")

            # الاتصال بقاعدة البيانات باستخدام كائن Database
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            # التحقق من حالة الطلب - بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                status_query = "SELECT status FROM orders WHERE id = %s"
            else:
                status_query = "SELECT status FROM orders WHERE id = ?"

            cursor.execute(status_query, (order_id,))
            result = cursor.fetchone()

            # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
            if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                status = result['status']
            elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                status = result[0]
            else:
                self.logger.warning(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                if db_type != 'mysql':
                    conn.close()
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد حالة الطلب")
                return

            if status not in ['distribution', 'distribution_delivered']:
                QMessageBox.warning(self, "تحذير", "لا يمكن تحويل هذا الطلب إلى مكتمل بسبب حالته الحالية")
                if db_type != 'mysql':
                    conn.close()
                return

            # تحديث حالة الطلب إلى مكتمل وتحديث تاريخ الإكمال بتاريخ اليوم الحالي
            import datetime
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                update_query = "UPDATE orders SET status = 'completed', completion_date = %s WHERE id = %s"
                cursor.execute(update_query, (current_date, order_id))
            else:
                update_query = "UPDATE orders SET status = 'completed', completion_date = ? WHERE id = ?"
                cursor.execute(update_query, (current_date, order_id))

            # حفظ التغييرات
            conn.commit()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            # تحديث الجدول
            self.load_orders()

            # عرض رسالة نجاح
            QMessageBox.information(self, "تم التحويل", "تم تحويل الطلب إلى مكتمل (واصل) بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في تحويل الطلب إلى مكتمل: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحويل الطلب إلى مكتمل: {str(e)}")

    def show_dimensions_dialog(self, order_id):
        """عرض نافذة إدخال أبعاد السجاد"""
        try:
            conn = sqlite3.connect("carpet_cleaning.db")
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # استعلام للحصول على عدد السجاد وأبعاده
            cursor.execute("SELECT carpet_count FROM orders WHERE id = ?", (order_id,))
            row = cursor.fetchone()

            if not row:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")
                conn.close()
                return

            carpet_count = row['carpet_count'] or 1

            # استعلام للحصول على أبعاد السجاد الحالية
            cursor.execute("""
                SELECT length, width, total_area FROM carpet_dimensions
                WHERE order_id = ? ORDER BY id
            """, (order_id,))

            existing_dimensions = [dict(row) for row in cursor.fetchall()]

            # إنشاء قائمة بالأبعاد الحالية بالتنسيق المطلوب لنافذة الأبعاد
            formatted_dimensions = []
            for dim in existing_dimensions:
                formatted_dimensions.append({
                    'length': dim['length'],
                    'width': dim['width']
                })

            # إنشاء وعرض نافذة إدخال الأبعاد
            dialog = CarpetDimensionsDialog(self, carpet_count, formatted_dimensions)

            if dialog.exec_() == QDialog.Accepted:
                # الحصول على الأبعاد الجديدة
                new_dimensions = dialog.get_dimensions()

                # حذف الأبعاد القديمة
                cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = ?", (order_id,))

                # حساب المساحة الكلية
                total_area = 0

                # إضافة الأبعاد الجديدة
                for dim in new_dimensions:
                    length = dim['length']
                    width = dim['width']
                    area = length * width
                    total_area += area

                    cursor.execute("""
                        INSERT INTO carpet_dimensions (order_id, length, width, total_area)
                        VALUES (?, ?, ?, ?)
                    """, (order_id, length, width, area))

                # تقريب المساحة الكلية حسب القاعدة المطلوبة
                decimal_part = total_area - int(total_area)
                if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
                    total_area = int(total_area) + 1
                else:  # إذا كان الكسر أقل من 0.5
                    total_area = int(total_area)

                # تحديث المساحة الكلية في الطلب
                cursor.execute("SELECT price_per_meter, blanket_price FROM settings LIMIT 1")
                settings = cursor.fetchone()
                price_per_meter = settings['price_per_meter'] if settings else 0
                blanket_price = settings['blanket_price'] if settings else 5000

                # الحصول على عدد البطانية
                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite') if hasattr(self, 'db') else 'sqlite'

                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    blanket_query = "SELECT blanket_count FROM orders WHERE id = %s"
                else:
                    # استخدام ? للمعاملات مع SQLite
                    blanket_query = "SELECT blanket_count FROM orders WHERE id = ?"

                cursor.execute(blanket_query, (order_id,))
                blanket_row = cursor.fetchone()
                blanket_count = blanket_row['blanket_count'] if blanket_row else 0

                # حساب السعر الإجمالي
                carpet_price = total_area * price_per_meter
                blanket_total_price = blanket_count * blanket_price
                total_price = carpet_price + blanket_total_price

                # تحديث السعر والمساحة في الطلب
                cursor.execute("""
                    UPDATE orders SET total_area = ?, total_price = ?
                    WHERE id = ?
                """, (total_area, total_price, order_id))

                conn.commit()
                print(f"تم تحديث أبعاد السجاد بنجاح.")
                print(f"تم حذف {len(existing_dimensions)} من أبعاد السجاد للطلب رقم {order_id}")
                print(f"تم إضافة بُعد سجادة جديد (الطول: {length}, العرض: {width}, المساحة: {total_area}) للطلب رقم {order_id}")
                print(f"السعر الإجمالي المحدث: {total_price} (سجاد: {carpet_price} + بطانية: {blanket_total_price})")

                # تحديث الجدول
                self.load_orders()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إدخال أبعاد السجاد: {str(e)}")

    def edit_order(self, order_id):
        """تعديل بيانات الطلب"""
        try:
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect("carpet_cleaning.db")
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # استعلام للحصول على بيانات الطلب
            cursor.execute("""
                SELECT id, receipt_number, carpet_count, blanket_count
                FROM orders WHERE id = ?
            """, (order_id,))

            order = cursor.fetchone()

            if not order:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")
                conn.close()
                return

            # إنشاء نموذج التعديل
            edit_dialog = QDialog(self)
            edit_dialog.setWindowTitle("تعديل الطلب")
            edit_dialog.setMinimumWidth(300)

            layout = QVBoxLayout()

            # رقم الوصل
            receipt_layout = QHBoxLayout()
            receipt_label = QLabel("رقم الوصل:")
            receipt_input = QLineEdit()
            receipt_input.setText(order['receipt_number'] or "")
            receipt_layout.addWidget(receipt_label)
            receipt_layout.addWidget(receipt_input)
            layout.addLayout(receipt_layout)

            # مساحة السجاد
            carpet_area_layout = QHBoxLayout()
            carpet_area_label = QLabel("مساحة السجاد:")
            carpet_area_input = QSpinBox()
            carpet_area_input.setMinimum(1)
            carpet_area_input.setValue(order['carpet_count'] or 1)
            carpet_area_layout.addWidget(carpet_area_label)
            carpet_area_layout.addWidget(carpet_area_input)

            # زر إدخال الأبعاد
            dimensions_btn = QPushButton("إدخال الأبعاد")
            dimensions_btn.clicked.connect(lambda: self.show_dimensions_dialog(order_id))
            carpet_area_layout.addWidget(dimensions_btn)

            layout.addLayout(carpet_area_layout)

            # عدد البطانية
            blanket_count_layout = QHBoxLayout()
            blanket_count_label = QLabel("عدد البطانية:")
            blanket_count_input = QSpinBox()
            blanket_count_input.setMinimum(0)
            blanket_count_input.setValue(order['blanket_count'] or 0)
            blanket_count_layout.addWidget(blanket_count_label)
            blanket_count_layout.addWidget(blanket_count_input)
            layout.addLayout(blanket_count_layout)

            # أزرار الحفظ والإلغاء
            buttons_layout = QHBoxLayout()
            save_btn = QPushButton("حفظ")
            cancel_btn = QPushButton("إلغاء")

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            layout.addLayout(buttons_layout)

            edit_dialog.setLayout(layout)

            # ربط الأحداث
            save_btn.clicked.connect(lambda: self.save_edited_order(
                order_id,
                receipt_input.text(),
                carpet_area_input.value(),
                blanket_count_input.value(),
                edit_dialog
            ))
            cancel_btn.clicked.connect(edit_dialog.reject)

            # عرض النافذة
            edit_dialog.exec_()

            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في تعديل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الطلب: {str(e)}")
