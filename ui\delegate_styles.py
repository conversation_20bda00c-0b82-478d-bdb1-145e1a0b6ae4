"""أنماط التصميم لنافذة استلام المندوبين"""

from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QColor
from PyQt5.QtWidgets import QStyle, QApplication

# تعريف الألوان الأساسية
COLORS = {
    'primary': '#2c3e50',    # أزرق داكن
    'secondary': '#34495e',  # أزرق رمادي
    'success': '#27ae60',    # أخضر
    'info': '#3498db',       # أزرق فاتح
    'warning': '#f39c12',    # برتقالي
    'danger': '#e74c3c',     # أحمر
    'light': '#ecf0f1',      # أبيض مائل للرمادي
    'dark': '#2c3e50',       # أزرق داكن
    'white': '#ffffff',      # أبيض
    'muted': '#95a5a6',      # رمادي
}

# أنماط النافذة الرئيسية
DELEGATE_WINDOW_STYLE = f"""
    QDialog {{
        background-color: {COLORS['light']};
        border: 1px solid {COLORS['muted']};
        border-radius: 5px;
    }}
    
    QLabel {{
        color: {COLORS['dark']};
        font-size: 11pt;
        line-height: 1;
        padding: 2px;
    }}
    
    QGroupBox {{
        font-size: 12pt;
        font-weight: bold;
        border: 1px solid {COLORS['muted']};
        border-radius: 5px;
        margin-top: 5px;
        padding-top: 10px;
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 5px;
        color: {COLORS['primary']};
    }}
"""

# أنماط الأزرار
BUTTON_STYLE = f"""
    QPushButton {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 11pt;
        min-width: 80px;
        min-height: 30px;
    }}
    
    QPushButton:hover {{
        background-color: #2980b9;
    }}
    
    QPushButton:pressed {{
        background-color: #1f6aa5;
    }}
    
    QPushButton:disabled {{
        background-color: {COLORS['muted']};
        color: #7f8c8d;
    }}
"""

# أنماط حقول الإدخال
INPUT_STYLE = f"""
    QLineEdit, QSpinBox, QDoubleSpinBox {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['muted']};
        border-radius: 4px;
        padding: 8px;
        font-size: 11pt;
        min-height: 25px;
    }}
    
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
        border: 2px solid {COLORS['info']};
    }}
    
    QSpinBox::up-button, QDoubleSpinBox::up-button {{
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 20px;
        border-left: 1px solid {COLORS['muted']};
        border-bottom: 1px solid {COLORS['muted']};
        border-top-right-radius: 4px;
        background-color: {COLORS['light']};
    }}
    
    QSpinBox::down-button, QDoubleSpinBox::down-button {{
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 20px;
        border-left: 1px solid {COLORS['muted']};
        border-bottom-right-radius: 4px;
        background-color: {COLORS['light']};
    }}
    
    QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
    QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {{
        background-color: #bdc3c7;
    }}
"""

# أنماط الجدول
TABLE_STYLE = f"""
    QTableWidget {{
        background-color: {COLORS['white']};
        alternate-background-color: {COLORS['light']};
        border: 1px solid {COLORS['muted']};
        border-radius: 4px;
        gridline-color: #d0d0d0;
        font-size: 11pt;
    }}
    
    QTableWidget::item {{
        padding: 5px;
        border-bottom: 1px solid #ecf0f1;
    }}
    
    QTableWidget::item:selected {{
        background-color: #3498db;
        color: {COLORS['white']};
    }}
    
    QHeaderView::section {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        padding: 8px;
        font-size: 11pt;
        font-weight: bold;
        border: none;
    }}
    
    QHeaderView::section:checked {{
        background-color: #2980b9;
    }}
    
    QTableWidget QTableCornerButton::section {{
        background-color: {COLORS['primary']};
        border: none;
    }}
"""

# أنماط شريط التمرير
SCROLLBAR_STYLE = f"""
    QScrollBar:vertical {{
        border: none;
        background-color: {COLORS['light']};
        width: 12px;
        margin: 0px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['muted']};
        min-height: 30px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: #7f8c8d;
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    QScrollBar:horizontal {{
        border: none;
        background-color: {COLORS['light']};
        height: 12px;
        margin: 0px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {COLORS['muted']};
        min-width: 30px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: #7f8c8d;
    }}
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
        width: 0px;
    }}
"""

# أنماط مربعات الاختيار
CHECKBOX_STYLE = f"""
    QCheckBox {{
        spacing: 10px;
        font-size: 11pt;
        color: {COLORS['dark']};
    }}
    
    QCheckBox::indicator {{
        width: 20px;
        height: 20px;
        border: 2px solid {COLORS['muted']};
        border-radius: 4px;
    }}
    
    QCheckBox::indicator:unchecked {{
        background-color: {COLORS['white']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COLORS['success']};
        border-color: {COLORS['success']};
    }}
    
    QCheckBox::indicator:hover {{
        border-color: {COLORS['info']};
    }}
"""

# أنماط حقول البحث
SEARCH_STYLE = f"""
    QLineEdit {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['muted']};
        border-radius: 4px;
        padding: 8px 8px 8px 30px;
        font-size: 11pt;
        min-height: 25px;
        background-position: left center;
        background-repeat: no-repeat;
        background-origin: content;
        background-clip: padding;
    }}
    
    QLineEdit:focus {{
        border: 2px solid {COLORS['info']};
    }}
"""

# أنماط مربعات الحوار
DIALOG_STYLE = f"""
    QDialog {{
        background-color: {COLORS['light']};
        border: 1px solid {COLORS['muted']};
        border-radius: 5px;
    }}
    
    QLabel {{
        color: {COLORS['dark']};
        font-size: 11pt;
    }}
"""

# دالة لإنشاء نمط زر بلون محدد
def get_button_style(color):
    """إنشاء ستايل للأزرار"""
    return f"""
        QPushButton {{
            background-color: {color};
            color: {COLORS['white']};
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {color}dd;
        }}
        QPushButton:pressed {{
            background-color: {color}ee;
            padding-top: 12px;
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_light']};
            color: {COLORS['gray']};
        }}
    """

# دالة لتعديل لون (تفتيح أو تغميق)
def adjust_color(hex_color, amount):
    # تحويل اللون من صيغة hex إلى RGB
    hex_color = hex_color.lstrip('#')
    r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    # تعديل القيم
    r = max(0, min(255, r + amount))
    g = max(0, min(255, g + amount))
    b = max(0, min(255, b + amount))
    
    # إعادة تحويل إلى صيغة hex
    return f'#{r:02x}{g:02x}{b:02x}'

# دالة لإنشاء أيقونة بحجم محدد
def create_icon(icon_name, size=QSize(24, 24)):
    icon = QIcon.fromTheme(icon_name)
    if icon.isNull():
        # استخدام أيقونات افتراضية من نظام PyQt
        fallback_icons = {
            "document-save": QStyle.SP_DialogSaveButton,
            "document-print": QStyle.SP_DirIcon,
            "document-send": QStyle.SP_ArrowRight,
            "document-edit": QStyle.SP_FileIcon,
            "document-properties": QStyle.SP_FileDialogInfoView,
            "edit-select-all": QStyle.SP_DialogApplyButton,
            "window-close": QStyle.SP_DialogCancelButton
        }
        
        if icon_name in fallback_icons:
            # الحصول على الأيقونة من خلال التطبيق الحالي
            app = QApplication.instance()
            if app:
                style = app.style()
                icon = QIcon(style.standardIcon(fallback_icons[icon_name]))
    
    return icon
