from PyQt5.QtWidgets import (QMain<PERSON>indow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QMessageBox, QCheckBox, QSpacerItem, QSizePolicy,
                            QDialog)  # إضافة QDialog هنا
from PyQt5.QtCore import Qt, QDateTime, QEvent, QDate, QTimer, QObject
from PyQt5.QtGui import QFont, QColor, QIcon
import sqlite3
from db.models import Order, Database
from ui.edit_order_dialog import EditOrderDialog
from datetime import datetime
import logging
from src.rep_orders_view import RepresentativeOrdersWindow

class WarehouseWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("قائمة المخزن")
        self.resize(1320, 700)

        # إضافة متغيرات التتبع
        self.all_selected = False
        self.selected_count = 0
        self.current_row = -1  # متغير لتتبع الصف الحالي

        # إنشاء مؤقت للرسائل المؤقتة
        self.message_timer = QTimer()
        self.message_timer.setSingleShot(True)
        self.message_timer.timeout.connect(self.hide_temp_message)

        # إنشاء تسمية للرسائل المؤقتة
        self.temp_message_label = QLabel(self)
        self.temp_message_label.setAlignment(Qt.AlignCenter)
        self.temp_message_label.setStyleSheet("""
            QLabel {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                font-size: 14pt;
                padding: 10px;
                border-radius: 5px;
            }
        """)
        self.temp_message_label.hide()

        # إنشاء قاعدة البيانات
        self.db = Database()

        # إعداد السجل
        self.logger = logging.getLogger('warehouse')
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # تهيئة الواجهة
        self.init_ui()

        # تحميل الطلبات
        self.load_warehouse_orders()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # عنوان الصفحة
        title_label = QLabel("قائمة المخزن")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; margin-bottom: 10px;")
        main_layout.addWidget(title_label)

        # قسم البحث
        search_layout = QHBoxLayout()

        # قسم البحث - الجزء الأيسر
        search_left_layout = QHBoxLayout()
        search_left_layout.setStretch(1, 8)  # زيادة نسبة امتداد أكبر لحقل البحث
        search_label = QLabel("بحث:")
        search_label.setStyleSheet("font-size: 12pt; font-weight: bold;")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في الطلبات...")
        self.search_input.setMinimumHeight(40)
        self.search_input.setMinimumWidth(400)  # زيادة العرض الأدنى لحقل البحث بشكل أكبر

        self.search_input.textChanged.connect(self.filter_orders)
        # تثبيت معالجة keyPressEvent لحقل البحث
        self.search_input.keyPressEvent = self.search_key_press_event
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 7px;
                border: 1px solid #dcdde1;
                border-radius: 10px;
                font-size: 12pt;
            }
        """)
        search_left_layout.addWidget(search_label)
        search_left_layout.addWidget(self.search_input, 1)  # إضافة معامل امتداد لحقل البحث

        # إضافة تخطيط البحث إلى التخطيط الأفقي الرئيسي
        search_layout.addLayout(search_left_layout)

        # إضافة فاصل مرن بين البحث والأزرار
        search_layout.addSpacerItem(QSpacerItem(10, 10, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # تخطيط أفقي للأزرار (تحديد الكل وتحويل المحدد)
        buttons_layout = QHBoxLayout()

        # زر تحديد/إلغاء تحديد الكل
        self.select_all_btn = QPushButton("تحديد الكل")
        self.select_all_btn.setFixedWidth(120)  # تقليل عرض زر التحديد
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_all_btn.clicked.connect(self.toggle_select_all)
        buttons_layout.addWidget(self.select_all_btn)

        # زر تحويل الطلبات المحددة
        self.transfer_selected_btn = QPushButton("تحويل المحدد")
        self.transfer_selected_btn.setFixedWidth(120)  # تقليل عرض زر التحويل
        self.transfer_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        self.transfer_selected_btn.clicked.connect(lambda: self.transfer_order())
        self.transfer_selected_btn.setToolTip("تحويل الطلبات المحددة إلى المندوب")
        buttons_layout.addWidget(self.transfer_selected_btn)

        # إضافة تخطيط الأزرار إلى التخطيط الأفقي الرئيسي
        search_layout.addLayout(buttons_layout)

        # إضافة عداد الطلبات المحددة
        self.selected_count_label = QLabel("عدد الطلبات المحددة: 0")
        self.selected_count_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 15px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin: 0 10px;
            }
        """)
        search_layout.addWidget(self.selected_count_label)

        # إضافة فاصل مرن
        search_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # إضافة حقل المبلغ الإجمالي
        self.total_amount_label = QLabel("المبلغ الإجمالي: 0 دينار")
        self.total_amount_label.setFixedHeight(35)  # تحديد ارتفاع ثابت
        self.total_amount_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 3px 15px;
                background-color: #f1c40f;
                border: 2px solid #f39c12;
                border-radius: 5px;
                margin: 0 10px;
            }
        """)
        search_layout.addWidget(self.total_amount_label)  # إضافة الحقل مباشرة بدون شرط

        main_layout.addLayout(search_layout)

        # متغير لتتبع حالة التحديد
        self.all_selected = False

        # إنشاء جدول الطلبات
        self.orders_table = QTableWidget()

        # تعيين سلوك التحديد للجدول
        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.SingleSelection)

        # تمكين تظليل الصفوف المتناوبة
        self.orders_table.setAlternatingRowColors(True)

        self.orders_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
                gridline-color: #f5f6fa;
                alternate-background-color: #f2f2f2;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f5f6fa;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        # تعيين أعمدة الجدول
        headers = [
            "تحديد",
            "تسلسل",
            "رقم الوصل",
            "العنوان",
            "رقم الهاتف",
            "عدد السجاد",
            "المساحة الإجمالية",
            "عدد البطانية",
            "نقل",
            "السعر الإجمالي",
            "التاريخ",
            "التفاصيل",
            "التحويل",
            "الحذف"
        ]

        self.orders_table.setColumnCount(len(headers))
        self.orders_table.setHorizontalHeaderLabels(headers)

        # تعيين عرض الأعمدة
        self.orders_table.setColumnWidth(0, 50)   # تحديد
        self.orders_table.setColumnWidth(1, 60)   # تسلسل
        self.orders_table.setColumnWidth(2, 90)   # رقم الوصل
        self.orders_table.setColumnWidth(3, 200)  # العنوان
        self.orders_table.setColumnWidth(4, 120)  # رقم الهاتف
        self.orders_table.setColumnWidth(5, 80)   # عدد السجاد
        self.orders_table.setColumnWidth(6, 100)  # المساحة الإجمالية
        self.orders_table.setColumnWidth(7, 80)   # عدد البطانية
        self.orders_table.setColumnWidth(8, 60)   # نقل
        self.orders_table.setColumnWidth(9, 100)  # السعر الإجمالي
        self.orders_table.setColumnWidth(10, 100) # التاريخ
        self.orders_table.setColumnWidth(11, 80)  # التفاصيل
        self.orders_table.setColumnWidth(12, 80)  # التحويل
        self.orders_table.setColumnWidth(13, 80)  # الحذف

        # تنسيق رأس الجدول
        header = self.orders_table.horizontalHeader()
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #273c75;
                padding: 8px;
                border: none;
                color: white;
                font-weight: bold;
                font-size: 10pt;
            }
        """)

        # تعيين ارتفاع الصفوف
        self.orders_table.verticalHeader().setDefaultSectionSize(40)
        self.orders_table.verticalHeader().setVisible(False)

        # منع تحرير الخلايا
        self.orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تعيين التركيز على الجدول
        self.orders_table.setFocus()

        # إضافة معالج أحداث المفاتيح للجدول
        self.setup_keyboard_navigation()

        main_layout.addWidget(self.orders_table)

    def load_warehouse_orders(self):
        """تحميل الطلبات الموجودة في المخزن"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # استعلام للحصول على الطلبات في المخزن - استخدام القيم المخزنة مباشرة بدلاً من إعادة حسابها
            cursor.execute("""
                SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                       (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                       o.total_area,
                       o.blanket_count,
                       o.total_price,
                       o.created_at,
                       o.has_delivery_fee as has_delivery
                FROM orders o
                WHERE o.status IN ('warehouse', 'في المخزن', 'مخزن')
                ORDER BY o.address ASC
            """)

            orders = cursor.fetchall()

            # تعيين عدد الصفوف
            self.orders_table.setRowCount(0)

            # إضافة الطلبات إلى الجدول
            for i, order in enumerate(orders):
                row_position = self.orders_table.rowCount()
                self.orders_table.insertRow(row_position)

                # إضافة مربع تحديد
                checkbox = QCheckBox()
                # تأكد من أن الإشارة متصلة بشكل صحيح
                checkbox.stateChanged.connect(lambda state, row=row_position: self.checkbox_state_changed(state, row))
                checkbox_container = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_container)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                self.orders_table.setCellWidget(row_position, 0, checkbox_container)

                # تسلسل
                self.orders_table.setItem(row_position, 1, QTableWidgetItem(str(i + 1)))

                # رقم الوصل
                receipt_number = order[1] if order[1] else ""
                receipt_item = QTableWidgetItem(str(receipt_number))
                receipt_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 2, receipt_item)

                # العنوان
                address = order[2] if order[2] else ""
                address_item = QTableWidgetItem(address)
                address_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 3, address_item)

                # رقم الهاتف (مع دعم رقمين)
                phone_widget = QWidget()
                phone_layout = QVBoxLayout(phone_widget)
                phone_layout.setContentsMargins(2, 2, 2, 2)
                phone_layout.setSpacing(2)

                # الهاتف الأساسي
                phone1 = str(order[3]) if order[3] else ""
                if phone1:
                    phone1_label = QLabel(phone1)
                    phone1_label.setAlignment(Qt.AlignCenter)
                    phone1_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone1_label)

                # الهاتف الثاني إذا وجد
                phone2 = str(order[4]) if order[4] else ""
                if phone2:
                    phone2_label = QLabel(phone2)
                    phone2_label.setAlignment(Qt.AlignCenter)
                    phone2_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone2_label)
                    # تعديل ارتفاع الصف ليناسب الرقمين
                    self.orders_table.setRowHeight(row_position, 60)

                self.orders_table.setCellWidget(row_position, 4, phone_widget)

                # عدد السجاد
                carpet_count = order[5] if order[5] else 0
                carpet_item = QTableWidgetItem(str(carpet_count))
                carpet_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 5, carpet_item)

                # المساحة الإجمالية
                total_area = order[6] if order[6] else 0
                area_item = QTableWidgetItem(str(int(total_area)))  # تحويل المساحة إلى رقم صحيح
                area_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 6, area_item)

                # عدد البطانية
                blanket_count = order[7] if order[7] else 0
                blanket_item = QTableWidgetItem(str(blanket_count))
                blanket_item.setTextAlignment(Qt.AlignCenter)  # توسيط عدد البطانيات
                self.orders_table.setItem(row_position, 7, blanket_item)

                # حقل النقل
                has_delivery = order[10] if len(order) > 10 and order[10] is not None else 0
                delivery_item = QTableWidgetItem("تم" if has_delivery > 0 else "")
                delivery_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 8, delivery_item)

                # السعر الإجمالي (مع فواصل كل ثلاة مراتب)
                total_price = order[8] if order[8] else 0
                price_str = "{:,}".format(int(total_price))
                price_item = QTableWidgetItem(price_str)
                price_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row_position, 9, price_item)

                # التاريخ (بدون ساعة تاريخ فقط)
                created_at = order[9]
                if created_at:
                    try:
                        # التحقق من نوع البيانات
                        if isinstance(created_at, str):
                            date_obj = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                        else:
                            # إذا كان كائن datetime بالفعل
                            date_obj = created_at

                        date_str = date_obj.strftime("%Y-%m-%d")
                        self.orders_table.setItem(row_position, 10, QTableWidgetItem(date_str))
                    except Exception as e:
                        # في حالة حدوث خطأ، عرض التاريخ كما هو
                        self.logger.error(f"خطأ في تحويل التاريخ: {str(e)}")
                        self.orders_table.setItem(row_position, 10, QTableWidgetItem(str(created_at)))
                else:
                    self.orders_table.setItem(row_position, 10, QTableWidgetItem(""))

                # زر التفاصيل
                details_btn = QPushButton("التفاصيل")
                details_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        padding: 5px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                details_btn.clicked.connect(lambda checked, order_id=order[0]: self.show_order_details(order_id))
                self.orders_table.setCellWidget(row_position, 11, details_btn)

                # زر التحويل
                transfer_btn = QPushButton("تحويل")
                transfer_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #2ecc71;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        padding: 5px;
                    }
                    QPushButton:hover {
                        background-color: #27ae60;
                    }
                """)
                transfer_btn.clicked.connect(lambda checked, order_id=order[0]: self.transfer_order(order_id))
                self.orders_table.setCellWidget(row_position, 12, transfer_btn)

                # زر الحذف (للمدراء فقط)
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #95a5a6;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        padding: 5px;
                    }
                    QPushButton:hover {
                        background-color: #7f8c8d;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, order_id=order[0]: self.delete_order(order_id))
                self.orders_table.setCellWidget(row_position, 13, delete_btn)

                # تخزين معرف الطلب كبيانات دور
                self.orders_table.setItem(row_position, 1, QTableWidgetItem(str(i + 1)))
                self.orders_table.item(row_position, 1).setData(Qt.UserRole, order[0])

            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الطلبات: {str(e)}")

        # بعد إضافة جميع الطلبات
        self.update_total_amount()

    def setup_keyboard_navigation(self):
        """إعداد التنقل باستخدام لوحة المفاتيح في نافذة المخزن"""
        # تعيين دالة keyPressEvent للنافذة
        self.keyPressEvent = self.window_key_press_event

        # تعيين دالة keyPressEvent للجدول
        import types
        self.orders_table.keyPressEvent = types.MethodType(self.table_key_press_event, self.orders_table)

    def table_key_press_event(self, table, event):
        """معالجة أحداث المفاتيح للجدول"""
        # الحصول على الصف الحالي
        current_row = table.currentRow()

        # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
        if event.key() == Qt.Key_Space and current_row >= 0:
            # تأكد من أن الصف غير مخفي
            if not table.isRowHidden(current_row):
                # الحصول على مربع التحديد وتحديث حالته
                checkbox_container = table.cellWidget(current_row, 0)
                if checkbox_container and checkbox_container.layout():
                    checkbox = checkbox_container.layout().itemAt(0).widget()
                    if isinstance(checkbox, QCheckBox):
                        # تبديل حالة مربع الاختيار
                        checkbox.setChecked(not checkbox.isChecked())
                        # تحديث عرض الجدول
                        table.viewport().update()
                        # تحديث عداد الطلبات المحددة
                        self.update_selected_count()
                        # منع تمرير الحدث للفئة الأساسية
                        event.accept()
                        return

        # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
        QTableWidget.keyPressEvent(table, event)

    def window_key_press_event(self, event):
        """معالجة أحداث المفاتيح للنافذة"""
        # إذا كان التركيز على حقل البحث، نترك معالجة الحدث له
        if self.search_input.hasFocus():
            super().keyPressEvent(event)
            return

        # إذا لم يكن التركيز على الجدول، ننقل التركيز إليه
        if not self.orders_table.hasFocus():
            self.orders_table.setFocus()

        # الحصول على الصف الحالي
        current_row = self.orders_table.currentRow()

        # إذا لم يتم تحديد أي صف، نحدد الصف الأول غير المخفي
        if current_row < 0 and event.key() in [Qt.Key_Up, Qt.Key_Down, Qt.Key_Space]:
            for row in range(self.orders_table.rowCount()):
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    current_row = row
                    self.current_row = row
                    break

        # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
        if event.key() == Qt.Key_Space and current_row >= 0:
            # تأكد من أن الصف غير مخفي
            if not self.orders_table.isRowHidden(current_row):
                # الحصول على مربع التحديد وتحديث حالته
                checkbox_container = self.orders_table.cellWidget(current_row, 0)
                if checkbox_container:
                    # طريقة أخرى للوصول إلى مربع الاختيار
                    checkbox = checkbox_container.layout().itemAt(0).widget()
                    if isinstance(checkbox, QCheckBox):
                        # تبديل حالة مربع الاختيار
                        checkbox.setChecked(not checkbox.isChecked())
                        # تحديث عرض الجدول
                        self.orders_table.viewport().update()
                        # تحديث عداد الطلبات المحددة
                        self.update_selected_count()
                        # منع تمرير الحدث للفئة الأساسية
                        event.accept()
                        return

        # معالجة مفاتيح الأسهم للتنقل بين الصفوف
        elif event.key() == Qt.Key_Up:
            # التنقل للصف السابق غير المخفي
            row = current_row - 1
            while row >= 0:
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    self.current_row = row
                    event.accept()
                    return
                row -= 1

        elif event.key() == Qt.Key_Down:
            # التنقل للصف التالي غير المخفي
            row = current_row + 1
            while row < self.orders_table.rowCount():
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    self.current_row = row
                    event.accept()
                    return
                row += 1

        # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
        super().keyPressEvent(event)

    def search_key_press_event(self, event):
        """معالجة أحداث المفاتيح في حقل البحث"""
        # التحقق من ضغط مفتاح المسافة
        if event.key() == Qt.Key_Space and self.search_input.text().strip():
            search_text = self.search_input.text().strip().lower()

            # البحث عن الطلب المطابق وتحديده
            found_and_selected = False
            for row in range(self.orders_table.rowCount()):
                if self.orders_table.isRowHidden(row):
                    continue

                # البحث في رقم الوصل والعنوان ورقم الهاتف
                for col in [2, 3, 4]:  # رقم الوصل، العنوان، رقم الهاتف
                    if col == 4:  # رقم الهاتف (ويدجت)
                        phone_widget = self.orders_table.cellWidget(row, col)
                        if phone_widget:
                            phone_text = ""
                            for i in range(phone_widget.layout().count()):
                                label = phone_widget.layout().itemAt(i).widget()
                                if isinstance(label, QLabel):
                                    phone_text += label.text().lower() + " "
                            if search_text in phone_text:
                                found_and_selected = True
                                break
                    else:
                        item = self.orders_table.item(row, col)
                        if item and search_text in item.text().lower():
                            found_and_selected = True
                            break

                if found_and_selected:
                    # تحديد الطلب (تفعيل مربع الاختيار)
                    checkbox_widget = self.orders_table.cellWidget(row, 0)
                    if checkbox_widget:
                        checkbox = checkbox_widget.layout().itemAt(0).widget()
                        if isinstance(checkbox, QCheckBox):
                            checkbox.setChecked(True)

                    # مسح حقل البحث للبحث عن طلب جديد
                    self.search_input.clear()

                    # إلغاء تصفية الطلبات لإظهار جميع الطلبات مرة أخرى
                    self.reset_filter()

                    # تحديث عداد الطلبات المحددة بعد تحديد الطلب وإعادة عرض جميع الطلبات
                    self.update_selected_count()
                    break

            # إذا لم يتم العثور على طلب مطابق، عرض رسالة تحذير مؤقتة
            if not found_and_selected:
                self.show_temp_message("لم يتم العثور على طلب مطابق", 2000)

            # منع معالجة الحدث الافتراضي (إضافة مسافة)
            event.accept()
        else:
            # السماح بمعالجة الحدث الافتراضي لباقي المفاتيح
            QLineEdit.keyPressEvent(self.search_input, event)

    def reset_filter(self):
        """إعادة تعيين تصفية الطلبات لإظهار جميع الطلبات"""
        for row in range(self.orders_table.rowCount()):
            self.orders_table.setRowHidden(row, False)

    def filter_orders(self):
        """تصفية الطلبات حسب النص المدخل"""
        search_text = self.search_input.text().lower()

        for row in range(self.orders_table.rowCount()):
            show_row = False

            # البحث في جميع الأعمدة النصية
            for col in [2, 3, 4]:  # رقم الوصل، العنوان، رقم الهاتف
                if col == 4:  # رقم الهاتف (ويدجت)
                    phone_widget = self.orders_table.cellWidget(row, col)
                    if phone_widget:
                        phone_text = ""
                        for i in range(phone_widget.layout().count()):
                            label = phone_widget.layout().itemAt(i).widget()
                            if isinstance(label, QLabel):
                                phone_text += label.text().lower() + " "
                        if search_text in phone_text:
                            show_row = True
                            break
                else:
                    item = self.orders_table.item(row, col)
                    if item and search_text in item.text().lower():
                        show_row = True
                        break

            self.orders_table.setRowHidden(row, not show_row)

    def show_order_details(self, order_id):
        """عرض تفاصيل الطلب"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # استعلام للحصول على تفاصيل الطلب
            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            query = """
                SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2, o.notes,
                       o.created_at, o.status, o.blanket_count, o.total_price,
                       o.has_delivery_fee, o.delivery_fee
                FROM orders o
                WHERE o.id = ?
            """

            # تعديل الاستعلام حسب نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = query.replace('?', '%s')

            cursor.execute(query, (order_id,))

            order = cursor.fetchone()

            if order:
                # استعلام للحصول على أبعاد السجاد
                query = """
                    SELECT cd.id, cd.length, cd.width, cd.total_area
                    FROM carpet_dimensions cd
                    WHERE cd.order_id = ?
                """

                # تعديل الاستعلام حسب نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query = query.replace('?', '%s')

                cursor.execute(query, (order_id,))

                dimensions = cursor.fetchall()

                # استخدام عدد البطانية من قاعدة البيانات
                blanket_count = order[8] if order[8] is not None else 0

                # إنشاء نص التفاصيل
                details = f"رقم الطلب: {order_id}\n"
                details += f"رقم الوصل: {order[1] or 'غير محدد'}\n"
                details += f"العنوان: {order[2]}\n"
                details += f"رقم الهاتف: {order[3]}\n"

                if order[4]:  # رقم الهاتف الثاني
                    details += f"رقم الهاتف البديل: {order[4]}\n"

                details += f"عدد السجاد: {len(dimensions)}\n"
                details += f"عدد البطانية: {blanket_count}\n"

                # إضافة أجور النقل إذا وجدت
                has_delivery_fee = order[10] if len(order) > 10 and order[10] is not None else 0
                delivery_fee = order[11] if len(order) > 11 and order[11] is not None else 0

                if has_delivery_fee and delivery_fee > 0:
                    details += f"أجور النقل: {delivery_fee:,} دينار\n"

                details += f"السعر الإجمالي: {order[9]:,} دينار\n"
                details += f"الملاحظات: {order[5] or 'لا توجد ملاحظات'}\n\n"

                details += "تفاصيل السجاد:\n"
                for i, dim in enumerate(dimensions):
                    details += f"  {i+1}. الطول: {dim[1]} م، العرض: {dim[2]} م، المساحة: {dim[3]:.2f} م²\n"

                # عرض التفاصيل في رسالة
                msg_box = QMessageBox()
                msg_box.setWindowTitle("تفاصيل الطلب")
                msg_box.setText(details)
                msg_box.setIcon(QMessageBox.Information)
                msg_box.exec_()

            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل الطلب: {str(e)}")

    def return_to_rep(self, order_id):
        """إرجاع الطلب إلى قائمة استلام المندوبين"""
        try:
            # التأكيد قبل الإرجاع
            reply = QMessageBox.question(self, "تأكيد الإرجاع",
                                       "هل أنت متأكد من إرجاع هذا الطلب إلى قائمة استلام المندوبين؟",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # تحديث حالة الطلب
                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                query = """
                    UPDATE orders
                    SET status = 'تم النقل للمندوب'
                    WHERE id = ?
                """

                # تعديل الاستعلام حسب نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query = query.replace('?', '%s')

                cursor.execute(query, (order_id,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجاح", "تم إرجاع الطلب إلى قائمة استلام المندوبين بنجاح")

                # إعادة تحميل الطلبات
                self.load_warehouse_orders()

        except Exception as e:
            self.logger.error(f"خطأ في إرجاع الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إرجاع الطلب: {str(e)}")

    def show_temp_message(self, message, duration=2000):
        """عرض رسالة مؤقتة تختفي بعد فترة زمنية محددة"""
        # تعيين نص الرسالة
        self.temp_message_label.setText(message)

        # تحديد موقع الرسالة في وسط النافذة
        width = self.width() // 2
        height = self.height() // 2
        self.temp_message_label.setGeometry(width - 200, height - 50, 400, 100)

        # إظهار الرسالة
        self.temp_message_label.show()
        self.temp_message_label.raise_()

        # بدء المؤقت لإخفاء الرسالة بعد المدة المحددة
        self.message_timer.start(duration)

    def hide_temp_message(self):
        """إخفاء الرسالة المؤقتة"""
        self.temp_message_label.hide()

    def transfer_order(self, order_id=None):
        """تحويل الطلب أو الطلبات المحددة إلى قائمة توزيع المندوبين"""
        try:
            # التحقق من تحديد طلب على الأقل (إذا تم استدعاء الدالة بدون معرف طلب)
            if order_id is None and self.selected_count == 0:
                self.show_temp_message("الرجاء تحديد طلب واحد على الأقل للتحويل")
                return

            # استدعاء نافذة توزيع المندوبين لاختيار المندوب
            from src.delegate_distribution import DistributionWindow
            distribution_window = DistributionWindow(self)

            # الحصول على معرف المندوب واسمه
            rep_id, rep_name = distribution_window.add_new_representative()

            # إذا لم يتم اختيار مندوب، إلغاء العملية
            if not rep_id:
                return

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # إذا تم تمرير معرف طلب محدد، قم بتحويل هذا الطلب فقط
            if order_id:
                # تحديث حالة الطلب إلى 'distribution' بدلاً من إضافته مرة أخرى إلى قائمة الطلبات الواردة
                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                query = """
                    UPDATE orders
                    SET status = 'distribution', pickup_representative_id = ?
                    WHERE id = ?
                """

                # تعديل الاستعلام حسب نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query = query.replace('?', '%s')

                cursor.execute(query, (rep_id, order_id))

                conn.commit()
                self.show_temp_message(f"تم تحويل الطلب إلى المندوب {rep_name} بنجاح")

            # وإلا، قم بتحويل جميع الطلبات المحددة
            else:
                # جمع معرفات الطلبات المحددة
                selected_order_ids = []
                for row in range(self.orders_table.rowCount()):
                    if not self.orders_table.isRowHidden(row):
                        checkbox_widget = self.orders_table.cellWidget(row, 0)
                        if checkbox_widget and checkbox_widget.layout():
                            checkbox = checkbox_widget.layout().itemAt(0).widget()
                            if isinstance(checkbox, QCheckBox) and checkbox.isChecked():
                                # الحصول على معرف الطلب من بيانات الصف
                                order_id_item = self.orders_table.item(row, 1)
                                if order_id_item:
                                    order_id = order_id_item.data(Qt.UserRole)
                                    selected_order_ids.append(order_id)

                # تحديث حالة الطلبات المحددة
                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                query = """
                    UPDATE orders
                    SET status = 'distribution', pickup_representative_id = ?
                    WHERE id = ?
                """

                # تعديل الاستعلام حسب نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query = query.replace('?', '%s')

                for order_id in selected_order_ids:
                    cursor.execute(query, (rep_id, order_id))

                conn.commit()

                # عرض رسالة نجاح بالصيغة العربية الصحيحة
                count = len(selected_order_ids)
                if count == 1:
                    self.show_temp_message(f"تم تحويل طلب واحد إلى المندوب {rep_name} بنجاح")
                elif count == 2:
                    self.show_temp_message(f"تم تحويل طلبين إلى المندوب {rep_name} بنجاح")
                elif count >= 3 and count <= 10:
                    self.show_temp_message(f"تم تحويل {count} طلبات إلى المندوب {rep_name} بنجاح")
                else:
                    self.show_temp_message(f"تم تحويل {count} طلباً إلى المندوب {rep_name} بنجاح")

            conn.close()

            # إعادة تحميل الطلبات
            self.load_warehouse_orders()

        except Exception as e:
            self.logger.error(f"خطأ في تحويل الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحويل الطلب: {str(e)}")

    def delete_order(self, order_id):
        """حذف الطلب (للمدراء فقط)"""
        try:
            # التحقق من تحديد طلب على الأقل
            if self.selected_count == 0:
                # عرض رسالة تحذير مؤقتة
                self.show_temp_message("الرجاء تحديد طلب واحد على الأقل للحذف")
                return

            # التحقق من صلاحيات المستخدم (للمدراء فقط)
            # هنا يمكن إضافة التحقق من صلاحيات المستخدم

            # التأكيد قبل الحذف
            reply = QMessageBox.warning(self, "تأكيد الحذف",
                                      "هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذه العملية.",
                                      QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # حذف الطلب وجميع البيانات المرتبطة به
                # تحديد نوع قاعدة البيانات
                db_type = getattr(self.db, 'db_type', 'sqlite')

                # تحضير الاستعلامات
                query1 = "DELETE FROM carpet_dimensions WHERE order_id = ?"
                query2 = "DELETE FROM orders WHERE id = ?"

                # تعديل الاستعلامات حسب نوع قاعدة البيانات
                if db_type == 'mysql':
                    # استخدام %s للمعاملات مع MySQL
                    query1 = query1.replace('?', '%s')
                    query2 = query2.replace('?', '%s')

                cursor.execute(query1, (order_id,))
                # لا نحتاج لحذف البطانيات لأن جدول blankets غير موجود
                cursor.execute(query2, (order_id,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجاح", "تم حذف الطلب بنجاح")

                # إعادة تحميل الطلبات
                self.load_warehouse_orders()

        except Exception as e:
            self.logger.error(f"خطأ في حذف الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الطلب: {str(e)}")

    def toggle_select_all(self):
        """تبديل حالة تحديد جميع الطلبات"""
        try:
            # تبديل حالة التحديد
            self.all_selected = not self.all_selected

            # تغيين نص الزر بناءً على الحالة
            if self.all_selected:
                self.select_all_btn.setText("إلغاء تحديد الكل")
                self.select_all_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px 15px;
                        font-size: 12pt;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
            else:
                self.select_all_btn.setText("تحديد الكل")
                self.select_all_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px 15px;
                        font-size: 12pt;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)

            # تسجيل معلومات التصحيح
            self.logger.info(f"تبديل حالة تحديد الكل إلى: {self.all_selected}")

            # تحديد أو إلغاء تحديد جميع الصفوف
            # منع تحديث العداد لكل مربع اختيار على حدة
            self.blockSignals(True)

            selected_count = 0
            for row in range(self.orders_table.rowCount()):
                # تجاهل الصفوف المخفية (في حالة البحث)
                if not self.orders_table.isRowHidden(row):
                    checkbox_widget = self.orders_table.cellWidget(row, 0)
                    if checkbox_widget and checkbox_widget.layout():
                        checkbox_item = checkbox_widget.layout().itemAt(0)
                        if checkbox_item and checkbox_item.widget():
                            checkbox = checkbox_item.widget()
                            if isinstance(checkbox, QCheckBox):
                                # منع إطلاق إشارات أثناء التغيير الجماعي
                                checkbox.blockSignals(True)
                                checkbox.setChecked(self.all_selected)
                                checkbox.blockSignals(False)
                                if self.all_selected:
                                    selected_count += 1

            # إعادة تفعيل الإشارات
            self.blockSignals(False)

            # تحديث عداد الطلبات المحددة مرة واحدة بعد الانتهاء من تحديد/إلغاء تحديد الكل
            if selected_count == 0:
                self.logger.info("لم يتم تحديد أي طلب من خلال زر تحديد الكل")
            elif selected_count == 1:
                self.logger.info("تم تحديد طلب واحد من خلال زر تحديد الكل")
            elif selected_count == 2:
                self.logger.info("تم تحديد طلبان من خلال زر تحديد الكل")
            elif selected_count >= 3 and selected_count <= 10:
                self.logger.info(f"تم تحديد {selected_count} طلبات من خلال زر تحديد الكل")
            else:
                self.logger.info(f"تم تحديد {selected_count} طلباً من خلال زر تحديد الكل")
            self.update_selected_count()

            # عرض رسالة مؤقتة للمستخدم بالصيغة العربية الصحيحة
            if self.all_selected:
                if selected_count == 0:
                    self.show_temp_message("لم يتم تحديد أي طلب")
                elif selected_count == 1:
                    self.show_temp_message("تم تحديد طلب واحد")
                elif selected_count == 2:
                    self.show_temp_message("تم تحديد طلبان")
                elif selected_count >= 3 and selected_count <= 10:
                    self.show_temp_message(f"تم تحديد {selected_count} طلبات")
                else:
                    self.show_temp_message(f"تم تحديد {selected_count} طلباً")
            else:
                self.show_temp_message("تم إلغاء تحديد جميع الطلبات")

        except Exception as e:
            self.logger.error(f"خطأ في تبديل حالة التحديد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تبديل حالة التحديد: {str(e)}")

    def checkbox_state_changed(self, state, row):
        """معالجة تغيير حالة مربع الاختيار"""
        self.logger.info(f"تغيير حالة مربع الاختيار في الصف {row} إلى {state}")
        # تحديث عدد الطلبات المحددة بعد تغيير حالة مربع الاختيار
        self.update_selected_count()

    def update_selected_count(self):
        """تحديث عدد الطلبات المحددة"""
        count = 0
        total_price = 0

        # تسجيل معلومات التصحيح
        self.logger.info("بدء تحديث عدد الطلبات المحددة")

        # إعادة حساب عدد الطلبات المحددة بدقة
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                checkbox_widget = self.orders_table.cellWidget(row, 0)
                if checkbox_widget and checkbox_widget.layout() and checkbox_widget.layout().count() > 0:
                    # الحصول على مربع الاختيار من الخلية
                    checkbox = checkbox_widget.layout().itemAt(0).widget()
                    if checkbox and isinstance(checkbox, QCheckBox) and checkbox.isChecked():
                        # زيادة العداد
                        count += 1

                        # حساب المبلغ الإجمالي للطلبات المحددة
                        price_item = self.orders_table.item(row, 9)  # تصحيح رقم العمود للسعر الإجمالي
                        if price_item:
                            # إزالة الفواصل من النص قبل التحويل إلى رقم
                            price_text = price_item.text().replace(',', '')
                            try:
                                price = int(price_text)
                                total_price += price
                            except ValueError:
                                self.logger.warning(f"خطأ في تحويل السعر: {price_text}")

        # تسجيل عدد الطلبات المحددة
        if count == 0:
            self.logger.info("لم يتم تحديد أي طلب")
        elif count == 1:
            self.logger.info("تم تحديد طلب واحد")
        elif count == 2:
            self.logger.info("تم تحديد طلبان")
        elif count >= 3 and count <= 10:
            self.logger.info(f"تم تحديد {count} طلبات")
        else:
            self.logger.info(f"تم تحديد {count} طلباً")

        # تحديث المتغير وتسمية العداد
        self.selected_count = count

        # تحديث نص العداد بالصيغة العربية الصحيحة
        if count == 0:
            self.selected_count_label.setText("عدد الطلبات المحددة: 0")
        elif count == 1:
            self.selected_count_label.setText("عدد الطلبات المحددة: 1 (طلب واحد)")
        elif count == 2:
            self.selected_count_label.setText("عدد الطلبات المحددة: 2 (طلبان)")
        elif count >= 3 and count <= 10:
            self.selected_count_label.setText(f"عدد الطلبات المحددة: {count} طلبات")
        else:
            self.selected_count_label.setText(f"عدد الطلبات المحددة: {count} طلباً")

        # تحديث المبلغ الإجمالي للطلبات المحددة
        if count > 0:
            formatted_total = "{:,}".format(total_price)
            self.total_amount_label.setText(f"المبلغ الإجمالي: {formatted_total} دينار")
        else:
            # عندما لا تكون هناك طلبات محددة، قم بتحديث المبلغ الإجمالي لجميع الطلبات
            self.update_total_amount()

        # تحديث واجهة المستخدم فوراً
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

    def update_total_amount(self):
        """تحديث المبلغ الإجمالي للطلبات"""
        total = 0
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                price_item = self.orders_table.item(row, 9)  # تصحيح رقم العمود للسعر الإجمالي
                if price_item:
                    # إزالة الفواصل وتحويل النص إلى رقم
                    price_text = price_item.text().replace(',', '')
                    try:
                        price = int(price_text)
                        total += price
                    except ValueError:
                        continue

        # تنسيق المبلغ بفواصل كل ثلاة أرقام
        formatted_total = "{:,}".format(total)
        self.total_amount_label.setText(f"المبلغ الإجمالي: {formatted_total} دينار")
