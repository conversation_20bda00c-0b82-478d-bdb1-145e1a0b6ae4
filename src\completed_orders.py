from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                           QMessageBox, QHeaderView, QWidget, QCheckBox,
                           QAbstractItemView)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QFont
from db.models import Database
from ui.styles import get_button_style, COLORS, TABLE_STYLE, SEARCH_STYLE

class CompletedOrdersWindow(QDialog):
    """نافذة الطلبات المكتملة"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة الطلبات المكتملة")
        self.setModal(True)
        self.setMinimumSize(1200, 700)

        # إنشاء قاعدة البيانات
        try:
            self.db = Database()
            print("تم تهيئة قاعدة البيانات بنجاح في نافذة الطلبات المكتملة")
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "فشل الاتصال بقاعدة البيانات")
            return

        self.initUI()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء حقول البحث
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)

        # حقل البحث باسم المندوب
        rep_search_label = QLabel("بحث باسم المندوب:")
        self.rep_search = QLineEdit()
        self.rep_search.setStyleSheet(SEARCH_STYLE)
        self.rep_search.textChanged.connect(self.filter_orders)

        # حقل البحث برقم الهاتف
        phone_search_label = QLabel("بحث برقم الهاتف:")
        self.phone_search = QLineEdit()
        self.phone_search.setStyleSheet(SEARCH_STYLE)
        self.phone_search.textChanged.connect(self.filter_orders)

        # حقل البحث برقم الوصل
        receipt_search_label = QLabel("بحث برقم الوصل:")
        self.receipt_search = QLineEdit()
        self.receipt_search.setStyleSheet(SEARCH_STYLE)
        self.receipt_search.textChanged.connect(self.filter_orders)

        # إضافة حقول البحث إلى التخطيط
        search_layout.addWidget(rep_search_label)
        search_layout.addWidget(self.rep_search)
        search_layout.addWidget(phone_search_label)
        search_layout.addWidget(self.phone_search)
        search_layout.addWidget(receipt_search_label)
        search_layout.addWidget(self.receipt_search)

        # إضافة الأزرار
        buttons_container = QWidget()
        buttons_layout = QHBoxLayout(buttons_container)

        # زر تحديد الكل
        self.select_all_btn = QPushButton("تحديد الكل")
        self.select_all_btn.setStyleSheet(get_button_style(COLORS['info']))
        self.select_all_btn.clicked.connect(self.toggle_select_all)
        buttons_layout.addWidget(self.select_all_btn)

        # إضافة الحاويات إلى التخطيط الرئيسي
        layout.addWidget(search_container)
        layout.addWidget(buttons_container)

        # إنشاء جدول الطلبات
        self.orders_table = QTableWidget()
        self.orders_table.setStyleSheet(TABLE_STYLE)
        self.orders_table.setColumnCount(10)
        self.orders_table.setHorizontalHeaderLabels([
            "تحديد", "التسلسل", "رقم الوصل", "رقم الهاتف",
            "العنوان", "السعر الاجمالي", "ملاحظات", "اسم المندوب",
            "تاريخ الاستلام", "تاريخ الإكمال"
        ])

        # تعيين خصائص الجدول
        self.orders_table.horizontalHeader().setStretchLastSection(True)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.orders_table.verticalHeader().setVisible(False)

        # تعديل عرض الأعمدة
        column_widths = [20, 25, 75, 120, 200, 85, 200, 120, 100, 100]
        for i, width in enumerate(column_widths):
            self.orders_table.setColumnWidth(i, width)

        layout.addWidget(self.orders_table)
        self.setLayout(layout)

        # تحميل الطلبات
        self.load_orders()

    def load_orders(self):
        """تحميل الطلبات من قاعدة البيانات"""
        try:
            # استعلام SQL لجلب الطلبات المكتملة مع أسماء المندوبين الصحيحة
            orders = self.db.fetch_all(
                """
                SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address,
                       o.total_price, o.notes,
                       COALESCE(
                           r_pickup.name,
                           r_delivery.name,
                           o.delegate_name,
                           'غير محدد'
                       ) as rep_name,
                       o.created_at,
                       o.completion_date
                FROM orders o
                LEFT JOIN representatives r_pickup ON o.pickup_representative_id = r_pickup.id
                LEFT JOIN representatives r_delivery ON o.delivery_representative_id = r_delivery.id
                WHERE o.status = 'مكتمل' OR o.status = 'completed' OR o.status = 'واصل'
                OR o.status = 'تم التسليم' OR o.status = 'delivered' OR o.status = 'distribution_delivered'
                OR o.status = 'تم الاستلام' OR o.status = 'تم التوصيل' OR o.status = 'تم التوزيع'
                OR o.status = 'completed_delivery' OR o.status = 'completed_distribution'
                ORDER BY o.created_at DESC
                """
            )

            self.orders_table.setRowCount(len(orders))

            for row, order in enumerate(orders):
                # تقليل ارتفاع الصف
                self.orders_table.setRowHeight(row, 35)

                # إضافة مربع تحديد
                checkbox = QCheckBox()
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                self.orders_table.setCellWidget(row, 0, checkbox_widget)

                # إضافة بيانات الطلب
                # ترتيب البيانات: id, receipt_number, phone, phone2, address, total_price, notes, rep_name, created_at, completion_date

                # إضافة معرف الطلب (مخفي)
                id_item = QTableWidgetItem(str(order[0]))
                self.orders_table.setItem(row, 1, id_item)

                # رقم الوصل
                receipt_item = QTableWidgetItem(str(order[1]) if order[1] is not None else "")
                receipt_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 2, receipt_item)

                # توحيد حقول الهاتف (الأول والثاني) في حقل واحد
                phone_widget = QWidget()
                phone_layout = QVBoxLayout(phone_widget)
                phone_layout.setContentsMargins(2, 2, 2, 2)
                phone_layout.setSpacing(2)

                # الهاتف الأساسي
                phone1 = str(order[2]) if order[2] else ""
                if phone1:
                    phone1_label = QLabel(phone1)
                    phone1_label.setAlignment(Qt.AlignCenter)
                    phone1_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone1_label)

                # الهاتف الثاني إذا وجد
                phone2 = str(order[3]) if order[3] else ""
                if phone2:
                    phone2_label = QLabel(phone2)
                    phone2_label.setAlignment(Qt.AlignCenter)
                    phone2_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone2_label)
                    # تعديل ارتفاع الصف ليناسب الرقمين
                    self.orders_table.setRowHeight(row, 60)  # ارتفاع أكبر عند وجود رقمين

                self.orders_table.setCellWidget(row, 3, phone_widget)

                # العنوان
                address_item = QTableWidgetItem(str(order[4]) if order[4] is not None else "")
                address_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 4, address_item)

                # السعر الإجمالي - تنسيق السعر بحذف الأعشار وإضافة فاصلة كل ثلاث مراتب
                price_str = ""
                if order[5] is not None:
                    try:
                        # تحويل السعر إلى عدد صحيح (حذف الأعشار)
                        price_int = int(float(order[5]))
                        # تنسيق السعر بإضافة فاصلة كل ثلاث مراتب
                        price_str = "{:,}".format(price_int)
                    except (ValueError, TypeError):
                        # في حالة حدوث خطأ، عرض السعر كما هو
                        price_str = str(order[5])

                total_price_item = QTableWidgetItem(price_str)
                total_price_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 5, total_price_item)

                # ملاحظات
                notes_item = QTableWidgetItem(str(order[6]) if order[6] is not None else "")
                notes_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 6, notes_item)

                # اسم المندوب
                rep_name_item = QTableWidgetItem(str(order[7]) if order[7] is not None else "")
                rep_name_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 7, rep_name_item)

                # تاريخ الاستلام
                created_date_str = ""
                if order[8] is not None:
                    try:
                        # تحويل التاريخ إلى كائن QDateTime
                        date_time = QDateTime.fromString(str(order[8]), Qt.ISODate)
                        if date_time.isValid():
                            # تنسيق التاريخ بدون وقت الساعة
                            created_date_str = date_time.toString("yyyy-MM-dd")
                        else:
                            # إذا كان التنسيق غير صالح، عرض النص الأصلي
                            created_date_str = str(order[8]).split(" ")[0] if " " in str(order[8]) else str(order[8])
                    except:
                        # في حالة حدوث خطأ، عرض التاريخ كما هو أو فقط جزء التاريخ
                        created_date_str = str(order[8]).split(" ")[0] if " " in str(order[8]) else str(order[8])

                created_date_item = QTableWidgetItem(created_date_str)
                created_date_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 8, created_date_item)

                # تاريخ الإكمال
                completion_date_str = ""
                if order[9] is not None:
                    try:
                        # تحويل التاريخ إلى كائن QDateTime
                        date_time = QDateTime.fromString(str(order[9]), Qt.ISODate)
                        if date_time.isValid():
                            # تنسيق التاريخ بدون وقت الساعة
                            completion_date_str = date_time.toString("yyyy-MM-dd")
                        else:
                            # إذا كان التنسيق غير صالح، عرض النص الأصلي
                            completion_date_str = str(order[9]).split(" ")[0] if " " in str(order[9]) else str(order[9])
                    except:
                        # في حالة حدوث خطأ، عرض التاريخ كما هو أو فقط جزء التاريخ
                        completion_date_str = str(order[9]).split(" ")[0] if " " in str(order[9]) else str(order[9])

                completion_date_item = QTableWidgetItem(completion_date_str)
                completion_date_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 9, completion_date_item)

        except Exception as e:
            print(f"خطأ في تحميل الطلبات المكتملة: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الطلبات: {str(e)}")

    def filter_orders(self):
        """تصفية الطلبات حسب معايير البحث"""
        rep_filter = self.rep_search.text().lower()
        phone_filter = self.phone_search.text().lower()
        receipt_filter = self.receipt_search.text().lower()

        for row in range(self.orders_table.rowCount()):
            show_row = True

            # التحقق من تطابق اسم المندوب
            if rep_filter:
                rep_name = self.orders_table.item(row, 7).text().lower()
                if rep_filter not in rep_name:
                    show_row = False

            # التحقق من تطابق رقم الهاتف
            if phone_filter and show_row:
                # البحث في أرقام الهاتف (الأول والثاني)
                phone_widget = self.orders_table.cellWidget(row, 3)
                phone_text = ""

                if phone_widget:
                    # جمع نصوص جميع الـ QLabel في الـ widget
                    for i in range(phone_widget.layout().count()):
                        label = phone_widget.layout().itemAt(i).widget()
                        if label:
                            phone_text += label.text().lower() + " "

                if phone_filter not in phone_text:
                    show_row = False

            # التحقق من تطابق رقم الوصل
            if receipt_filter and show_row:
                receipt = self.orders_table.item(row, 2).text().lower()
                if receipt_filter not in receipt:
                    show_row = False

            # إظهار أو إخفاء الصف
            self.orders_table.setRowHidden(row, not show_row)

    def toggle_select_all(self):
        """تبديل تحديد جميع الطلبات"""
        # التحقق من حالة الزر
        if self.select_all_btn.text() == "تحديد الكل":
            # تحديد جميع الطلبات
            for row in range(self.orders_table.rowCount()):
                if not self.orders_table.isRowHidden(row):
                    checkbox = self.orders_table.cellWidget(row, 0).findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)
            self.select_all_btn.setText("إلغاء التحديد")
        else:
            # إلغاء تحديد جميع الطلبات
            for row in range(self.orders_table.rowCount()):
                checkbox = self.orders_table.cellWidget(row, 0).findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(False)
            self.select_all_btn.setText("تحديد الكل")
