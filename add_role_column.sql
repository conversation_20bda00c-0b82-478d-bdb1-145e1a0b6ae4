-- التحقق من وجود عمود 'role' في جدول المستخدمين
SELECT COUNT(*) INTO @column_exists
FROM information_schema.columns
WHERE table_schema = DATABASE()
AND table_name = 'users'
AND column_name = 'role';

-- إضافة عمود 'role' إذا لم يكن موجودًا
SET @query = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN role VARCHAR(50) NOT NULL DEFAULT "user"', 'SELECT "Column already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة مستخدم افتراضي إذا لم يكن موجودًا
INSERT IGNORE INTO users (username, password, role) VALUES ('admin', 'admin123', 'admin');
