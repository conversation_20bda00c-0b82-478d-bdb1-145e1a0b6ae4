"""
التحقق من جدول المستخدمين في قاعدة البيانات
"""

import pymysql
from utils.config import load_config

def check_users():
    """التحقق من جدول المستخدمين في قاعدة البيانات"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()
        
        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
        
        # التحقق من وجود جدول المستخدمين
        cursor.execute("SHOW TABLES LIKE 'users'")
        if cursor.fetchone():
            print("جدول المستخدمين موجود")
            
            # عرض هيكل جدول المستخدمين
            cursor.execute("DESCRIBE users")
            columns = cursor.fetchall()
            print("هيكل جدول المستخدمين:")
            for column in columns:
                print(f"  {column}")
            
            # عرض بيانات المستخدمين
            cursor.execute("SELECT * FROM users")
            users = cursor.fetchall()
            print("بيانات المستخدمين:")
            for user in users:
                print(f"  {user}")
            
            # التحقق من وجود المستخدم admin
            cursor.execute("SELECT * FROM users WHERE username = 'admin'")
            admin_user = cursor.fetchone()
            if admin_user:
                print(f"المستخدم admin موجود: {admin_user}")
                
                # التحقق من صلاحية المستخدم admin
                role_column_index = None
                for i, column in enumerate(columns):
                    if column[0] == 'role':
                        role_column_index = i
                        break
                
                if role_column_index is not None:
                    admin_role = admin_user[role_column_index]
                    print(f"صلاحية المستخدم admin: {admin_role}")
                    
                    # تحديث صلاحية المستخدم admin إذا لم تكن 'admin'
                    if admin_role != 'admin':
                        cursor.execute("UPDATE users SET role = 'admin' WHERE username = 'admin'")
                        conn.commit()
                        print("تم تحديث صلاحية المستخدم admin إلى 'admin'")
                        
                        # التحقق من التحديث
                        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
                        updated_admin = cursor.fetchone()
                        print(f"المستخدم admin بعد التحديث: {updated_admin}")
                else:
                    print("لم يتم العثور على عمود 'role' في جدول المستخدمين")
            else:
                print("المستخدم admin غير موجود")
                
                # إضافة المستخدم admin
                cursor.execute("INSERT INTO users (username, password, role) VALUES ('admin', 'admin123', 'admin')")
                conn.commit()
                print("تم إضافة المستخدم admin بصلاحية 'admin'")
        else:
            print("جدول المستخدمين غير موجود")
        
        # إغلاق الاتصال
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في التحقق من جدول المستخدمين: {str(e)}")
        return False

if __name__ == "__main__":
    check_users()
