# دليل الترحيل من SQLite إلى MySQL

هذا الدليل يشرح خطوات ترحيل برنامج غسيل السجاد من قاعدة بيانات SQLite إلى MySQL.

## المتطلبات

1. تثبيت MySQL Server (تم بالفعل)
2. تثبيت حزم Python اللازمة للاتصال بـ MySQL

## خطوات الترحيل

### 1. تثبيت حزم Python اللازمة

قم بتشغيل الملف `install_mysql_requirements.py` لتثبيت حزم Python اللازمة:

```
python install_mysql_requirements.py
```

### 2. تهيئة قاعدة بيانات MySQL

قم بتشغيل الملف `init_mysql_database.py` لإنشاء قاعدة البيانات والجداول:

```
python init_mysql_database.py
```

### 3. ترحيل البيانات من SQLite إلى MySQL

قم بتشغيل الملف `migrate_sqlite_to_mysql.py` لترحيل البيانات:

```
python migrate_sqlite_to_mysql.py
```

اختر الخيار المناسب لترحيل قاعدة بيانات `carpet_db.db` أو `carpet_cleaning.db` أو كلاهما.

### 4. تحديث إعدادات البرنامج

تم بالفعل تحديث ملف `app_config.json` لاستخدام MySQL بدلاً من SQLite. تأكد من أن الإعدادات التالية موجودة:

```json
{
    "db_type": "mysql",
    "db_host": "localhost",
    "db_port": "3306",
    "db_name": "carpet_cleaning_service",
    "db_user": "carpet_user",
    "db_password": "@#57111819752#@"
}
```

### 5. اختبار البرنامج

قم بتشغيل البرنامج للتأكد من أنه يعمل بشكل صحيح مع MySQL:

```
python run.py
```

## استخدام البرنامج على أجهزة متعددة

لاستخدام البرنامج على أجهزة متعددة، يجب تكوين MySQL للسماح بالاتصالات البعيدة:

1. افتح MySQL Workbench
2. اتصل بالخادم المحلي باستخدام حساب المستخدم الجذر
3. انتقل إلى: Server > Options File
4. في قسم Networking، ابحث عن "bind-address" وقم بتغييره إلى "0.0.0.0" للسماح بالاتصالات من أي عنوان IP
5. انقر على "Apply" ثم أعد تشغيل خدمة MySQL

ثم على كل جهاز، قم بتحديث ملف `app_config.json` لاستخدام عنوان IP للخادم بدلاً من "localhost":

```json
{
    "db_type": "mysql",
    "db_host": "192.168.1.x",  // استبدل بعنوان IP للخادم
    "db_port": "3306",
    "db_name": "carpet_cleaning_service",
    "db_user": "carpet_user",
    "db_password": "@#57111819752#@"
}
```

## استكشاف الأخطاء وإصلاحها

### مشكلة الاتصال بقاعدة البيانات

إذا واجهت مشكلة في الاتصال بقاعدة البيانات، تأكد من:

1. أن خدمة MySQL قيد التشغيل
2. أن إعدادات الاتصال صحيحة في ملف `app_config.json`
3. أن المستخدم `carpet_user` لديه صلاحيات كافية للوصول إلى قاعدة البيانات

### مشكلة في ترحيل البيانات

إذا واجهت مشكلة في ترحيل البيانات، جرب:

1. التأكد من أن قاعدة البيانات SQLite موجودة وصالحة
2. التأكد من أن جداول MySQL تم إنشاؤها بشكل صحيح
3. تشغيل الترحيل مرة أخرى مع اختيار قاعدة بيانات واحدة في كل مرة

## ملاحظات هامة

- لا تحذف ملفات قاعدة بيانات SQLite حتى تتأكد من أن الترحيل تم بنجاح وأن البرنامج يعمل بشكل صحيح مع MySQL
- قم بإنشاء نسخة احتياطية من قاعدة بيانات MySQL بشكل دوري
- يمكنك العودة إلى استخدام SQLite في أي وقت عن طريق تغيير إعداد `db_type` إلى `sqlite` في ملف `app_config.json`
