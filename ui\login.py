from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, 
                           QLabel, QLineEdit, QPushButton, QMessageBox, QDesktopWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from utils.config import setup_arabic_encoding
from db.models import Database
from ui.styles import get_button_style, WINDOW_STYLE

# تهيئة الترميز العربي
setup_arabic_encoding()

class LoginWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.db = Database()
        self.user = None
        
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(400, 300)
        self.setStyleSheet(WINDOW_STYLE)
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # وضع النافذة في وسط الشاشة
        self.center_window()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إضافة العنوان
        title = QLabel("تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            font-size: 40px;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        layout.addWidget(title)
        
        # حقل اسم المستخدم
        username_layout = QHBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-size: 16px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("font-size: 16px;")
        username_layout.addWidget(self.username_input)
        username_layout.addWidget(username_label)
        layout.addLayout(username_layout)
        
        # حقل كلمة المرور
        password_layout = QHBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-size: 16px;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("font-size: 16px;")
        password_layout.addWidget(self.password_input)
        password_layout.addWidget(password_label)
        layout.addLayout(password_layout)
        
        # أزرار تسجيل الدخول والإلغاء
        buttons_layout = QHBoxLayout()
        
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.setStyleSheet(get_button_style("#2ecc71"))  # أخضر
        login_btn.clicked.connect(self.login)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(get_button_style("#e74c3c"))  # أحمر
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(login_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
    
    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            user = self.db.authenticate(username, password)
            if user:
                self.user = user
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول: {str(e)}")

    def center_window(self):
        """وضع النافذة في وسط الشاشة"""
        frame = self.frameGeometry()
        center = QDesktopWidget().availableGeometry().center()
        frame.moveCenter(center)
        self.move(frame.topLeft())
