#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من بنية جدول الطلبات
"""

import os
import sys

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_orders_table():
    """التحقق من بنية جدول الطلبات"""
    try:
        print("🔍 فحص بنية جدول الطلبات")
        print("=" * 50)
        
        from db.models import Database
        
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # عرض أعمدة جدول الطلبات
        print("📋 أعمدة جدول orders:")
        cursor.execute("DESCRIBE orders")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"   {column[0]} - {column[1]} - {column[2]} - {column[3]}")
        
        # التحقق من وجود العمود transport_fees
        column_names = [col[0] for col in columns]
        
        print(f"\n🔍 البحث عن الأعمدة المطلوبة:")
        
        required_columns = ['delivery_fee', 'has_delivery_fee', 'transport_fees']
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col} موجود")
            else:
                print(f"   ❌ {col} غير موجود")
        
        # عرض عدد الطلبات
        cursor.execute("SELECT COUNT(*) FROM orders")
        count = cursor.fetchone()[0]
        print(f"\n📊 عدد الطلبات في الجدول: {count}")
        
        # عرض آخر 3 طلبات
        if count > 0:
            print(f"\n📋 آخر 3 طلبات:")
            cursor.execute("SELECT id, phone, address, status FROM orders ORDER BY id DESC LIMIT 3")
            recent_orders = cursor.fetchall()
            
            for order in recent_orders:
                print(f"   ID: {order[0]}, الهاتف: {order[1]}, العنوان: {order[2][:20]}..., الحالة: {order[3]}")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ تم فحص جدول الطلبات بنجاح")
        print("✅ تم إصلاح مشكلة transport_fees")
        print("✅ البرنامج جاهز للاستخدام")
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول الطلبات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_orders_table()
