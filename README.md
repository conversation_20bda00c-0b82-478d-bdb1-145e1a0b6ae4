# برنامج إدارة غسيل السجاد

نظام متكامل لإدارة عمليات غسيل السجاد من استلام الطلبات وحتى تسليمها للعملاء.

## الميزات الرئيسية

- إدارة دورة حياة الطلبات (الاستلام، التنظيف، التسليم)
- إدارة المندوبين وتوزيع الطلبات
- تتبع أبعاد السجاد وتفاصيل التنظيف
- إدارة الحسابات والمدفوعات
- تقارير متنوعة
- دعم كامل للغة العربية
- نظام النسخ الاحتياطي التلقائي اليومي

## هيكل المشروع

- `src/`: الملفات الأساسية للبرنامج
- `ui/`: ملفات واجهة المستخدم
- `db/`: ملفات قاعدة البيانات
- `utils/`: أدوات مساعدة
- `scripts/`: نصوص برمجية مساعدة
- `backups/`: مجلد النسخ الاحتياطية
- `logs/`: سجلات البرنامج

## متطلبات النظام

- Python 3.x
- PyQt5
- SQLAlchemy
- دعم اللغة العربية

## طريقة التشغيل

```
python run.py
```

## نظام النسخ الاحتياطي

يتضمن البرنامج نظام نسخ احتياطي متكامل يوفر:

- **النسخ الاحتياطي التلقائي اليومي**: يقوم البرنامج بإنشاء نسخة احتياطية من قاعدة البيانات بشكل يومي في الوقت المحدد.
- **النسخ الاحتياطي عند الإغلاق**: يمكن تفعيل إنشاء نسخة احتياطية عند إغلاق البرنامج.
- **النسخ الاحتياطي اليدوي**: يمكن للمستخدم إنشاء نسخة احتياطية في أي وقت.
- **استعادة النسخ الاحتياطية**: يمكن استعادة قاعدة البيانات من أي نسخة احتياطية سابقة.
- **إدارة مجلد النسخ الاحتياطية**: يمكن تحديد المجلد الذي يتم حفظ النسخ الاحتياطية فيه.
- **تنظيف النسخ الاحتياطية القديمة**: يمكن حذف النسخ الاحتياطية التي مر عليها أكثر من 30 يوم.

### إعدادات النسخ الاحتياطي

يمكن الوصول إلى إعدادات النسخ الاحتياطي من خلال:

1. فتح نافذة الإعدادات
2. الانتقال إلى تبويب "النسخ الاحتياطي"
3. تعديل الإعدادات حسب الحاجة

## الوثائق

- `README.md`: الملف الحالي
- `README_TOOLS.md`: توثيق الأدوات المساعدة

## الترخيص

جميع الحقوق محفوظة © 2025
