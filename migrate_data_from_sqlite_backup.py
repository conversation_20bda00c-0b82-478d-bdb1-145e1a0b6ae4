#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لنقل البيانات من النسخة الاحتياطية لـ SQLite إلى MySQL
"""

import os
import sys
import sqlite3
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def migrate_data_from_sqlite():
    """نقل البيانات من SQLite إلى MySQL"""
    try:
        print("\n" + "=" * 60)
        print("نقل البيانات من SQLite إلى MySQL".center(60))
        print("=" * 60)
        
        # البحث عن ملفات SQLite في النسخة الاحتياطية
        backup_dir = "sqlite_backup"
        sqlite_files = []
        
        if os.path.exists(backup_dir):
            for file in os.listdir(backup_dir):
                if file.endswith('.db'):
                    sqlite_files.append(os.path.join(backup_dir, file))
        
        # البحث عن ملفات SQLite في المجلد الرئيسي
        for file in ['carpet_cleaning.db', 'carpet_db.db']:
            if os.path.exists(file):
                sqlite_files.append(file)
        
        if not sqlite_files:
            print("✗ لم يتم العثور على ملفات SQLite")
            return False
        
        print(f"تم العثور على {len(sqlite_files)} ملف SQLite:")
        for file in sqlite_files:
            print(f"  - {file}")
        
        # اختيار أحدث ملف SQLite
        sqlite_file = sqlite_files[0]
        print(f"\nسيتم استخدام الملف: {sqlite_file}")
        
        # الاتصال بـ SQLite
        sqlite_conn = sqlite3.connect(sqlite_file)
        sqlite_cursor = sqlite_conn.cursor()
        
        # التحقق من الجداول في SQLite
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        sqlite_tables = [table[0] for table in sqlite_cursor.fetchall()]
        print(f"\nالجداول في SQLite: {sqlite_tables}")
        
        # الاتصال بـ MySQL
        from db.models import Database
        mysql_db = Database()
        mysql_conn = mysql_db.get_connection()
        mysql_cursor = mysql_conn.cursor()
        
        print("✓ تم الاتصال بـ MySQL")
        
        # نقل بيانات الطلبات
        if 'orders' in sqlite_tables:
            print("\n--- نقل بيانات الطلبات ---")
            
            # قراءة البيانات من SQLite
            sqlite_cursor.execute("SELECT * FROM orders")
            orders = sqlite_cursor.fetchall()
            print(f"عدد الطلبات في SQLite: {len(orders)}")
            
            if orders:
                # الحصول على أسماء الأعمدة
                sqlite_cursor.execute("PRAGMA table_info(orders)")
                columns_info = sqlite_cursor.fetchall()
                column_names = [col[1] for col in columns_info]
                print(f"أعمدة جدول الطلبات: {column_names}")
                
                # إدراج البيانات في MySQL
                for order in orders:
                    try:
                        # تحديد الأعمدة المطلوبة
                        insert_data = {
                            'phone': order[1] if len(order) > 1 else '',
                            'address': order[2] if len(order) > 2 else '',
                            'created_at': order[3] if len(order) > 3 else None,
                            'receipt_number': order[4] if len(order) > 4 else None,
                            'total_area': order[5] if len(order) > 5 else 0,
                            'total_price': order[6] if len(order) > 6 else 0,
                            'notes': order[7] if len(order) > 7 else None,
                            'status': order[8] if len(order) > 8 else 'جديد'
                        }
                        
                        mysql_cursor.execute("""
                            INSERT INTO orders (phone, address, created_at, receipt_number, 
                                              total_area, total_price, notes, status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            insert_data['phone'],
                            insert_data['address'],
                            insert_data['created_at'],
                            insert_data['receipt_number'],
                            insert_data['total_area'],
                            insert_data['total_price'],
                            insert_data['notes'],
                            insert_data['status']
                        ))
                        
                    except Exception as e:
                        print(f"خطأ في إدراج الطلب {order[0]}: {str(e)}")
                
                mysql_conn.commit()
                print(f"✓ تم نقل {len(orders)} طلب إلى MySQL")
        
        # نقل بيانات المندوبين
        if 'delegates' in sqlite_tables:
            print("\n--- نقل بيانات المندوبين ---")
            
            sqlite_cursor.execute("SELECT * FROM delegates")
            delegates = sqlite_cursor.fetchall()
            print(f"عدد المندوبين في SQLite: {len(delegates)}")
            
            if delegates:
                for delegate in delegates:
                    try:
                        mysql_cursor.execute("""
                            INSERT INTO delegates (name, created_at)
                            VALUES (%s, %s)
                        """, (
                            delegate[1] if len(delegate) > 1 else '',
                            delegate[2] if len(delegate) > 2 else None
                        ))
                    except Exception as e:
                        print(f"خطأ في إدراج المندوب {delegate[0]}: {str(e)}")
                
                mysql_conn.commit()
                print(f"✓ تم نقل {len(delegates)} مندوب إلى MySQL")
        
        # نقل بيانات الإعدادات
        if 'settings' in sqlite_tables:
            print("\n--- نقل بيانات الإعدادات ---")
            
            sqlite_cursor.execute("SELECT * FROM settings")
            settings = sqlite_cursor.fetchall()
            print(f"عدد الإعدادات في SQLite: {len(settings)}")
            
            if settings:
                setting = settings[0]  # أخذ أول إعداد
                try:
                    mysql_cursor.execute("""
                        INSERT INTO settings (price_per_meter, blanket_price, delivery_price)
                        VALUES (%s, %s, %s)
                    """, (
                        setting[1] if len(setting) > 1 else 1000.0,
                        setting[2] if len(setting) > 2 else 5000.0,
                        setting[3] if len(setting) > 3 else 2000.0
                    ))
                    mysql_conn.commit()
                    print("✓ تم نقل الإعدادات إلى MySQL")
                except Exception as e:
                    print(f"خطأ في نقل الإعدادات: {str(e)}")
        
        # إغلاق الاتصالات
        sqlite_conn.close()
        mysql_conn.close()
        
        print("\n" + "=" * 60)
        print("تم الانتهاء من نقل البيانات".center(60))
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"خطأ في نقل البيانات: {str(e)}")
        traceback.print_exc()
        return False

def create_sample_data():
    """إنشاء بيانات تجريبية إذا لم توجد بيانات"""
    try:
        print("\n" + "=" * 60)
        print("إنشاء بيانات تجريبية".center(60))
        print("=" * 60)
        
        from db.models import Database
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # إنشاء مندوبين تجريبيين
        delegates = [
            "أحمد محمد",
            "محمد علي", 
            "علي أحمد",
            "خالد محمود"
        ]
        
        print("إنشاء مندوبين تجريبيين...")
        for delegate_name in delegates:
            try:
                cursor.execute("INSERT INTO delegates (name) VALUES (%s)", (delegate_name,))
            except Exception as e:
                print(f"المندوب {delegate_name} موجود بالفعل")
        
        # إنشاء طلبات تجريبية
        orders = [
            ("01234567890", "الرياض - حي النخيل", "جديد", "R001", 12.5, 12500.0, "سجاد صالة"),
            ("01234567891", "الرياض - حي الملز", "في المخزن", "R002", 8.0, 8000.0, "سجاد غرفة نوم"),
            ("01234567892", "الرياض - حي العليا", "مكتمل", "R003", 15.0, 15000.0, "سجاد مجلس"),
            ("01234567893", "الرياض - حي السليمانية", "توزيع مندوب", "R004", 10.0, 10000.0, "سجاد مطبخ"),
            ("01234567894", "الرياض - حي الورود", "استلام مندوب", "R005", 20.0, 20000.0, "سجاد كبير")
        ]
        
        print("إنشاء طلبات تجريبية...")
        for order in orders:
            try:
                cursor.execute("""
                    INSERT INTO orders (phone, address, status, receipt_number, 
                                      total_area, total_price, notes)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, order)
            except Exception as e:
                print(f"خطأ في إنشاء الطلب: {str(e)}")
        
        # إنشاء إعدادات افتراضية
        try:
            cursor.execute("""
                INSERT INTO settings (price_per_meter, blanket_price, delivery_price)
                VALUES (%s, %s, %s)
            """, (1000.0, 5000.0, 2000.0))
        except Exception as e:
            print("الإعدادات موجودة بالفعل")
        
        conn.commit()
        conn.close()
        
        print("✓ تم إنشاء البيانات التجريبية")
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("استعادة البيانات إلى MySQL".center(60))
    print("=" * 60)
    
    # محاولة نقل البيانات من SQLite
    migration_success = migrate_data_from_sqlite()
    
    if not migration_success:
        print("\nلم يتم العثور على بيانات SQLite للنقل")
        print("سيتم إنشاء بيانات تجريبية...")
        create_sample_data()
    
    print("\n✅ تم الانتهاء من عملية استعادة البيانات")
    print("يمكنك الآن تشغيل البرنامج ومشاهدة البيانات في القوائم المختلفة")

if __name__ == "__main__":
    main()
