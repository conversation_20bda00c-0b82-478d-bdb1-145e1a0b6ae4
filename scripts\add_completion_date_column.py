#!/usr/bin/env python
import sqlite3
import os
import sys
import datetime

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from db.models import Database

def add_completion_date_column():
    """Add completion_date column to orders table if it doesn't exist"""
    try:
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()

        # Check if column exists
        cursor.execute("PRAGMA table_info(orders)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'completion_date' not in columns:
            print("Adding completion_date column...")
            cursor.execute("""
                ALTER TABLE orders 
                ADD COLUMN completion_date TIMESTAMP
            """)
            print("Column added successfully")
        else:
            print("completion_date column already exists")

        conn.commit()
        print("Schema update completed successfully")
        
    except Exception as e:
        print(f"Error updating schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
    finally:
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    add_completion_date_column()