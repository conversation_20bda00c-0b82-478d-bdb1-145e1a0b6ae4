"""
وحدة الاتصال بقاعدة بيانات MySQL لبرنامج غسيل السجاد
"""

import os
import mysql.connector
from mysql.connector import Error
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# قاعدة النماذج
Base = declarative_base()

class MySQLDatabase:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MySQLDatabase, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not MySQLDatabase._initialized:
            # إعدادات الاتصال بقاعدة البيانات
            self.config = {
                'host': 'localhost',  # يمكن تغييره إلى عنوان IP للخادم البعيد
                'user': 'carpet_user',
                'password': '@#57111819752#@',  # كلمة المرور التي تم إعدادها في MySQL Workbench
                'database': 'carpet_cleaning_service',
                'port': 3306,
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'raise_on_warnings': True
            }

            # إنشاء محرك SQLAlchemy
            self.engine_url = f"mysql+mysqlconnector://{self.config['user']}:{self.config['password']}@{self.config['host']}:{self.config['port']}/{self.config['database']}?charset={self.config['charset']}"
            self.engine = create_engine(self.engine_url, echo=False)

            # إنشاء جلسة
            self.Session = sessionmaker(bind=self.engine)

            MySQLDatabase._initialized = True

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات MySQL"""
        try:
            conn = mysql.connector.connect(**self.config)
            return conn
        except Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات MySQL: {e}")
            return None

    def get_session(self):
        """إنشاء جلسة SQLAlchemy"""
        return self.Session()

    def execute_query(self, query, params=None):
        """تنفيذ استعلام SQL"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            if conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor
        except Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            if conn:
                conn.rollback()
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
        return None

    def fetch_all(self, query, params=None):
        """تنفيذ استعلام واسترجاع جميع النتائج"""
        cursor = self.execute_query(query, params)
        if cursor:
            result = cursor.fetchall()
            cursor.close()
            return result
        return []

    def fetch_one(self, query, params=None):
        """تنفيذ استعلام واسترجاع نتيجة واحدة"""
        cursor = self.execute_query(query, params)
        if cursor:
            result = cursor.fetchone()
            cursor.close()
            return result
        return None

    def insert(self, query, params=None):
        """إدخال بيانات واسترجاع المعرف الجديد"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            if conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.lastrowid
        except Error as e:
            print(f"خطأ في إدخال البيانات: {e}")
            if conn:
                conn.rollback()
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
        return None
