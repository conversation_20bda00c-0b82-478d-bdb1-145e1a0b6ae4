from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLineEdit, QLabel, QMessageBox, QScrollArea, QWidget, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QDoubleValidator, QFont
from ui.styles import get_button_style, COLORS, DIALOG_STYLE
from db.models import Database

class CarpetDimensionsDialog(QDialog):
    def __init__(self, parent, carpet_count, existing_dimensions=None):
        super().__init__(parent)
        self.db = Database()
        self.carpet_count = carpet_count
        self.dimension_inputs = []
        
        # الحصول على سعر المتر من الإعدادات
        settings = self.db.fetch_one("SELECT price_per_meter FROM settings LIMIT 1")
        self.price_per_meter = settings[0] if settings and settings[0] is not None else 2000
        
        self.initUI()
        
        # تعيين الأبعاد الموجودة إذا تم تمريرها
        if existing_dimensions:
            for i, dim in enumerate(existing_dimensions):
                if i < len(self.dimension_inputs):
                    length_input, width_input, _, _ = self.dimension_inputs[i]
                    length_input.setText(str(dim['length']))
                    width_input.setText(str(dim['width']))
    
    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("أبعاد السجاد")
        self.setFixedSize(600, 600)  # تعيين حجم ثابت للنافذة
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)  # تعيين المسافة بين العناصر
        main_layout.setSizeConstraint(QVBoxLayout.SetFixedSize)  # تثبيت حجم النافذة
        
        # إنشاء منطقة التمرير
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # منع التمرير الأفقي
        scroll.setMinimumHeight(400)
        scroll.setMaximumHeight(400)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setAlignment(Qt.AlignTop)  # محاذاة المحتوى إلى الأعلى
        scroll_layout.setSizeConstraint(QVBoxLayout.SetMinAndMaxSize)  # تثبيت حجم المحتوى
        
        for i in range(self.carpet_count):
            group = QGroupBox(f"سجادة رقم {i + 1}")
            group_layout = QGridLayout()
            
            length_label = QLabel("الطول (متر):")
            length_input = QLineEdit()
            length_input.setValidator(QDoubleValidator(0.1, 100.0, 2))
            
            width_label = QLabel("العرض (متر):")
            width_input = QLineEdit()
            width_input.setValidator(QDoubleValidator(0.1, 100.0, 2))
            
            area_label = QLabel("المساحة:")
            area_value = QLabel("0.00 متر²")
            
            price_label = QLabel("السعر:")
            price_value = QLabel("-")
            
            group_layout.addWidget(length_label, 0, 0)
            group_layout.addWidget(length_input, 0, 1)
            group_layout.addWidget(width_label, 0, 2)
            group_layout.addWidget(width_input, 0, 3)
            group_layout.addWidget(area_label, 1, 0)
            group_layout.addWidget(area_value, 1, 1)
            group_layout.addWidget(price_label, 1, 2)
            group_layout.addWidget(price_value, 1, 3)
            
            self.dimension_inputs.append((length_input, width_input, area_value, price_value))
            
            # ربط دالة تحديث المساحة والسعر عند تغيير الأبعاد
            length_input.textChanged.connect(
                lambda text, l=length_input, w=width_input, a=area_value, p=price_value: 
                self.update_area_and_price(l, w, a, p)
            )
            width_input.textChanged.connect(
                lambda text, l=length_input, w=width_input, a=area_value, p=price_value: 
                self.update_area_and_price(l, w, a, p)
            )
            
            group.setLayout(group_layout)
            scroll_layout.addWidget(group)
        
        scroll_content.setLayout(scroll_layout)
        scroll.setWidget(scroll_content)
        main_layout.addWidget(scroll)
        
        # مجموعة الإجماليات
        summary_group = QGroupBox("الإجماليات")
        summary_layout = QGridLayout()
        
        self.total_area_label = QLabel("المساحة الكلية: 0.00 متر²")
        self.total_price_label = QLabel("السعر الإجمالي: 0 دينار")
        
        self.total_area_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #2980b9;")
        self.total_price_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #e74c3c;")
        
        summary_layout.addWidget(self.total_area_label, 0, 0)
        summary_layout.addWidget(self.total_price_label, 1, 0)
        
        summary_group.setLayout(summary_layout)
        main_layout.addWidget(summary_group)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(get_button_style(COLORS['success']))
        save_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(get_button_style(COLORS['danger']))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
    
    def update_area_and_price(self, length_field, width_field, area_label, price_label):
        """تحديث حساب المساحة لسجادة واحدة (بدون تقريب أو حساب السعر)"""
        try:
            length_text = length_field.text().replace(',', '.')
            width_text = width_field.text().replace(',', '.')
            
            if length_text and width_text:
                length_val = float(length_text)
                width_val = float(width_text)
                
                if length_val > 0 and width_val > 0:
                    # حساب المساحة الفردية فقط بدون تقريب
                    area = length_val * width_val
                    area_label.setText(f"{area:.2f} متر²")
                    # نخفي عرض السعر الفردي
                    price_label.setText("-")
                else:
                    area_label.setText("0.00 متر²")
                    price_label.setText("-")
            else:
                area_label.setText("0.00 متر²")
                price_label.setText("-")
        except ValueError:
            area_label.setText("0.00 متر²")
            price_label.setText("-")
        
        # تحديث المساحة والسعر الإجمالي
        self.update_total()
    
    def update_total(self):
        """تحديث المساحة الكلية والسعر الإجمالي بعد التقريب"""
        total_area = 0
        
        # جمع كل المساحات الفردية
        for length, width, _, _ in self.dimension_inputs:
            try:
                length_val = float(length.text().replace(',', '.') or 0)
                width_val = float(width.text().replace(',', '.') or 0)
                
                if length_val > 0 and width_val > 0:
                    area = length_val * width_val
                    total_area += area
            except ValueError:
                continue
        
        # تقريب المساحة الكلية حسب القاعدة المطلوبة
        decimal_part = total_area - int(total_area)
        if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
            total_area = int(total_area) + 1
        else:  # إذا كان الكسر أقل من 0.5
            total_area = int(total_area)
        
        # حساب السعر الإجمالي على المساحة المقربة فقط
        total_price = total_area * self.price_per_meter
        
        # تحديث العرض
        self.total_area_label.setText(f"المساحة الكلية (بعد التقريب): {total_area:.2f} متر²")
        self.total_price_label.setText(f"السعر الإجمالي: {total_price:,.0f} دينار")
    
    def get_dimensions(self):
        """الحصول على أبعاد السجاد المدخلة"""
        dimensions = []
        for length_input, width_input, _, _ in self.dimension_inputs:
            try:
                length = float(length_input.text().replace(',', '.'))
                width = float(width_input.text().replace(',', '.'))
                if length > 0 and width > 0:
                    dimensions.append({
                        'length': length,
                        'width': width
                    })
            except (ValueError, TypeError):
                continue
        return dimensions
