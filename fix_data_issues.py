#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح مشاكل البيانات في MySQL
"""

import os
import sys
import traceback
from datetime import datetime

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def fix_data_issues():
    """إصلاح مشاكل البيانات"""
    try:
        print("\n" + "=" * 60)
        print("إصلاح مشاكل البيانات في MySQL".center(60))
        print("=" * 60)
        
        from db.models import Database
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # 1. إصلاح التواريخ الفارغة في جدول الطلبات
        print("\n1. إصلاح التواريخ في جدول الطلبات:")
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute("UPDATE orders SET created_at = %s WHERE created_at IS NULL OR created_at = ''", (current_time,))
        updated_rows = cursor.rowcount
        print(f"✓ تم إصلاح {updated_rows} تاريخ في جدول الطلبات")
        
        # 2. التحقق من عدد الطلبات
        cursor.execute("SELECT COUNT(*) FROM orders")
        orders_count = cursor.fetchone()[0]
        print(f"✓ إجمالي الطلبات: {orders_count}")
        
        # 3. التحقق من الطلبات حسب الحالة
        print("\n2. الطلبات حسب الحالة:")
        cursor.execute("SELECT status, COUNT(*) FROM orders GROUP BY status")
        status_counts = cursor.fetchall()
        for status, count in status_counts:
            print(f"  - {status}: {count} طلب")
        
        # 4. إضافة إعدادات افتراضية إذا لم توجد
        print("\n3. التحقق من الإعدادات:")
        cursor.execute("SELECT COUNT(*) FROM settings")
        settings_count = cursor.fetchone()[0]
        
        if settings_count == 0:
            cursor.execute("""
                INSERT INTO settings (price_per_meter, blanket_price, delivery_price)
                VALUES (%s, %s, %s)
            """, (1000.0, 5000.0, 2000.0))
            print("✓ تم إضافة الإعدادات الافتراضية")
        else:
            print(f"✓ الإعدادات موجودة ({settings_count} إعداد)")
        
        # 5. التحقق من المندوبين
        print("\n4. التحقق من المندوبين:")
        cursor.execute("SELECT COUNT(*) FROM delegates")
        delegates_count = cursor.fetchone()[0]
        print(f"✓ عدد المندوبين: {delegates_count}")
        
        if delegates_count > 0:
            cursor.execute("SELECT id, name FROM delegates")
            delegates = cursor.fetchall()
            print("المندوبين:")
            for delegate in delegates:
                print(f"  - ID: {delegate[0]}, الاسم: {delegate[1]}")
        
        # 6. إضافة أبعاد السجاد للطلبات التي لا تحتوي على أبعاد
        print("\n5. التحقق من أبعاد السجاد:")
        cursor.execute("SELECT COUNT(*) FROM carpet_dimensions")
        dimensions_count = cursor.fetchone()[0]
        print(f"✓ عدد أبعاد السجاد: {dimensions_count}")
        
        # إضافة أبعاد افتراضية للطلبات التي لا تحتوي على أبعاد
        cursor.execute("""
            SELECT o.id, o.total_area 
            FROM orders o 
            LEFT JOIN carpet_dimensions cd ON o.id = cd.order_id 
            WHERE cd.id IS NULL AND o.total_area > 0
        """)
        orders_without_dimensions = cursor.fetchall()
        
        if orders_without_dimensions:
            print(f"إضافة أبعاد افتراضية لـ {len(orders_without_dimensions)} طلب...")
            for order_id, total_area in orders_without_dimensions:
                # إضافة بعد افتراضي (مربع بنفس المساحة)
                side_length = (total_area ** 0.5)  # الجذر التربيعي للمساحة
                cursor.execute("""
                    INSERT INTO carpet_dimensions (order_id, length, width, total_area)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, side_length, side_length, total_area))
            print(f"✓ تم إضافة أبعاد افتراضية لـ {len(orders_without_dimensions)} طلب")
        
        # 7. تحديث أرقام الوصل للطلبات التي لا تحتوي على أرقام
        print("\n6. تحديث أرقام الوصل:")
        cursor.execute("SELECT COUNT(*) FROM orders WHERE receipt_number IS NULL OR receipt_number = ''")
        orders_without_receipt = cursor.fetchone()[0]
        
        if orders_without_receipt > 0:
            cursor.execute("SELECT id FROM orders WHERE receipt_number IS NULL OR receipt_number = ''")
            orders_ids = cursor.fetchall()
            
            for i, (order_id,) in enumerate(orders_ids, 1):
                receipt_number = f"R{order_id:04d}"
                cursor.execute("UPDATE orders SET receipt_number = %s WHERE id = %s", (receipt_number, order_id))
            
            print(f"✓ تم تحديث {orders_without_receipt} رقم وصل")
        else:
            print("✓ جميع الطلبات تحتوي على أرقام وصل")
        
        # حفظ التغييرات
        conn.commit()
        
        # 8. عرض ملخص نهائي
        print("\n" + "=" * 60)
        print("ملخص البيانات النهائي".center(60))
        print("=" * 60)
        
        cursor.execute("SELECT COUNT(*) FROM orders")
        final_orders_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM delegates")
        final_delegates_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM carpet_dimensions")
        final_dimensions_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM settings")
        final_settings_count = cursor.fetchone()[0]
        
        print(f"✅ إجمالي الطلبات: {final_orders_count}")
        print(f"✅ إجمالي المندوبين: {final_delegates_count}")
        print(f"✅ إجمالي أبعاد السجاد: {final_dimensions_count}")
        print(f"✅ إجمالي الإعدادات: {final_settings_count}")
        
        # عرض بعض الطلبات كمثال
        print(f"\nأمثلة على الطلبات:")
        cursor.execute("SELECT id, phone, address, status, receipt_number FROM orders LIMIT 5")
        sample_orders = cursor.fetchall()
        for order in sample_orders:
            print(f"  - ID: {order[0]}, الهاتف: {order[1]}, الحالة: {order[3]}, رقم الوصل: {order[4]}")
        
        conn.close()
        
        print("\n✅ تم إصلاح جميع مشاكل البيانات بنجاح!")
        print("البرنامج الآن جاهز للاستخدام مع جميع البيانات")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إصلاح البيانات: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_data_issues()
