import os
import traceback
import sys
from datetime import datetime

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# استيراد الوحدات من المجلدات الجديدة
from utils.config import setup_arabic_encoding
from PyQt5.QtCore import Qt, QDateTime, QTimeZone
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                              QHBoxLayout, QPushButton, QLabel, QDesktopWidget,
                              QFrame, QGridLayout, QMessageBox, QDialog,
                              QComboBox, QDialogButtonBox, QTableWidget, QTableWidgetItem,
                              QLineEdit, QHeaderView)
from db.models import Database
from src.settings import SettingsWindow
from ui.login import LoginWindow
from src.warehouse import WarehouseWindow
from ui.styles import get_button_style, WINDOW_STYLE, TITLE_CONTAINER_STYLE, TITLE_STYLE, SLOGAN_STYLE, DATE_STYLE, COLORS, MESSAGEBOX_STYLE
from src.delegate_distribution import DistributionWindow
from src.completed_orders import CompletedOrdersWindow
from src.orders import OrdersWindow
from src.delegate_orders_simple import DelegateOrdersWindow

# تهيئة الترميز العربي
try:
    setup_arabic_encoding()
except Exception as e:
    print(f"خطأ في تهيئة الترميز العربي: {str(e)}")
    traceback.print_exc()

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QPushButton, QLabel, QDesktopWidget,
                               QFrame, QGridLayout, QMessageBox, QDialog)
    from PyQt5.QtCore import Qt, QDateTime, QTimeZone
    from PyQt5.QtGui import QFont
except Exception as e:
    print(f"خطأ في استيراد مكتبات PyQt5: {str(e)}")
    traceback.print_exc()
    sys.exit(1)

try:
    from db.models import Database
    from src.settings import SettingsWindow
    from ui.login import LoginWindow
    from src.warehouse import WarehouseWindow
    from ui.styles import get_button_style, WINDOW_STYLE, TITLE_CONTAINER_STYLE, TITLE_STYLE, SLOGAN_STYLE, DATE_STYLE, COLORS, MESSAGEBOX_STYLE
except Exception as e:
    print(f"خطأ في استيراد الملفات المحلية: {str(e)}")
    traceback.print_exc()
    sys.exit(1)

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    def __init__(self):
        super(MainWindow, self).__init__()

        # تهيئة قاعدة البيانات
        try:
            self.db = Database()
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            sys.exit(1)

        # تعيين حدث الإغلاق
        self.setAttribute(Qt.WA_DeleteOnClose)

        self.user = None

        self.setWindowTitle("ادارة برج الوليد لغيل السجاد")
        self.setStyleSheet(WINDOW_STYLE)

        # إنشاء الويدجت المركزي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # تهيئة الواجهة الرئيسية
        self.init_ui()

        # عرض نافذة تسجيل الدخول
        self.show_login()

        # ضبط موقع النافذة في وسط الشاشة
        self.center_window()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setSpacing(15)

        # إنشاء العنوان والشعار
        title_container = QWidget()
        title_container.setObjectName("titleContainer")
        title_container.setStyleSheet(TITLE_CONTAINER_STYLE)
        title_layout = QVBoxLayout(title_container)
        title_layout.setSpacing(0)
        title_layout.setContentsMargins(10, 0, 10, 0)

        # شعار النظام
        title = QLabel("برج الوليد لغسيل السجاد")
        title.setStyleSheet(TITLE_STYLE)
        title.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title)

        # التاريخ والوقت
        baghdad_timezone = QTimeZone(b"Asia/Baghdad")
        current_datetime = QDateTime.currentDateTime()
        baghdad_time = current_datetime.toTimeZone(baghdad_timezone)
        time_format = "yyyy/MM/dd - hh:mm AP"  # AP يضيف AM/PM
        date_time = QLabel(baghdad_time.toString(time_format))
        date_time.setStyleSheet(DATE_STYLE)
        date_time.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(date_time)

        # إضافة حاوية العنوان إلى التخطيط الرئيسي
        main_layout.addWidget(title_container)

        # إضافة حقل البحث حسب رقم الهاتف ورقم الوصل
        search_container = QWidget()
        search_container.setObjectName("searchContainer")
        search_container.setStyleSheet("""
            QWidget#searchContainer {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 10px;
            }
            QLineEdit {
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #ced4da;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 5px;
                background-color: #007bff;
                color: white;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
        """)

        search_layout = QVBoxLayout(search_container)
        search_layout.setContentsMargins(20, 10, 20, 10)
        search_layout.setSpacing(10)

        # حقل البحث بواسطة رقم الهاتف
        phone_search_container = QWidget()
        phone_search_layout = QHBoxLayout(phone_search_container)
        phone_search_layout.setContentsMargins(0, 0, 0, 0)

        phone_search_label = QLabel("بحث حسب رقم الهاتف:")
        phone_search_label.setFont(QFont('Arial', 12))
        phone_search_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        phone_search_label.setFixedWidth(100)

        self.phone_search = QLineEdit()
        self.phone_search.setPlaceholderText("أدخل رقم الهاتف هنا...")
        self.phone_search.setAlignment(Qt.AlignRight)
        self.phone_search.setFont(QFont('Arial', 12))
        self.phone_search.setFixedHeight(35)
        # إضافة معالجة حدث الضغط على مفتاح الإدخال
        self.phone_search.returnPressed.connect(self.search_by_phone)

        phone_search_button = QPushButton("بحث")
        phone_search_button.setFont(QFont('Arial', 12))
        phone_search_button.setFixedSize(100, 35)
        phone_search_button.clicked.connect(self.search_by_phone)

        phone_search_layout.addWidget(phone_search_button)
        phone_search_layout.addWidget(self.phone_search)
        phone_search_layout.addWidget(phone_search_label)

        # حقل البحث بواسطة رقم الوصل
        receipt_search_container = QWidget()
        receipt_search_layout = QHBoxLayout(receipt_search_container)
        receipt_search_layout.setContentsMargins(0, 0, 0, 0)

        receipt_search_label = QLabel("بحث حسب رقم الوصل:")
        receipt_search_label.setFont(QFont('Arial', 12))
        receipt_search_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        receipt_search_label.setFixedWidth(100)

        self.receipt_search = QLineEdit()
        self.receipt_search.setPlaceholderText("أدخل رقم الوصل هنا...")
        self.receipt_search.setAlignment(Qt.AlignRight)
        self.receipt_search.setFont(QFont('Arial', 12))
        self.receipt_search.setFixedHeight(35)
        # إضافة معالجة حدث الضغط على مفتاح الإدخال
        self.receipt_search.returnPressed.connect(self.search_by_receipt)

        receipt_search_button = QPushButton("بحث")
        receipt_search_button.setFont(QFont('Arial', 12))
        receipt_search_button.setFixedSize(100, 35)
        receipt_search_button.clicked.connect(self.search_by_receipt)

        receipt_search_layout.addWidget(receipt_search_button)
        receipt_search_layout.addWidget(self.receipt_search)
        receipt_search_layout.addWidget(receipt_search_label)

        # إضافة حقول البحث إلى التخطيط الرئيسي
        search_layout.addWidget(phone_search_container)
        search_layout.addWidget(receipt_search_container)

        # إضافة حاوية البحث إلى التخطيط الرئيسي
        main_layout.addWidget(search_container)

        # شبكة الأزرار
        buttons_container = QWidget()
        buttons_container.setObjectName("buttonsContainer")
        buttons_container.setStyleSheet("""
            QWidget#buttonsContainer {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        buttons_grid = QGridLayout(buttons_container)
        buttons_grid.setSpacing(15)
        buttons_grid.setContentsMargins(20, 20, 20, 20)

        # الحصول على عدد الطلبات في كل قسم
        order_counts = self.get_order_counts()

        # إنشاء الأزرار بشكل منفصل لسهولة التحكم

        # الصف الأول - اليمين
        self.new_order_button = QPushButton("إدخال الطلبات")
        self.new_order_button.setStyleSheet(get_button_style(COLORS['success']))
        self.new_order_button.clicked.connect(self.new_order)
        self.new_order_button.setFixedSize(200, 70)
        self.new_order_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.new_order_button, 0, 0)

        # الصف الأول - الوسط
        self.delegate_pickup_orders_button = QPushButton("استلام المندوبين")
        self.delegate_pickup_orders_button.setStyleSheet(get_button_style(COLORS['warning']))
        self.delegate_pickup_orders_button.clicked.connect(self.delegate_pickup_orders)
        self.delegate_pickup_orders_button.setFixedSize(200, 70)
        self.delegate_pickup_orders_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.delegate_pickup_orders_button, 0, 1)

        # الصف الأول - اليسار
        self.manage_delegates_button = QPushButton("الحسابات")
        self.manage_delegates_button.setStyleSheet(get_button_style(COLORS['danger']))
        self.manage_delegates_button.clicked.connect(self.manage_delegates)
        self.manage_delegates_button.setFixedSize(200, 70)
        self.manage_delegates_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.manage_delegates_button, 0, 2)

        # الصف الثاني - اليمين
        self.view_incoming_orders_button = QPushButton(f"الطلبات الواردة ({order_counts['incoming']})")
        self.view_incoming_orders_button.setStyleSheet(get_button_style(COLORS['primary']))
        self.view_incoming_orders_button.clicked.connect(self.view_incoming_orders)
        self.view_incoming_orders_button.setFixedSize(200, 70)
        self.view_incoming_orders_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.view_incoming_orders_button, 1, 0)

        # الصف الثاني - الوسط
        self.delegate_delivery_orders_button = QPushButton(f"توزيع المندوبين ({order_counts['distribution']})")
        self.delegate_delivery_orders_button.setStyleSheet(get_button_style(COLORS['primary']))
        self.delegate_delivery_orders_button.clicked.connect(self.delegate_delivery_orders)
        self.delegate_delivery_orders_button.setFixedSize(200, 70)
        self.delegate_delivery_orders_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.delegate_delivery_orders_button, 1, 1)

        # الصف الثاني - اليسار
        self.view_reports_button = QPushButton("التقارير")
        self.view_reports_button.setStyleSheet(get_button_style(COLORS['warning']))
        self.view_reports_button.clicked.connect(self.view_reports)
        self.view_reports_button.setFixedSize(200, 70)
        self.view_reports_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.view_reports_button, 1, 2)

        # الصف الثالث - اليمين
        self.completed_orders_button = QPushButton("الطلبات المكتملة")
        self.completed_orders_button.setStyleSheet(get_button_style(COLORS['info']))
        self.completed_orders_button.clicked.connect(self.completed_orders)
        self.completed_orders_button.setFixedSize(200, 70)
        self.completed_orders_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.completed_orders_button, 2, 0)

        # الصف الثالث - الوسط
        self.warehouse_orders_button = QPushButton(f"المخزن ({order_counts['warehouse']})")
        self.warehouse_orders_button.setStyleSheet(get_button_style(COLORS['success']))
        self.warehouse_orders_button.clicked.connect(self.warehouse_orders)
        self.warehouse_orders_button.setFixedSize(200, 70)
        self.warehouse_orders_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.warehouse_orders_button, 2, 1)

        # الصف الثالث - اليسار
        self.show_settings_button = QPushButton("إعدادات النظام")
        self.show_settings_button.setStyleSheet(get_button_style(COLORS['info']))
        self.show_settings_button.clicked.connect(self.show_settings)
        self.show_settings_button.setFixedSize(200, 70)
        self.show_settings_button.setFont(QFont('Arial', 20))
        buttons_grid.addWidget(self.show_settings_button, 2, 2)

        # إضافة مسافة بين مجموعات الأزرار
        buttons_grid.setHorizontalSpacing(40)
        buttons_grid.setVerticalSpacing(40)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"background-color: {COLORS['border']}; margin: 0 20px;")
        separator.setFixedHeight(2)

        # إضافة زر الخروج في الأسفل
        logout_button = QPushButton("تسجيل خروج")
        logout_button.setStyleSheet(get_button_style(COLORS['danger']))
        logout_button.clicked.connect(self.logout)
        logout_button.setFixedSize(200, 35)  # تقليل ارتفاع زر الخروج
        logout_button.setFont(QFont('Arial', 11))  # نفس حجم خط الأزرار الأخرى

        # إنشاء حاوية لزر الخروج مع محاذاة للوسط
        logout_container = QWidget()
        logout_layout = QHBoxLayout(logout_container)
        logout_layout.setContentsMargins(10, 5, 10, 5)  # تقليل الهوامش
        logout_layout.addStretch()
        logout_layout.addWidget(logout_button)
        logout_layout.addStretch()

        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addSpacing(15)
        main_layout.addWidget(buttons_container)
        main_layout.addSpacing(15)
        main_layout.addWidget(separator)
        main_layout.addSpacing(5)
        main_layout.addWidget(logout_container)
        main_layout.addSpacing(5)

        # ضبط حجم النافذة
        self.setFixedSize(1100, 680)  # تقليل ارتفاع النافذة بمقدار 2 سم (76 بكسل)

    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            login_window = LoginWindow(self)
            if login_window.exec() == QDialog.Accepted:
                self.current_user = login_window.user['username']  # اسم المستخدم
                self.user = login_window.user  # قاموس بيانات المستخدم
                print(f"تم تسجيل الدخول بنجاح: {self.user}")

                # تحديث واجهة المستخدم
                self.update_ui_for_user()

                # إعادة رسم النافذة
                self.repaint()

                # تحديث التخطيط
                self.centralWidget().layout().update()
            else:
                sys.exit(0)
        except Exception as e:
            print(f"خطأ في عرض نافذة تسجيل الدخول: {str(e)}")
            traceback.print_exc()
            sys.exit(1)

    def update_ui_for_user(self):
        """تحديث واجهة المستخدم حسب صلاحيات المستخدم"""
        if not self.user or not isinstance(self.user, dict):
            return

        # self.user = {'id': id, 'username': username, 'role': role}
        role = self.user.get('role', 'user')  # الصلاحية
        is_admin = (role == 'admin')

        print(f"تحديث واجهة المستخدم للمستخدم: {self.user.get('username')}, الصلاحية: {role}, هل هو مدير؟ {is_admin}")

        # تحديث حالة الأزرار
        # أزرار متاحة للجميع
        self.new_order_button.setEnabled(True)
        self.new_order_button.setVisible(True)

        self.view_incoming_orders_button.setEnabled(True)
        self.view_incoming_orders_button.setVisible(True)

        self.delegate_pickup_orders_button.setEnabled(True)
        self.delegate_pickup_orders_button.setVisible(True)

        self.delegate_delivery_orders_button.setEnabled(True)
        self.delegate_delivery_orders_button.setVisible(True)

        self.warehouse_orders_button.setEnabled(True)
        self.warehouse_orders_button.setVisible(True)

        self.completed_orders_button.setEnabled(True)
        self.completed_orders_button.setVisible(True)

        # أزرار خاصة بالمدير فقط
        print(f"تحديث أزرار المدير: {is_admin}")
        self.manage_delegates_button.setEnabled(is_admin)
        self.manage_delegates_button.setVisible(is_admin)

        self.view_reports_button.setEnabled(is_admin)
        self.view_reports_button.setVisible(is_admin)

        self.show_settings_button.setEnabled(is_admin)
        self.show_settings_button.setVisible(is_admin)

        # تحديث عدد الطلبات على الأزرار
        self.update_order_counts()

        # إعادة ترتيب الأزرار لإظهار/إخفاء الأزرار المخفية
        self.centralWidget().layout().update()

        # إعادة رسم النافذة
        self.repaint()

    def update_order_counts(self):
        """تحديث عدد الطلبات على الأزرار"""
        try:
            # الحصول على عدد الطلبات المحدث
            order_counts = self.get_order_counts()

            # تحديث نص الأزرار
            self.view_incoming_orders_button.setText(f"الطلبات الواردة ({order_counts['incoming']})")
            self.completed_orders_button.setText("الطلبات المكتملة")
            self.delegate_pickup_orders_button.setText(f"استلام المندوبين ({order_counts['pickup']})")
            self.delegate_delivery_orders_button.setText(f"توزيع المندوبين ({order_counts['distribution']})")
            self.warehouse_orders_button.setText(f"المخزن ({order_counts['warehouse']})")
        except Exception as e:
            print(f"خطأ في تحديث عدد الطلبات: {str(e)}")
            import traceback
            traceback.print_exc()

    def new_order(self):
        """فتح نافذة طلب جديد"""
        try:
            print("جاري فتح نافذة طلب جديد...")
            from ui.forms import OrderForm
            print("تم استيراد OrderForm بنجاح")
            dialog = OrderForm(self)
            print("تم إنشاء نافذة OrderForm بنجاح")
            if dialog.exec() == QDialog.Accepted:
                # تحديث جدول الطلبات الواردة إذا كان موجوداً
                if hasattr(self, 'incoming_orders_table'):
                    self.incoming_orders_table.load_orders()
                # تحديث عدد الطلبات على الأزرار
                self.update_order_counts()
        except Exception as e:
            import traceback
            print(f"خطأ في فتح نافذة الطلب الجديد: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة الطلب الجديد: {str(e)}")

    def view_incoming_orders(self):
        """عرض الطلبات الواردة"""
        try:
            from src.orders import OrdersWindow
            self.incoming_orders_dialog = OrdersWindow(self)
            self.incoming_orders_dialog.exec()
            # تحديث عدد الطلبات على الأزرار بعد العودة من النافذة
            self.update_order_counts()
        except Exception as e:
            print(f"خطأ في عرض الطلبات الواردة: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء عرض الطلبات الواردة")

    def delegate_pickup_orders(self):
        """عرض قائمة استلام المندوبين"""
        try:
            from src.delegate_orders_simple import DelegateOrdersWindow
            dialog = DelegateOrdersWindow(parent=self, db=self.db)
            dialog.show()
            # تحديث عدد الطلبات على الأزرار بعد العودة من النافذة
            self.update_order_counts()
        except Exception as e:
            print(f"خطأ في عرض قائمة استلام المندوبين: {str(e)}")
            print("التفاصيل الكاملة للخطأ:")
            print(traceback.format_exc())
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء عرض قائمة استلام المندوبين")

    def delegate_delivery_orders(self):
        """عرض قائمة توزيع المندوبين"""
        try:
            from src.delegate_delivery import DeliveryDelegatesWindow
            delivery_window = DeliveryDelegatesWindow(self)
            delivery_window.show()
            # تحديث عدد الطلبات على الأزرار بعد العودة من النافذة
            self.update_order_counts()
        except Exception as e:
            print(f"خطأ في عرض قائمة توزيع المندوبين: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء عرض قائمة توزيع المندوبين")

    def warehouse_orders(self):
        """فتح نافذة المخزن"""
        try:
            self.warehouse_window = WarehouseWindow()
            self.warehouse_window.show()
            # تحديث عدد الطلبات على الأزرار بعد العودة من النافذة
            self.update_order_counts()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة المخزن: {str(e)}")

    def completed_orders(self):
        """عرض الطلبات المكتملة"""
        try:
            completed_window = CompletedOrdersWindow(self)
            completed_window.exec()
            # تحديث عدد الطلبات على الأزرار بعد العودة من النافذة
            self.update_order_counts()
        except Exception as e:
            print(f"خطأ في عرض الطلبات المكتملة: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء عرض الطلبات المكتملة")

    def manage_delegates(self):
        """الحسابات"""
        try:
            # طباعة رسالة تشخيصية
            print("جاري فتح نافذة الحسابات...")

            # استيراد الملف
            import importlib.util
            import os

            # التأكد من وجود الملف
            accounts_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "accounts.py")
            print(f"مسار ملف الحسابات: {accounts_path}")
            print(f"هل الملف موجود؟ {os.path.exists(accounts_path)}")

            if os.path.exists(accounts_path):
                # استيراد الملف ديناميكيًا
                spec = importlib.util.spec_from_file_location("accounts_module", accounts_path)
                accounts_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(accounts_module)

                # الوصول إلى الفئة
                AccountsWindow = accounts_module.AccountsWindow
                print("تم استيراد AccountsWindow بنجاح")

                # إنشاء النافذة
                self.accounts_window = AccountsWindow()  # حفظ النافذة كمتغير عضو
                print("تم إنشاء نافذة AccountsWindow بنجاح")

                # عرض النافذة
                self.accounts_window.show()
                print("تم عرض نافذة الحسابات بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "ملف الحسابات غير موجود")
        except Exception as e:
            print(f"خطأ في عرض نافذة الحسابات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة الحسابات: {str(e)}")

    def view_reports(self):
        """عرض التقارير"""
        try:
            # طباعة رسالة تشخيصية
            print("جاري فتح نافذة التقارير...")

            # استيراد الملف
            import importlib.util
            import os

            # التأكد من وجود الملف
            reports_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reports.py")
            print(f"مسار ملف التقارير: {reports_path}")
            print(f"هل الملف موجود؟ {os.path.exists(reports_path)}")

            if os.path.exists(reports_path):
                # استيراد الملف ديناميكيًا
                spec = importlib.util.spec_from_file_location("reports_module", reports_path)
                reports_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(reports_module)

                # الوصول إلى الفئة
                ReportsWindow = reports_module.ReportsWindow
                print("تم استيراد ReportsWindow بنجاح")

                # إنشاء النافذة
                self.reports_window = ReportsWindow()
                print("تم إنشاء نافذة ReportsWindow بنجاح")

                # عرض النافذة
                self.reports_window.show()
                print("تم عرض نافذة التقارير بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "ملف التقارير غير موجود")
        except Exception as e:
            print(f"خطأ في عرض نافذة التقارير: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة التقارير: {str(e)}")

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        try:
            # طباعة رسالة تشخيصية
            print("جاري فتح نافذة الإعدادات...")

            if not self.user:
                QMessageBox.warning(self, "تنبيه", "الرجاء تسجيل الدخول أولاً")
                return

            # self.user is a dict {'id': id, 'username': username, 'role': role}
            role = self.user.get('role', 'user')

            if role != 'admin':
                QMessageBox.warning(self, "تنبيه", "عذراً، هذه الصفحة متاحة فقط للمدراء")
                return

            # استيراد الملف
            import importlib.util
            import os

            # التأكد من وجود الملف
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.py")
            print(f"مسار ملف الإعدادات: {settings_path}")
            print(f"هل الملف موجود؟ {os.path.exists(settings_path)}")

            if os.path.exists(settings_path):
                # استيراد الملف ديناميكيًا
                spec = importlib.util.spec_from_file_location("settings_module", settings_path)
                settings_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(settings_module)

                # الوصول إلى الفئة
                SettingsWindow = settings_module.SettingsWindow
                print("تم استيراد SettingsWindow بنجاح")

                # إنشاء النافذة
                settings_window = SettingsWindow(self)
                print("تم إنشاء نافذة SettingsWindow بنجاح")

                # تطبيق الستايل
                settings_window.setStyleSheet(MESSAGEBOX_STYLE)  # تطبيق ستايل الإشعارات

                # عرض النافذة
                settings_window.show()
                print("تم عرض نافذة الإعدادات بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "ملف الإعدادات غير موجود")
        except Exception as e:
            print(f"خطأ في عرض نافذة الإعدادات: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة الإعدادات: {str(e)}")

    def search_by_receipt(self):
        """البحث عن طلب حسب رقم الوصل في جميع القوائم"""
        receipt_number = self.receipt_search.text().strip()
        if not receipt_number:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الوصل للبحث")
            return

        # طباعة رقم الوصل للتصحيح
        print(f"البحث عن رقم الوصل: {receipt_number}")

        try:
            # البحث في قاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # قائمة لتخزين نتائج البحث
            results = []

            # 1. البحث في جميع الطلبات بغض النظر عن الحالة
            # التحقق من نوع قاعدة البيانات
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(o.delegate_name, '') as delegate_name,
                           COALESCE((SELECT d.name FROM delegates d WHERE d.id = o.delegate_id), '') as delegate_name_from_delegates,
                           COALESCE((SELECT do.representative_name FROM delegate_orders do WHERE do.order_id = o.id LIMIT 1), '') as representative_name,
                           COALESCE((SELECT r.name FROM representatives r WHERE r.id = o.pickup_representative_id), '') as rep_name
                    FROM orders o
                    WHERE o.receipt_number LIKE %s
                """, (f"%{receipt_number}%",))
            else:
                # استخدام ? مع SQLite
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(o.delegate_name, '') as delegate_name,
                           COALESCE((SELECT d.name FROM delegates d WHERE d.id = o.delegate_id), '') as delegate_name_from_delegates,
                           COALESCE((SELECT do.representative_name FROM delegate_orders do WHERE do.order_id = o.id LIMIT 1), '') as representative_name,
                           COALESCE((SELECT r.name FROM representatives r WHERE r.id = o.pickup_representative_id), '') as rep_name
                    FROM orders o
                    WHERE o.receipt_number LIKE ?
                """, (f"%{receipt_number}%",))
            rows = cursor.fetchall()
            for row in rows:
                # تحديد القسم بناءً على حالة الطلب
                location = "الطلبات الواردة"
                if row[5] == 'warehouse':
                    location = "المخزن"
                elif row[5] == 'with_delegate':
                    location = "استلام المندوبين"
                elif row[5] == 'distribution':
                    location = "توزيع المندوبين"
                elif row[5] == 'completed':
                    location = "الطلبات المكتملة"

                # استخراج اسم المندوب من نتائج الاستعلام
                order_id = row[0]
                delegate_name = ""

                # محاولة الحصول على اسم المندوب من الحقول المختلفة
                if len(row) > 7 and row[7]:  # delegate_name من جدول orders
                    delegate_name = row[7]
                    print(f"وجدت اسم المندوب في حقل delegate_name للطلب {row[1]}: '{delegate_name}'")

                if not delegate_name and len(row) > 8 and row[8]:  # delegate_name_from_delegates
                    delegate_name = row[8]
                    print(f"وجدت اسم المندوب في جدول delegates للطلب {row[1]}: '{delegate_name}'")

                if not delegate_name and len(row) > 9 and row[9]:  # representative_name من جدول delegate_orders
                    delegate_name = row[9]
                    print(f"وجدت اسم المندوب في جدول delegate_orders للطلب {row[1]}: '{delegate_name}'")

                if not delegate_name and len(row) > 10 and row[10]:  # rep_name من جدول representatives
                    delegate_name = row[10]
                    print(f"وجدت اسم المندوب في جدول representatives للطلب {row[1]}: '{delegate_name}'")

                print(f"اسم المندوب النهائي للطلب {row[1]}: '{delegate_name}'")

                results.append({
                    'id': row[0],
                    'receipt_number': row[1],
                    'customer_name': row[4],  # استخدام العنوان كاسم العميل
                    'phone': row[2] or row[3],
                    'status': row[5],
                    'created_at': row[6],
                    'location': location,
                    'delegate_name': delegate_name
                })

            # البحث في جدول delegate_orders للحصول على معلومات إضافية عن المندوبين
            # التحقق من نوع قاعدة البيانات
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(do.representative_name, '') as representative_name
                    FROM delegate_orders do
                    JOIN orders o ON do.order_id = o.id
                    WHERE o.receipt_number LIKE %s
                """, (f"%{receipt_number}%",))
            else:
                # استخدام ? مع SQLite
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(do.representative_name, '') as representative_name
                    FROM delegate_orders do
                    JOIN orders o ON do.order_id = o.id
                    WHERE o.receipt_number LIKE ?
                """, (f"%{receipt_number}%",))

            # طباعة تشخيصية لمعرفة نتائج الاستعلام
            print("\nنتائج البحث في جدول delegate_orders:")
            rows_delegate_orders = cursor.fetchall()
            for row in rows_delegate_orders:
                print(f"الطلب {row[1]}: اسم المندوب = '{row[7] if len(row) > 7 else 'غير متوفر'}'")

            # استعلام إضافي للبحث عن اسم المندوب بطريقة مختلفة
            print("\nاستعلام إضافي للبحث عن اسم المندوب:")
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT o.id, o.receipt_number, d.name as delegate_name
                    FROM orders o
                    LEFT JOIN delegates d ON o.delegate_id = d.id
                    WHERE o.receipt_number LIKE %s
                """, (f"%{receipt_number}%",))
            else:
                cursor.execute("""
                    SELECT o.id, o.receipt_number, d.name as delegate_name
                    FROM orders o
                    LEFT JOIN delegates d ON o.delegate_id = d.id
                    WHERE o.receipt_number LIKE ?
                """, (f"%{receipt_number}%",))

            delegate_info = cursor.fetchall()
            for info in delegate_info:
                print(f"الطلب {info[1]}: اسم المندوب من جدول delegates = '{info[2] if info[2] else 'غير متوفر'}'")

                # تحديث اسم المندوب في النتائج إذا كان متوفراً
                if info[2]:
                    for result in results:
                        if result['receipt_number'] == info[1]:
                            result['delegate_name'] = info[2]
                            print(f"تم تحديث اسم المندوب للطلب {info[1]} إلى '{info[2]}'")
                            break
            rows = cursor.fetchall()
            for row in rows:
                # تحديث معلومات المندوب إذا كان الطلب موجود بالفعل في النتائج
                found = False
                for result in results:
                    if result['id'] == row[0]:
                        # طباعة تشخيصية لمعرفة عملية تحديث اسم المندوب
                        old_name = result['delegate_name']
                        new_name = row[7] if row[7] else ""
                        print(f"تحديث اسم المندوب للطلب {row[1]}: من '{old_name}' إلى '{new_name}'")

                        # تحديث اسم المندوب فقط إذا كان الاسم الجديد غير فارغ
                        if new_name:
                            result['delegate_name'] = new_name
                        found = True
                        break

                # إضافة الطلب إذا لم يكن موجودًا بالفعل
                if not found:
                    location = "استلام المندوبين"
                    if row[5] == 'distribution':
                        location = "توزيع المندوبين"

                    # البحث عن اسم المندوب من جدول delegates
                    order_id = row[0]
                    rep_name = row[7] if row[7] else ""

                    # استعلام مباشر للحصول على اسم المندوب من جدول delegates
                    try:
                        cursor.execute("""
                            SELECT d.name FROM delegates d
                            JOIN orders o ON o.delegate_id = d.id
                            WHERE o.id = ?
                        """, (order_id,))
                        delegate_result = cursor.fetchone()
                        if delegate_result and delegate_result[0]:
                            rep_name = delegate_result[0]
                            print(f"وجدت اسم المندوب في جدول delegates للطلب {row[1]}: '{rep_name}'")
                    except Exception as e:
                        print(f"خطأ في البحث عن اسم المندوب من جدول delegates: {str(e)}")

                    print(f"اسم المندوب النهائي للطلب {row[1]}: '{rep_name}'")

                    results.append({
                        'id': row[0],
                        'receipt_number': row[1],
                        'customer_name': row[4],  # استخدام العنوان كاسم العميل
                        'phone': row[2] or row[3],
                        'status': row[5],
                        'created_at': row[6],
                        'location': location,
                        'delegate_name': rep_name
                    })

            # عرض نتيجة البحث
            if results:
                # إنشاء نافذة منبثقة لعرض النتائج
                dialog = QDialog(self)
                dialog.setWindowTitle("نتائج البحث")
                dialog.setMinimumWidth(800)
                dialog.setMinimumHeight(400)

                layout = QVBoxLayout(dialog)

                # إنشاء جدول لعرض النتائج
                table = QTableWidget()
                table.setColumnCount(6)
                table.setHorizontalHeaderLabels(["رقم الوصل", "اسم الزبون", "رقم الهاتف", "القسم", "اسم المندوب", "تاريخ الإنشاء"])
                table.setRowCount(len(results))

                # تعيين عنوان النافذة ليشمل رقم الوصل المبحوث عنه
                dialog.setWindowTitle(f"نتائج البحث عن رقم الوصل: {receipt_number} - تم العثور على {len(results)} طلب")

                # ملء الجدول بالنتائج
                for i, result in enumerate(results):
                    # طباعة تشخيصية لمعرفة قيم النتائج
                    print(f"نتيجة البحث {i+1}:")
                    print(f"  رقم الوصل: {result['receipt_number']}")
                    print(f"  اسم الزبون: {result['customer_name']}")
                    print(f"  رقم الهاتف: {result['phone']}")
                    print(f"  القسم: {result['location']}")
                    print(f"  اسم المندوب: '{result['delegate_name']}'")
                    print(f"  تاريخ الإنشاء: {result['created_at']}")

                    # إضافة البيانات إلى الجدول
                    table.setItem(i, 0, QTableWidgetItem(str(result['receipt_number'])))
                    table.setItem(i, 1, QTableWidgetItem(result['customer_name']))
                    table.setItem(i, 2, QTableWidgetItem(result['phone']))
                    table.setItem(i, 3, QTableWidgetItem(result['location']))

                    # استخدام اسم المندوب المخزن في النتائج
                    delegate_name = result.get('delegate_name', '')
                    print(f"  اسم المندوب المستخرج من النتائج: {delegate_name}")

                    # إضافة اسم المندوب إلى الجدول
                    table.setItem(i, 4, QTableWidgetItem(delegate_name if delegate_name is not None else ""))

                    # معالجة التاريخ بشكل صحيح
                    created_at = result['created_at']
                    if created_at:
                        try:
                            # التحقق من نوع البيانات
                            if isinstance(created_at, str):
                                # إذا كان التاريخ نصاً، نعرضه كما هو
                                date_str = created_at
                            else:
                                # إذا كان كائن datetime، نحوله إلى نص
                                date_str = created_at.strftime("%Y-%m-%d")
                        except Exception as e:
                            # في حالة حدوث خطأ، نعرض التاريخ كما هو
                            print(f"خطأ في تحويل التاريخ: {str(e)}")
                            date_str = str(created_at)
                    else:
                        date_str = ""

                    table.setItem(i, 5, QTableWidgetItem(date_str))

                # تعديل عرض الجدول
                table.setStyleSheet("""
                    QTableWidget {
                        border: 1px solid #d3d3d3;
                        border-radius: 5px;
                        font-size: 14px;
                    }
                    QHeaderView::section {
                        background-color: #f0f0f0;
                        padding: 5px;
                        border: 1px solid #d3d3d3;
                        font-weight: bold;
                    }
                    QTableWidget::item {
                        padding: 5px;
                    }
                """)
                table.horizontalHeader().setStretchLastSection(True)
                table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

                # إضافة الجدول إلى التخطيط
                layout.addWidget(table)

                # إضافة زر إغلاق
                close_button = QPushButton("إغلاق")
                close_button.clicked.connect(dialog.accept)
                close_button.setStyleSheet(get_button_style(COLORS['primary']))
                close_button.setFixedSize(120, 30)

                button_layout = QHBoxLayout()
                button_layout.addStretch()
                button_layout.addWidget(close_button)
                button_layout.addStretch()

                layout.addLayout(button_layout)

                # عرض النافذة المنبثقة
                dialog.exec_()
            else:
                QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على طلب بهذا الرقم")

            conn.close()

        except Exception as e:
            print(f"خطأ في البحث عن الطلب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()

    def search_by_phone(self):
        """البحث عن طلب حسب رقم الهاتف في جميع القوائم"""
        phone_number = self.phone_search.text().strip()
        if not phone_number:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الهاتف للبحث")
            return

        # تنظيف رقم الهاتف (إزالة المسافات والرموز غير الضرورية)
        phone_number = ''.join(c for c in phone_number if c.isdigit())

        # طباعة رقم الهاتف للتصحيح
        print(f"البحث عن رقم الهاتف: {phone_number}")

        try:
            # البحث في قاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # قائمة لتخزين نتائج البحث
            results = []

            # 1. البحث في جميع الطلبات بغض النظر عن الحالة
            # التحقق من نوع قاعدة البيانات
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(o.delegate_name, '') as delegate_name,
                           COALESCE((SELECT d.name FROM delegates d WHERE d.id = o.delegate_id), '') as delegate_name_from_delegates,
                           COALESCE((SELECT do.representative_name FROM delegate_orders do WHERE do.order_id = o.id LIMIT 1), '') as representative_name,
                           COALESCE((SELECT r.name FROM representatives r WHERE r.id = o.pickup_representative_id), '') as rep_name
                    FROM orders o
                    WHERE (o.phone LIKE %s OR o.phone2 LIKE %s)
                """, (f"%{phone_number}%", f"%{phone_number}%"))
            else:
                # استخدام ? مع SQLite
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(o.delegate_name, '') as delegate_name,
                           COALESCE((SELECT d.name FROM delegates d WHERE d.id = o.delegate_id), '') as delegate_name_from_delegates,
                           COALESCE((SELECT do.representative_name FROM delegate_orders do WHERE do.order_id = o.id LIMIT 1), '') as representative_name,
                           COALESCE((SELECT r.name FROM representatives r WHERE r.id = o.pickup_representative_id), '') as rep_name
                    FROM orders o
                    WHERE (o.phone LIKE ? OR o.phone2 LIKE ?)
                """, (f"%{phone_number}%", f"%{phone_number}%"))
            rows = cursor.fetchall()
            for row in rows:
                # تحديد القسم بناءً على حالة الطلب
                location = "الطلبات الواردة"
                if row[5] == 'warehouse':
                    location = "المخزن"
                elif row[5] == 'with_delegate':
                    location = "استلام المندوبين"
                elif row[5] == 'distribution':
                    location = "توزيع المندوبين"
                elif row[5] == 'completed':
                    location = "الطلبات المكتملة"

                # استخراج اسم المندوب من نتائج الاستعلام
                order_id = row[0]
                delegate_name = ""

                # محاولة الحصول على اسم المندوب من الحقول المختلفة
                if len(row) > 7 and row[7]:  # delegate_name من جدول orders
                    delegate_name = row[7]
                    print(f"وجدت اسم المندوب في حقل delegate_name للطلب {row[1]} (بحث بالهاتف): '{delegate_name}'")

                if not delegate_name and len(row) > 8 and row[8]:  # delegate_name_from_delegates
                    delegate_name = row[8]
                    print(f"وجدت اسم المندوب في جدول delegates للطلب {row[1]} (بحث بالهاتف): '{delegate_name}'")

                if not delegate_name and len(row) > 9 and row[9]:  # representative_name من جدول delegate_orders
                    delegate_name = row[9]
                    print(f"وجدت اسم المندوب في جدول delegate_orders للطلب {row[1]} (بحث بالهاتف): '{delegate_name}'")

                if not delegate_name and len(row) > 10 and row[10]:  # rep_name من جدول representatives
                    delegate_name = row[10]
                    print(f"وجدت اسم المندوب في جدول representatives للطلب {row[1]} (بحث بالهاتف): '{delegate_name}'")

                print(f"اسم المندوب النهائي للطلب {row[1]} (بحث بالهاتف): '{delegate_name}'")

                results.append({
                    'id': row[0],
                    'receipt_number': row[1],
                    'customer_name': row[4],  # استخدام العنوان كاسم العميل
                    'phone': row[2] or row[3],
                    'status': row[5],
                    'created_at': row[6],
                    'location': location,
                    'delegate_name': delegate_name
                })

            # البحث في جدول delegate_orders للحصول على معلومات إضافية عن المندوبين
            # التحقق من نوع قاعدة البيانات
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(do.representative_name, '') as representative_name
                    FROM delegate_orders do
                    JOIN orders o ON do.order_id = o.id
                    WHERE (o.phone LIKE %s OR o.phone2 LIKE %s)
                """, (f"%{phone_number}%", f"%{phone_number}%"))
            else:
                # استخدام ? مع SQLite
                cursor.execute("""
                    SELECT o.id, o.receipt_number, o.phone, o.phone2, o.address, o.status, o.created_at,
                           COALESCE(do.representative_name, '') as representative_name
                    FROM delegate_orders do
                    JOIN orders o ON do.order_id = o.id
                    WHERE (o.phone LIKE ? OR o.phone2 LIKE ?)
                """, (f"%{phone_number}%", f"%{phone_number}%"))

            # طباعة تشخيصية لمعرفة نتائج الاستعلام
            print("\nنتائج البحث في جدول delegate_orders (بحث بالهاتف):")
            rows_delegate_orders = cursor.fetchall()
            for row in rows_delegate_orders:
                print(f"الطلب {row[1]}: اسم المندوب = '{row[7] if len(row) > 7 else 'غير متوفر'}'")

            # استعلام إضافي للبحث عن اسم المندوب بطريقة مختلفة
            print("\nاستعلام إضافي للبحث عن اسم المندوب (بحث بالهاتف):")
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT o.id, o.receipt_number, d.name as delegate_name
                    FROM orders o
                    LEFT JOIN delegates d ON o.delegate_id = d.id
                    WHERE (o.phone LIKE %s OR o.phone2 LIKE %s)
                """, (f"%{phone_number}%", f"%{phone_number}%"))
            else:
                cursor.execute("""
                    SELECT o.id, o.receipt_number, d.name as delegate_name
                    FROM orders o
                    LEFT JOIN delegates d ON o.delegate_id = d.id
                    WHERE (o.phone LIKE ? OR o.phone2 LIKE ?)
                """, (f"%{phone_number}%", f"%{phone_number}%"))

            delegate_info = cursor.fetchall()
            for info in delegate_info:
                print(f"الطلب {info[1]}: اسم المندوب من جدول delegates = '{info[2] if info[2] else 'غير متوفر'}'")

                # تحديث اسم المندوب في النتائج إذا كان متوفراً
                if info[2]:
                    for result in results:
                        if result['receipt_number'] == info[1]:
                            result['delegate_name'] = info[2]
                            print(f"تم تحديث اسم المندوب للطلب {info[1]} إلى '{info[2]}'")
                            break
            rows = cursor.fetchall()
            for row in rows:
                # تحديث معلومات المندوب إذا كان الطلب موجود بالفعل في النتائج
                found = False
                for result in results:
                    if result['id'] == row[0]:
                        # طباعة تشخيصية لمعرفة عملية تحديث اسم المندوب
                        old_name = result['delegate_name']
                        new_name = row[7] if row[7] else ""
                        print(f"تحديث اسم المندوب للطلب {row[1]} (بحث بالهاتف): من '{old_name}' إلى '{new_name}'")

                        # تحديث اسم المندوب فقط إذا كان الاسم الجديد غير فارغ
                        if new_name:
                            result['delegate_name'] = new_name
                        found = True
                        break

                # إضافة الطلب إذا لم يكن موجودًا بالفعل
                if not found:
                    location = "استلام المندوبين"
                    if row[5] == 'distribution':
                        location = "توزيع المندوبين"

                    # البحث عن اسم المندوب من جدول delegates
                    order_id = row[0]
                    rep_name = row[7] if row[7] else ""

                    # استعلام مباشر للحصول على اسم المندوب من جدول delegates
                    try:
                        cursor.execute("""
                            SELECT d.name FROM delegates d
                            JOIN orders o ON o.delegate_id = d.id
                            WHERE o.id = ?
                        """, (order_id,))
                        delegate_result = cursor.fetchone()
                        if delegate_result and delegate_result[0]:
                            rep_name = delegate_result[0]
                            print(f"وجدت اسم المندوب في جدول delegates للطلب {row[1]} (بحث بالهاتف): '{rep_name}'")
                    except Exception as e:
                        print(f"خطأ في البحث عن اسم المندوب من جدول delegates (بحث بالهاتف): {str(e)}")

                    print(f"اسم المندوب النهائي للطلب {row[1]} (بحث بالهاتف): '{rep_name}'")

                    results.append({
                        'id': row[0],
                        'receipt_number': row[1],
                        'customer_name': row[4],  # استخدام العنوان كاسم العميل
                        'phone': row[2] or row[3],
                        'status': row[5],
                        'created_at': row[6],
                        'location': location,
                        'delegate_name': rep_name
                    })

            # عرض نتيجة البحث
            if results:
                # إنشاء نافذة منبثقة لعرض النتائج
                dialog = QDialog(self)
                dialog.setWindowTitle("نتائج البحث")
                dialog.setMinimumWidth(800)
                dialog.setMinimumHeight(400)

                layout = QVBoxLayout(dialog)

                # إنشاء جدول لعرض النتائج
                table = QTableWidget()
                table.setColumnCount(6)
                table.setHorizontalHeaderLabels(["رقم الوصل", "اسم الزبون", "رقم الهاتف", "القسم", "اسم المندوب", "تاريخ الإنشاء"])
                table.setRowCount(len(results))

                # تعيين عنوان النافذة ليشمل رقم الهاتف المبحوث عنه
                dialog.setWindowTitle(f"نتائج البحث عن رقم الهاتف: {phone_number} - تم العثور على {len(results)} طلب")

                # ملء الجدول بالنتائج
                for i, result in enumerate(results):
                    # طباعة تشخيصية لمعرفة قيم النتائج
                    print(f"نتيجة البحث {i+1} (بحث بالهاتف):")
                    print(f"  رقم الوصل: {result['receipt_number']}")
                    print(f"  اسم الزبون: {result['customer_name']}")
                    print(f"  رقم الهاتف: {result['phone']}")
                    print(f"  القسم: {result['location']}")
                    print(f"  اسم المندوب: '{result['delegate_name']}'")
                    print(f"  تاريخ الإنشاء: {result['created_at']}")

                    # إضافة البيانات إلى الجدول
                    table.setItem(i, 0, QTableWidgetItem(str(result['receipt_number'])))
                    table.setItem(i, 1, QTableWidgetItem(result['customer_name']))
                    table.setItem(i, 2, QTableWidgetItem(result['phone']))
                    table.setItem(i, 3, QTableWidgetItem(result['location']))

                    # إضافة اسم المندوب إلى الجدول
                    table.setItem(i, 4, QTableWidgetItem(result['delegate_name'] if result['delegate_name'] is not None else ""))

                    # معالجة التاريخ بشكل صحيح
                    created_at = result['created_at']
                    if created_at:
                        try:
                            # التحقق من نوع البيانات
                            if isinstance(created_at, str):
                                # إذا كان التاريخ نصاً، نعرضه كما هو
                                date_str = created_at
                            else:
                                # إذا كان كائن datetime، نحوله إلى نص
                                date_str = created_at.strftime("%Y-%m-%d")
                        except Exception as e:
                            # في حالة حدوث خطأ، نعرض التاريخ كما هو
                            print(f"خطأ في تحويل التاريخ: {str(e)}")
                            date_str = str(created_at)
                    else:
                        date_str = ""

                    table.setItem(i, 5, QTableWidgetItem(date_str))

                # تعديل عرض الجدول
                table.setStyleSheet("""
                    QTableWidget {
                        border: 1px solid #d3d3d3;
                        border-radius: 5px;
                        font-size: 14px;
                    }
                    QHeaderView::section {
                        background-color: #f0f0f0;
                        padding: 5px;
                        border: 1px solid #d3d3d3;
                        font-weight: bold;
                    }
                    QTableWidget::item {
                        padding: 5px;
                    }
                """)
                table.horizontalHeader().setStretchLastSection(True)
                table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

                # إضافة الجدول إلى التخطيط
                layout.addWidget(table)

                # إضافة زر إغلاق
                close_button = QPushButton("إغلاق")
                close_button.clicked.connect(dialog.accept)
                close_button.setStyleSheet(get_button_style(COLORS['primary']))
                close_button.setFixedSize(120, 30)

                button_layout = QHBoxLayout()
                button_layout.addStretch()
                button_layout.addWidget(close_button)
                button_layout.addStretch()

                layout.addLayout(button_layout)

                # عرض النافذة المنبثقة
                dialog.exec_()
            else:
                QMessageBox.information(self, "نتائج البحث", f"لم يتم العثور على أي طلبات برقم الهاتف: {phone_number}")
                print(f"لم يتم العثور على أي طلبات برقم الهاتف: {phone_number}")

            conn.close()

        except Exception as e:
            print(f"خطأ في البحث عن الطلب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()

    def logout(self):
        """تسجيل الخروج"""
        self.user = None
        self.update_ui_for_user()
        self.show_login()

    def center_window(self):
        """وضع النافذة في وسط الشاشة"""
        frame = self.frameGeometry()
        center = QDesktopWidget().availableGeometry().center()
        frame.moveCenter(center)
        self.move(frame.topLeft())

    def get_order_counts(self):
        """الحصول على عدد الطلبات في كل قسم"""
        counts = {
            'incoming': 0,
            'warehouse': 0,
            'pickup': 0,
            'distribution': 0,
            'completed': 0
        }

        try:
            # الاتصال بقاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # عدد الطلبات الواردة (الجديدة)
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('new', 'pending', 'received', 'جديد', 'قيد الانتظار', 'تم الاستلام') OR status IS NULL")
            else:
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('new', 'pending', 'received', 'جديد', 'قيد الانتظار', 'تم الاستلام') OR status IS NULL")
            counts['incoming'] = cursor.fetchone()[0]

            # عدد الطلبات في المخزن
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('warehouse', 'processing', 'in_warehouse', 'cleaning', 'مخزن', 'في المخزن', 'قيد المعالجة', 'تنظيف')")
            else:
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('warehouse', 'processing', 'in_warehouse', 'cleaning', 'مخزن', 'في المخزن', 'قيد المعالجة', 'تنظيف')")
            counts['warehouse'] = cursor.fetchone()[0]

            # عدد الطلبات في استلام المندوبين
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("""SELECT COUNT(*) FROM orders
                               WHERE id IN (SELECT DISTINCT order_id FROM delegate_orders)
                               """)
            else:
                cursor.execute("""SELECT COUNT(*) FROM orders
                               WHERE id IN (SELECT DISTINCT order_id FROM delegate_orders)
                               """)
            counts['pickup'] = cursor.fetchone()[0]

            # عدد الطلبات في توزيع المندوبين
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT COUNT(*) FROM orders
                    WHERE pickup_representative_id IS NOT NULL
                    AND (status = 'distribution' OR status = 'distribution_delivered')
                """)
            else:
                cursor.execute("""
                    SELECT COUNT(*) FROM orders
                    WHERE pickup_representative_id IS NOT NULL
                    AND (status = 'distribution' OR status = 'distribution_delivered')
                """)
            counts['distribution'] = cursor.fetchone()[0]

            # عدد الطلبات المكتملة
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('completed', 'مكتمل', 'واصل', 'تم التسليم', 'delivered', 'distribution_delivered', 'completed_delivery', 'completed_distribution')")
            else:
                cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('completed', 'مكتمل', 'واصل', 'تم التسليم', 'delivered', 'distribution_delivered', 'completed_delivery', 'completed_distribution')")
            counts['completed'] = cursor.fetchone()[0]

        except Exception as e:
            print(f"خطأ في الحصول على عدد الطلبات: {str(e)}")
            import traceback
            traceback.print_exc()

        return counts

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة الرئيسية للبرنامج.
        هذه الدالة تُستدعى فقط عند إغلاق النافذة الرئيسية، وليس عند إغلاق النوافذ الفرعية مثل نافذة الإعدادات.
        """
        try:
            # الاستفسار عن تأكيد الإغلاق
            reply = QMessageBox.question(
                self,
                "تأكيد الإغلاق",
                "هل أنت متأكد من أنك تريد إغلاق البرنامج؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                # إلغاء حدث الإغلاق إذا ضغط المستخدم على "لا"
                event.ignore()
                return

            # إنشاء نسخة احتياطية عند الإغلاق إذا كان مفعلاً
            from utils.config import load_config
            from utils.backup import create_exit_backup

            config = load_config()
            # التحقق من تفعيل النسخ الاحتياطي والنسخ الاحتياطي عند الخروج
            if config.get('backup_enabled', False) and config.get('exit_backup', False):
                # إنشاء نسخة احتياطية
                success, message = create_exit_backup()
                if success:
                    print("تم إنشاء نسخة احتياطية عند إغلاق البرنامج بنجاح")
                else:
                    print(f"خطأ في إنشاء النسخة الاحتياطية عند الإغلاق: {message}")
            else:
                print("النسخ الاحتياطي عند الخروج غير مفعل")

            # إغلاق قاعدة البيانات
            if hasattr(self, 'db'):
                self.db.close_connection()

            # قبول حدث الإغلاق
            event.accept()

        except Exception as e:
            print(f"خطأ في إغلاق البرنامج: {str(e)}")
            event.accept()



    def format_amount(self, amount):
        """تنسيق المبلغ بوضع فاصلة بعد كل ثلاث مراتب وإزالة الأصفار بعد الفاصلة العشرية"""
        if amount is None:
            return ""

        # تحويل المبلغ إلى نص
        amount_str = str(amount)

        # التحقق من وجود فاصلة عشرية
        if '.' in amount_str:
            integer_part, decimal_part = amount_str.split('.')

            # إزالة الأصفار النهائية من الجزء العشري
            decimal_part = decimal_part.rstrip('0')

            # إضافة فواصل للجزء الصحيح
            integer_part = '{:,}'.format(int(integer_part))

            # إعادة بناء الرقم
            if decimal_part:
                return f"{integer_part}.{decimal_part}"
            else:
                return integer_part
        else:
            # إضافة فواصل للرقم الصحيح
            return '{:,}'.format(int(amount_str))

def main():
    """نقطة الدخول الرئيسية للبرنامج"""
    try:
        # التحقق من أرقام الوصل عند بدء التشغيل حسب الإعدادات
        from utils import verify_receipts, config

        # تحميل إعدادات البرنامج
        app_config = config.load_config()
        check_all = app_config.get('check_all_receipts', False)
        check_days = app_config.get('receipt_check_days', 7)

        if check_all:
            print("جاري التحقق من جميع أرقام الوصل...")
        else:
            print(f"جاري التحقق من أرقام الوصل الحديثة (آخر {check_days} أيام)...")

        # تشغيل عملية التحقق
        verify_receipts.run_verification(check_all=check_all, days=check_days)

        app = QApplication(sys.argv)

        # إضافة دعم اللغة العربية للتطبيق
        app.setLayoutDirection(Qt.RightToLeft)
        font = QFont("Arial", 12)
        app.setFont(font)

        window = MainWindow()
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
