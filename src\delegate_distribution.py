from PyQt5.QtWidgets import (Q<PERSON>ial<PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QMessageBox, QScrollArea, QWidget, QGridLayout, QLineEdit, QComboBox)
from PyQt5.QtCore import Qt
# تم إزالة import sqlite3 - نستخدم MySQL فقط
import logging
# تم إزالة rep_orders_view - نستخدم delegate_orders_simple بدلاً منه
from db.models import Database

class AddRepresentativeDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة مندوب")
        self.setModal(True)
        self.setMinimumWidth(400)

        layout = QVBoxLayout()

        # عنوان النافذة
        title_label = QLabel("أدخل اسم المندوب")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title_label)

        # حقل إدخال اسم المندوب
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المندوب")
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12pt;
            }
        """)
        layout.addWidget(QLabel("اسم المندوب:"))
        layout.addWidget(self.name_input)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 4px;
                padding: 8px;
                min-width: 80px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_btn.clicked.connect(self.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 8px;
                min-width: 80px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    # تم إزالة دالة load_representatives لأننا لم نعد نستخدم القائمة المنسدلة

    def get_name(self):
        """الحصول على اسم المندوب المدخل"""
        return self.name_input.text().strip()

    def get_selected_rep_id(self):
        """الحصول على معرف المندوب المحدد"""
        # نعيد None لأننا لم نعد نستخدم القائمة المنسدلة
        return None

class DistributionWindow(QDialog):
    """نافذة توزيع المندوبين"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة توزيع المندوبين")
        self.setModal(True)
        self.setMinimumSize(1200, 600)

        # إعداد السجل
        self.logger = logging.getLogger('distribution_window')
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # قاموس لتخزين المندوبين النشطين
        self.active_representatives = {}

        self.initUI()

        # تحميل المندوبين النشطين من قاعدة البيانات
        self.load_active_representatives()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout()

        # العنوان
        title_label = QLabel("قائمة توزيع المندوبين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)

        # رسالة القائمة الفارغة
        self.empty_label = QLabel("لا يوجد مندوبين توزيع حالياً")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("font-size: 14pt; color: #7f8c8d; margin: 20px;")
        main_layout.addWidget(self.empty_label)

        # منطقة الأزرار القابلة للتمرير
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.buttons_container = QWidget()
        self.grid_layout = QGridLayout(self.buttons_container)
        self.grid_layout.setSpacing(10)

        self.scroll_area.setWidget(self.buttons_container)
        self.scroll_area.hide()
        main_layout.addWidget(self.scroll_area)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 5px;
                padding: 10px;
                font-size: 12pt;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def load_active_representatives(self):
        """تحميل المندوبين النشطين من قاعدة البيانات"""
        try:
            # استخدام كائن Database بدلاً من الاتصال المباشر بـ SQLite
            db = Database()
            conn = db.get_connection()
            cursor = conn.cursor()

            # نستخدم MySQL فقط

            # استعلام عن المندوبين الذين لديهم طلبات في حالة التوزيع - MySQL فقط
            cursor.execute("""
                SELECT DISTINCT r.id, r.name
                FROM representatives r
                JOIN orders o ON r.id = o.pickup_representative_id
                WHERE o.status = 'distribution' OR o.status = 'distribution_delivered'
            """)

            representatives = cursor.fetchall()

            # التعامل مع النتائج - MySQL فقط
            for rep in representatives:
                rep_id = rep[0]
                rep_name = rep[1]

                self.active_representatives[rep_id] = rep_name

            conn.close()

            # تحديث العرض
            self.update_representatives_display()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المندوبين النشطين: {str(e)}")

    def add_new_representative(self):
        """إضافة مندوب جديد"""
        dialog = AddRepresentativeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            rep_name = dialog.get_name()

            # التحقق من إدخال البيانات الإلزامية
            if not rep_name:
                QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم المندوب")
                return None, None

            try:
                # استخدام كائن Database بدلاً من الاتصال المباشر بـ SQLite
                db = Database()
                conn = db.get_connection()
                cursor = conn.cursor()

                # البحث عن المندوب بالاسم في قاعدة البيانات - MySQL فقط
                cursor.execute("SELECT id FROM representatives WHERE name = %s", (rep_name,))

                existing_rep = cursor.fetchone()

                if existing_rep:
                    # إذا كان المندوب موجوداً بالفعل، استخدم معرفه - MySQL فقط
                    rep_id = existing_rep[0]

                    # التحقق مما إذا كان المندوب موجوداً بالفعل في القائمة النشطة
                    if rep_id in self.active_representatives:
                        QMessageBox.information(self, "معلومات", f"المندوب {rep_name} موجود بالفعل في قائمة التوزيع")
                    else:
                        # إضافة المندوب إلى القائمة النشطة إذا لم يكن موجوداً بالفعل
                        self.active_representatives[rep_id] = rep_name
                        self.update_representatives_display()
                        QMessageBox.information(self, "نجاح", f"تم إضافة المندوب {rep_name} إلى قائمة التوزيع")
                else:
                    # إذا لم يكن المندوب موجوداً، قم بإضافته - MySQL فقط
                    try:
                        cursor.execute(
                            "INSERT INTO representatives (name) VALUES (%s)",
                            (rep_name,)
                        )
                        self.logger.info(f"تم إضافة المندوب {rep_name} بنجاح في MySQL")
                    except Exception as e:
                        self.logger.error(f"خطأ في إضافة المندوب في MySQL: {str(e)}")
                        # إعادة رفع الاستثناء للتعامل معه في الدالة الأم
                        raise

                    # الحصول على معرف المندوب الجديد - MySQL فقط
                    cursor.execute("SELECT LAST_INSERT_ID()")
                    rep_id = cursor.fetchone()[0]

                    # إضافة المندوب إلى القائمة النشطة
                    self.active_representatives[rep_id] = rep_name
                    self.update_representatives_display()

                    QMessageBox.information(self, "نجاح", "تم إضافة المندوب بنجاح")

                conn.commit()
                conn.close()

                return rep_id, rep_name

            except Exception as e:
                self.logger.error(f"خطأ في إضافة المندوب: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المندوب: {str(e)}")
                return None, None
        return None, None

    def get_rep_order_count(self, rep_id):
        """حساب عدد الطلبات لمندوب محدد"""
        try:
            # استخدام كائن Database بدلاً من الاتصال المباشر بـ SQLite
            db = Database()
            conn = db.get_connection()
            cursor = conn.cursor()

            # استعلام عن عدد الطلبات للمندوب - MySQL فقط
            cursor.execute("""
                SELECT COUNT(*)
                FROM orders
                WHERE pickup_representative_id = %s AND (status = 'distribution' OR status = 'distribution_delivered')
            """, (rep_id,))

            # التعامل مع النتائج - MySQL فقط
            result = cursor.fetchone()
            count = result[0]
            conn.close()

            return count

        except Exception as e:
            self.logger.error(f"خطأ في حساب عدد الطلبات للمندوب: {str(e)}")
            return 0

    def update_representatives_display(self):
        """تحديث عرض أزرار المندوبين"""
        if self.active_representatives:
            self.empty_label.hide()
            self.scroll_area.show()
        else:
            self.empty_label.show()
            self.scroll_area.hide()
            return

        # مسح الأزرار الحالية
        for i in reversed(range(self.grid_layout.count())):
            self.grid_layout.itemAt(i).widget().setParent(None)

        # إضافة الأزرار الجديدة
        for index, (rep_id, rep_name) in enumerate(self.active_representatives.items()):
            # الحصول على عدد الطلبات لكل مندوب
            order_count = self.get_rep_order_count(rep_id)

            # إنشاء نص الزر مع عدد الطلبات
            button_text = f"{rep_name}\n({order_count})"

            rep_button = QPushButton(button_text)
            rep_button.setMinimumSize(120, 60)
            rep_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-size: 12pt;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            rep_button.clicked.connect(
                lambda checked, rid=rep_id, rname=rep_name: self.show_rep_orders(rid, rname)
            )

            row = index // 6
            col = index % 6
            self.grid_layout.addWidget(rep_button, row, col)

    def show_rep_orders(self, rep_id, rep_name):
        """عرض طلبات المندوب"""
        try:
            # إنشاء اتصال بقاعدة البيانات وتمريره إلى نافذة طلبات المندوب
            from db.models import Database
            db = Database()

            from src.delegate_orders_simple import DelegateOrdersWindow
            orders_window = DelegateOrdersWindow(rep_id, rep_name, self)
            # تعيين قاعدة البيانات
            orders_window.db = db
            orders_window.exec_()
        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة طلبات المندوب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة طلبات المندوب: {str(e)}")