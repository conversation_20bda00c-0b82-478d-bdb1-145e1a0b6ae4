#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإضافة عمود has_delivery_fee إلى جدول orders في قاعدة بيانات MySQL
"""

import pymysql
from utils.config import load_config

def add_has_delivery_fee_column():
    """إضافة عمود has_delivery_fee إلى جدول orders في قاعدة بيانات MySQL"""
    # تحميل إعدادات البرنامج
    config = load_config()
    
    # إعدادات الاتصال بـ MySQL
    mysql_config = {
        'host': config.get('db_host', 'localhost'),
        'user': config.get('db_user', 'carpet_user'),
        'password': config.get('db_password', '@#57111819752#@'),
        'database': config.get('db_name', 'carpet_cleaning_service'),
        'port': int(config.get('db_port', '3306')),
        'charset': 'utf8mb4'
    }
    
    conn = None
    try:
        # الاتصال بقاعدة البيانات
        print(f"محاولة الاتصال بقاعدة بيانات MySQL: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        # التحقق من وجود عمود has_delivery_fee في جدول orders
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'has_delivery_fee'")
        if not cursor.fetchone():
            print("إضافة عمود has_delivery_fee إلى جدول orders...")
            cursor.execute("ALTER TABLE orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
            print("تم إضافة عمود has_delivery_fee بنجاح.")
        else:
            print("عمود has_delivery_fee موجود بالفعل في جدول orders.")
        
        # التحقق من وجود عمود delivery_fee في جدول orders
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'delivery_fee'")
        if not cursor.fetchone():
            print("إضافة عمود delivery_fee إلى جدول orders...")
            cursor.execute("ALTER TABLE orders ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00")
            print("تم إضافة عمود delivery_fee بنجاح.")
        else:
            print("عمود delivery_fee موجود بالفعل في جدول orders.")
        
        # التحقق من وجود عمود transport_fees في جدول orders
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'transport_fees'")
        if not cursor.fetchone():
            print("إضافة عمود transport_fees إلى جدول orders...")
            cursor.execute("ALTER TABLE orders ADD COLUMN transport_fees DECIMAL(10,2) DEFAULT 0.00")
            print("تم إضافة عمود transport_fees بنجاح.")
        else:
            print("عمود transport_fees موجود بالفعل في جدول orders.")
        
        # التحقق من وجود عمود has_delivery_fee في جدول delegate_orders
        cursor.execute("SHOW COLUMNS FROM delegate_orders LIKE 'has_delivery_fee'")
        if not cursor.fetchone():
            print("إضافة عمود has_delivery_fee إلى جدول delegate_orders...")
            cursor.execute("ALTER TABLE delegate_orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
            print("تم إضافة عمود has_delivery_fee بنجاح.")
        else:
            print("عمود has_delivery_fee موجود بالفعل في جدول delegate_orders.")
        
        # حفظ التغييرات
        conn.commit()
        print("تم تحديث هيكل الجداول بنجاح.")
        
    except Exception as e:
        print(f"خطأ في تحديث هيكل الجداول: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()
            print("تم إغلاق الاتصال بقاعدة البيانات.")

if __name__ == "__main__":
    add_has_delivery_fee_column()
