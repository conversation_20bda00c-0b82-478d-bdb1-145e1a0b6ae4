-- انشاء جدول مؤقت بدون عمود customer_name
CREATE TABLE temp_delegate_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    receipt_number TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    carpet_count INTEGER NOT NULL,
    total_price REAL NOT NULL,
    representative_name TEXT NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'pending'
);

-- نقل البيانات من الجدول القديم إلى الجدول المؤقت
INSERT INTO temp_delegate_orders (
    id, 
    receipt_number,
    phone_number,
    carpet_count,
    total_price,
    representative_name,
    order_date,
    status
)
SELECT 
    id,
    receipt_number,
    phone_number,
    carpet_count,
    total_price,
    representative_name,
    order_date,
    status 
FROM delegate_orders;

-- حذف الجدول القديم
DROP TABLE delegate_orders;

-- إعادة تسمية الجدول المؤقت
ALTER TABLE temp_delegate_orders RENAME TO delegate_orders;