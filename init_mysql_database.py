"""
أداة لتهيئة قاعدة بيانات MySQL وإنشاء الجداول اللازمة
"""

import os
import mysql.connector
from mysql.connector import Error

def execute_sql_file(conn, sql_file_path):
    """تنفيذ ملف SQL على قاعدة البيانات"""
    try:
        cursor = conn.cursor()

        # قراءة محتوى ملف SQL
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # تقسيم النص إلى استعلامات منفصلة
        # نفترض أن الاستعلامات مفصولة بـ ';'
        sql_commands = sql_script.split(';')

        # تنفيذ كل استعلام
        for command in sql_commands:
            command = command.strip()
            if command:
                cursor.execute(command)

        conn.commit()
        print("تم تنفيذ ملف SQL بنجاح")
        return True
    except Error as e:
        print(f"خطأ في تنفيذ ملف SQL: {e}")
        return False
    finally:
        if cursor:
            cursor.close()

def init_mysql_database():
    """تهيئة قاعدة بيانات MySQL"""
    # إعدادات الاتصال بـ MySQL باستخدام المستخدم الجذر
    root_password = 'admin123'  # استخدم كلمة المرور الافتراضية أو قم بتغييرها حسب إعدادك

    root_config = {
        'host': 'localhost',
        'user': 'root',
        'password': root_password,  # كلمة مرور المستخدم الجذر
        'port': 3306,
        'charset': 'utf8mb4',
        'raise_on_warnings': True
    }

    # إعدادات الاتصال بـ MySQL باستخدام carpet_user
    user_config = {
        'host': 'localhost',
        'user': 'carpet_user',
        'password': '@#57111819752#@',
        'port': 3306,
        'charset': 'utf8mb4',
        'raise_on_warnings': True
    }

    conn = None
    try:
        # الاتصال بخادم MySQL باستخدام المستخدم الجذر (بدون تحديد قاعدة بيانات)
        print("جاري الاتصال بخادم MySQL باستخدام المستخدم الجذر...")
        conn = mysql.connector.connect(**root_config)
        cursor = conn.cursor()

        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        cursor.execute("CREATE DATABASE IF NOT EXISTS carpet_cleaning_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("تم إنشاء قاعدة البيانات بنجاح")

        # إنشاء المستخدم إذا لم يكن موجوداً ومنحه الصلاحيات
        cursor.execute("CREATE USER IF NOT EXISTS 'carpet_user'@'localhost' IDENTIFIED BY '@#57111819752#@'")
        cursor.execute("CREATE USER IF NOT EXISTS 'carpet_user'@'%' IDENTIFIED BY '@#57111819752#@'")
        cursor.execute("GRANT ALL PRIVILEGES ON carpet_cleaning_service.* TO 'carpet_user'@'localhost'")
        cursor.execute("GRANT ALL PRIVILEGES ON carpet_cleaning_service.* TO 'carpet_user'@'%'")
        cursor.execute("FLUSH PRIVILEGES")
        print("تم إنشاء المستخدم ومنحه الصلاحيات بنجاح")

        # إغلاق الاتصال الحالي
        cursor.close()
        conn.close()

        # إعادة الاتصال مع تحديد قاعدة البيانات باستخدام carpet_user
        print("جاري الاتصال بقاعدة البيانات باستخدام carpet_user...")
        user_config['database'] = 'carpet_cleaning_service'
        conn = mysql.connector.connect(**user_config)

        # تنفيذ ملف SQL لإنشاء الجداول
        sql_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'create_mysql_database.sql')
        if os.path.exists(sql_file_path):
            execute_sql_file(conn, sql_file_path)
        else:
            print(f"ملف SQL غير موجود: {sql_file_path}")

        print("تم تهيئة قاعدة بيانات MySQL بنجاح")
        return True
    except Error as e:
        print(f"خطأ في تهيئة قاعدة بيانات MySQL: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("أداة تهيئة قاعدة بيانات MySQL")
    print("=" * 50)

    success = init_mysql_database()
    if success:
        print("تم تهيئة قاعدة بيانات MySQL بنجاح")
    else:
        print("فشل في تهيئة قاعدة بيانات MySQL")
