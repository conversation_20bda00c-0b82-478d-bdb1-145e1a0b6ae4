#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت التحقق النهائي من أن البرنامج يعمل بشكل صحيح
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def final_verification():
    """التحقق النهائي من البرنامج"""
    try:
        print("\n" + "=" * 60)
        print("التحقق النهائي من البرنامج".center(60))
        print("=" * 60)
        
        # 1. التحقق من قاعدة البيانات
        print("\n1. التحقق من قاعدة البيانات:")
        from db.models import Database
        
        db = Database()
        print(f"✓ تم إنشاء كائن Database")
        print(f"✓ نوع قاعدة البيانات: {db.db_type}")
        
        # 2. اختبار الاتصال
        print("\n2. اختبار الاتصال:")
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✓ عدد المستخدمين: {user_count}")
        
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"✓ عدد الجداول: {len(tables)}")
        
        conn.close()
        
        # 3. اختبار دالة authenticate
        print("\n3. اختبار دالة authenticate:")
        
        # اختبار المستخدم admin
        user = db.authenticate("admin", "admin123")
        if user:
            print(f"✓ تسجيل الدخول نجح للمستخدم: {user['username']}")
            print(f"✓ الدور: {user['role']}")
        else:
            print("✗ فشل تسجيل الدخول للمستخدم admin")
        
        # اختبار كلمة مرور خاطئة
        user_wrong = db.authenticate("admin", "wrong_password")
        if not user_wrong:
            print("✓ رفض كلمة المرور الخاطئة بشكل صحيح")
        else:
            print("✗ قبل كلمة مرور خاطئة!")
        
        # 4. التحقق من واجهة تسجيل الدخول
        print("\n4. التحقق من واجهة تسجيل الدخول:")
        try:
            from ui.login import LoginWindow
            print("✓ تم استيراد واجهة تسجيل الدخول")
        except Exception as e:
            print(f"✗ خطأ في استيراد واجهة تسجيل الدخول: {str(e)}")
        
        # 5. التحقق من الواجهة الرئيسية
        print("\n5. التحقق من الواجهة الرئيسية:")
        try:
            from src.main import MainWindow
            print("✓ تم استيراد الواجهة الرئيسية")
        except Exception as e:
            print(f"✗ خطأ في استيراد الواجهة الرئيسية: {str(e)}")
        
        # 6. التحقق من ملف التكوين
        print("\n6. التحقق من ملف التكوين:")
        import json
        
        config_file = "app_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✓ ملف التكوين موجود")
            print(f"✓ نوع قاعدة البيانات: {config.get('db_type')}")
            print(f"✓ خادم قاعدة البيانات: {config.get('db_host')}")
            print(f"✓ اسم قاعدة البيانات: {config.get('db_name')}")
        else:
            print("✗ ملف التكوين غير موجود")
        
        # 7. التحقق من عدم وجود ملفات SQLite
        print("\n7. التحقق من عدم وجود ملفات SQLite:")
        sqlite_files = ["carpet_cleaning.db", "carpet_db.db"]
        sqlite_found = False
        
        for file in sqlite_files:
            if os.path.exists(file):
                print(f"✗ تم العثور على ملف SQLite: {file}")
                sqlite_found = True
            else:
                print(f"✓ لا يوجد ملف SQLite: {file}")
        
        if not sqlite_found:
            print("✓ تم حذف جميع ملفات SQLite بنجاح")
        
        # 8. التحقق من النسخة الاحتياطية
        print("\n8. التحقق من النسخة الاحتياطية:")
        backup_dir = "sqlite_backup"
        if os.path.exists(backup_dir):
            backup_files = os.listdir(backup_dir)
            print(f"✓ تم العثور على مجلد النسخة الاحتياطية مع {len(backup_files)} ملف")
        else:
            print("✗ لم يتم العثور على مجلد النسخة الاحتياطية")
        
        print("\n" + "=" * 60)
        print("ملخص التحقق النهائي".center(60))
        print("=" * 60)
        
        print("✅ البرنامج جاهز للاستخدام!")
        print("✅ قاعدة البيانات MySQL تعمل بشكل صحيح")
        print("✅ تسجيل الدخول يعمل بشكل صحيح")
        print("✅ تم حذف جميع ملفات SQLite")
        print("✅ تم إنشاء نسخة احتياطية من البيانات")
        
        print("\n" + "=" * 60)
        print("بيانات تسجيل الدخول المتاحة".center(60))
        print("=" * 60)
        
        print("1. المدير الرئيسي:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("   الدور: admin")
        
        print("\n2. المستخدم زياد:")
        print("   اسم المستخدم: زياد")
        print("   كلمة المرور: 9999")
        
        print("\n3. المستخدم احمد:")
        print("   اسم المستخدم: احمد")
        print("   كلمة المرور: 2222")
        
        print("\n" + "=" * 60 + "\n")
        
    except Exception as e:
        print(f"خطأ في التحقق النهائي: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    final_verification()
