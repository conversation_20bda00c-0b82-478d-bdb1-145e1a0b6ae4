"""
تثبيت حزم Python اللازمة للاتصال بـ MySQL
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"تم تثبيت حزمة {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت حزمة {package}: {e}")
        return False

def main():
    """تثبيت جميع الحزم اللازمة"""
    print("جاري تثبيت حزم Python اللازمة للاتصال بـ MySQL...")
    
    # قائمة الحزم المطلوبة
    packages = [
        "mysql-connector-python",
        "sqlalchemy"
    ]
    
    # تثبيت كل حزمة
    success = True
    for package in packages:
        if not install_package(package):
            success = False
    
    if success:
        print("\nتم تثبيت جميع الحزم بنجاح!")
    else:
        print("\nحدثت بعض الأخطاء أثناء تثبيت الحزم. يرجى التحقق من الرسائل السابقة.")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
