from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QSpinBox, QPushButton, QMessageBox,
                            QListWidget, QListWidgetItem, QWidget, QFormLayout,
                            QScrollArea)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QKeyEvent
from db.models import Database
from src.carpet_dimensions import CarpetDimensionsDialog

class DelegateOrderEditDialog(QDialog):
    """نافذة تعديل طلبات المندوبين"""
    def __init__(self, parent=None, order_id=None, delegate_id=None):
        super().__init__(parent)
        self.db = Database()
        self.order_id = order_id
        self.delegate_id = delegate_id
        self.carpet_dimensions = []
        self.is_loading = True  # إضافة متغير للتحكم في تحميل البيانات
        self.setup_ui()
        self.is_loading = False

        if order_id:
            self.setWindowTitle("تعديل الطلب")
            self.load_order_data()
        else:
            self.setWindowTitle("طلب جديد")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFixedSize(600, 800)  # تعيين حجم ثابت للنافذة

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(10)

        # إنشاء منطقة التمرير
        scroll = QScrollArea(self)
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # إنشاء محتوى منطقة التمرير
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)

        # رقم الوصل
        receipt_layout = QHBoxLayout()
        receipt_label = QLabel("رقم الوصل:")
        self.receipt_number = QLineEdit()
        receipt_layout.addWidget(receipt_label)
        receipt_layout.addWidget(self.receipt_number)
        layout.addLayout(receipt_layout)

        # عدد السجاد وعدد البطانيات
        self.form_layout = QFormLayout()
        self.carpet_count_label = QLabel("عدد السجاد:")
        self.carpet_count_input = QSpinBox()
        self.carpet_count_input.setMinimum(1)
        self.carpet_count_input.setButtonSymbols(QSpinBox.NoButtons)  # إخفاء الأسهم
        self.carpet_count_input.valueChanged.connect(self.on_carpet_count_changed)
        # تثبيت معالج حدث المفاتيح لحقل عدد السجاد
        self.carpet_count_input.installEventFilter(self)
        self.form_layout.addRow(self.carpet_count_label, self.carpet_count_input)

        self.blanket_count_label = QLabel("عدد البطانيات:")
        self.blanket_count_input = QLineEdit()
        self.form_layout.addRow(self.blanket_count_label, self.blanket_count_input)

        layout.addLayout(self.form_layout)

        # زر إدخال الأبعاد
        self.dimensions_btn = QPushButton("إدخال الأبعاد")
        self.dimensions_btn.clicked.connect(self.show_dimensions_dialog)
        layout.addWidget(self.dimensions_btn)

        # قائمة أبعاد السجاد
        dimensions_label = QLabel("أبعاد السجاد:")
        layout.addWidget(dimensions_label)
        self.dimensions_list = QListWidget()
        layout.addWidget(self.dimensions_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # المسافة بين الأزرار

        # إنشاء حاوية للأزرار لتحديد عرضها
        buttons_container = QWidget()
        buttons_container.setFixedWidth(200)  # تحديد عرض حاوية الأزرار
        buttons_container_layout = QHBoxLayout(buttons_container)
        buttons_container_layout.setContentsMargins(0, 0, 0, 0)

        save_btn = QPushButton("حفظ")
        save_btn.setFixedWidth(90)  # تحديد عرض زر الحفظ
        save_btn.clicked.connect(self.save_order)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFixedWidth(90)  # تحديد عرض زر الإلغاء
        cancel_btn.clicked.connect(self.reject)

        buttons_container_layout.addWidget(save_btn)
        buttons_container_layout.addWidget(cancel_btn)

        buttons_layout.addWidget(buttons_container, alignment=Qt.AlignCenter)
        layout.addLayout(buttons_layout)

        # إضافة المحتوى إلى منطقة التمرير
        scroll.setWidget(content_widget)
        main_layout.addWidget(scroll)

    def eventFilter(self, obj, event):
        """معالجة أحداث المفاتيح للعناصر"""
        # معالجة مفتاح التاب في حقل عدد السجاد
        if obj == self.carpet_count_input and event.type() == QKeyEvent.KeyPress:
            # إذا كان المفتاح هو التاب، افتح نافذة أبعاد السجاد
            if event.key() == Qt.Key_Tab:
                self.show_dimensions_dialog()
                return True
            # إذا كان المفتاح هو الإنتر، انتقل إلى حقل عدد البطانيات
            elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                return True
        return super().eventFilter(obj, event)

    def load_order_data(self):
        """تحميل بيانات الطلب"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT receipt_number, carpet_count, blanket_count
                FROM orders WHERE id = ?
            """, (self.order_id,))

            row = cursor.fetchone()
            if row:
                self.is_loading = True  # تعيين العلم لتجنب فتح نافذة الأبعاد
                self.receipt_number.setText(row[0] or "")
                self.carpet_count_input.setValue(row[1] or 1)
                self.blanket_count_input.setText(str(row[2] or ""))
                self.is_loading = False

                # تحميل أبعاد السجاد
                self.load_carpet_dimensions()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطلب: {str(e)}")

    def load_carpet_dimensions(self):
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT length, width FROM carpet_dimensions
                WHERE order_id = ?
            """, (self.order_id,))

            self.carpet_dimensions = [(row[0], row[1]) for row in cursor.fetchall()]
            self.update_dimensions_list()

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل أبعاد السجاد: {str(e)}")

    def on_carpet_count_changed(self, value):
        """معالجة تغيير عدد السجاد"""
        if not self.is_loading:  # تجنب فتح النافذة عند تحميل البيانات
            self.carpet_dimensions = []  # إعادة تعيين الأبعاد
            self.update_dimensions_list()

    def show_dimensions_dialog(self):
        """عرض نافذة إدخال الأبعاد"""
        dialog = CarpetDimensionsDialog(self, self.carpet_count_input.value(), self.order_id)
        if dialog.exec() == QDialog.Accepted:
            self.carpet_dimensions = dialog.get_dimensions()
            self.update_dimensions_list()

    def update_dimensions_list(self):
        """تحديث قائمة أبعاد السجاد"""
        self.dimensions_list.clear()
        for i, (length, width) in enumerate(self.carpet_dimensions, 1):
            item = QListWidgetItem(f"سجادة {i}: {length}م × {width}م")
            self.dimensions_list.addItem(item)

    def save_order(self):
        """حفظ الطلب"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.receipt_number.text().strip():
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الوصل")
                self.receipt_number.setFocus()
                return

            if not self.carpet_dimensions:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال أبعاد السجاد")
                self.dimensions_btn.setFocus()
                return

            # حساب المساحة الكلية
            total_area = sum(length * width for length, width in self.carpet_dimensions)

            # الحصول على سعر المتر المربع وسعر البطانية من الإعدادات
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT price_per_meter, delivery_price FROM settings")
            settings = cursor.fetchone()

            if not settings:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على إعدادات الأسعار")
                return

            meter_price = settings[0]
            delivery_price = settings[1]

            # حساب السعر الإجمالي
            carpet_price = total_area * meter_price
            total_price = carpet_price + delivery_price

            # تحديث بيانات الطلب في قاعدة البيانات
            if self.order_id:  # تعديل طلب موجود
                cursor.execute("""
                    UPDATE orders SET
                    receipt_number = ?, carpet_count = ?, blanket_count = ?, total_area = ?, total_price = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    self.receipt_number.text(),
                    self.carpet_count_input.value(),
                    self.blanket_count_input.text(),
                    total_area,
                    total_price,
                    self.order_id
                ))

                # حذف الأبعاد القديمة
                cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = ?", (self.order_id,))

                # إضافة الأبعاد الجديدة
                for length, width in self.carpet_dimensions:
                    cursor.execute("""
                        INSERT INTO carpet_dimensions (order_id, length, width)
                        VALUES (?, ?, ?)
                    """, (self.order_id, length, width))
            else:  # إنشاء طلب جديد
                cursor.execute("""
                    INSERT INTO orders (
                    receipt_number, order_date, total_area, total_price, delegate_id, created_at, updated_at, carpet_count, blanket_count
                    ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, ?)
                """, (
                    self.receipt_number.text(),
                    QDateTime.currentDateTime().toString(Qt.ISODate),
                    total_area,
                    total_price,
                    self.delegate_id,
                    self.carpet_count_input.value(),
                    self.blanket_count_input.text()
                ))
                self.order_id = cursor.lastrowid

            conn.commit()
            conn.close()

            QMessageBox.information(self, "تم الحفظ", "تم حفظ الطلب بنجاح.")
            self.accept()  # إغلاق النافذة وإرجاع QDialog.Accepted

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الطلب: {str(e)}")
            conn.rollback()
            conn.close()
