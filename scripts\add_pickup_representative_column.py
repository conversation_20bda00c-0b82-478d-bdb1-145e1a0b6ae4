#!/usr/bin/env python
import sqlite3
import os
import sys

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from db.models import Database

def add_pickup_representative_column():
    """Add pickup_representative_id column to orders table if it doesn't exist"""
    try:
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()

        # Check if column exists
        cursor.execute("PRAGMA table_info(orders)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'pickup_representative_id' not in columns:
            print("Adding pickup_representative_id column...")
            cursor.execute("""
                ALTER TABLE orders 
                ADD COLUMN pickup_representative_id INTEGER 
                REFERENCES representatives(id)
            """)
            print("Column added successfully")
        else:
            print("pickup_representative_id column already exists")

        # Create index for better performance
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_orders_pickup_rep 
            ON orders(pickup_representative_id)
        """)
        
        conn.commit()
        print("Schema update completed successfully")
        
    except Exception as e:
        print(f"Error updating schema: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_pickup_representative_column()