from PyQt5.QtWidgets import QLabel
from PyQt5.QtCore import Qt, QTimer

class NotificationLabel(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                background-color: #2ecc71;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
        """)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.hide)
        self.hide()

    def showMessage(self, message, duration=2000):
        self.setText(message)
        self.show()
        self.timer.start(duration)  # إخفاء بعد ثانيتين
