#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت بسيط للتحقق من البيانات
"""

import os
import sys

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from db.models import Database
    
    print("جاري التحقق من البيانات...")
    
    db = Database()
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # التحقق من الطلبات
    cursor.execute("SELECT COUNT(*) FROM orders")
    orders_count = cursor.fetchone()[0]
    print(f"عدد الطلبات: {orders_count}")
    
    # التحقق من المندوبين
    cursor.execute("SELECT COUNT(*) FROM delegates")
    delegates_count = cursor.fetchone()[0]
    print(f"عدد المندوبين: {delegates_count}")
    
    # التحقق من طلبات المندوبين
    cursor.execute("SELECT COUNT(*) FROM delegate_orders")
    delegate_orders_count = cursor.fetchone()[0]
    print(f"عدد طلبات المندوبين: {delegate_orders_count}")
    
    # عرض بعض الطلبات إذا وجدت
    if orders_count > 0:
        cursor.execute("SELECT id, phone, address, status FROM orders LIMIT 3")
        orders = cursor.fetchall()
        print("\nبعض الطلبات:")
        for order in orders:
            print(f"  ID: {order[0]}, الهاتف: {order[1]}, الحالة: {order[3]}")
    else:
        print("\n⚠️ لا توجد طلبات في قاعدة البيانات!")
    
    conn.close()
    
except Exception as e:
    print(f"خطأ: {str(e)}")
    import traceback
    traceback.print_exc()
