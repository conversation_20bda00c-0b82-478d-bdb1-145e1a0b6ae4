from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
                           QLabel, QLineEdit, QPushButton, QFormLayout, QTableWidget,
                           QTableWidgetItem, QFileDialog, QMessageBox, QComboBox, QFrame,
                           QHeaderView, QAction, QMainWindow, QMenuBar, QGroupBox, QCheckBox, QSpinBox,
                           QAbstractItemView, QTimeEdit, QListWidget, QListWidgetItem, QApplication)
from PyQt5.QtCore import Qt, QLocale, QTimer, QTime
from PyQt5.QtGui import QIntValidator, QFont
from db.models import Database
import os
import shutil
import locale
import csv
import sys
from datetime import datetime
from utils.config import load_config, update_config

class DelegateDialog(QDialog):
    """نافذة إضافة/تعديل مندوب"""
    def __init__(self, parent=None, delegate_data=None):
        super().__init__(parent)
        self.db = Database()
        self.delegate_data = delegate_data
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة مندوب جديد" if not self.delegate_data else "تعديل مندوب")
        self.setGeometry(200, 200, 400, 300)

        layout = QVBoxLayout()

        # حقل الاسم
        name_layout = QHBoxLayout()
        name_label = QLabel("الاسم:")
        self.name_input = QLineEdit()
        if self.delegate_data:
            self.name_input.setText(self.delegate_data[1])  # name
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input)

        # حقل رقم الهاتف
        phone_layout = QHBoxLayout()
        phone_label = QLabel("رقم الهاتف:")
        self.phone_input = QLineEdit()
        if self.delegate_data:
            self.phone_input.setText(self.delegate_data[2])  # phone
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone_input)

        # حقل المنطقة
        area_layout = QHBoxLayout()
        area_label = QLabel("المنطقة:")
        self.area_input = QLineEdit()
        if self.delegate_data:
            self.area_input.setText(self.delegate_data[3])  # area
        area_layout.addWidget(area_label)
        area_layout.addWidget(self.area_input)

        # أزرار
        button_layout = QHBoxLayout()
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_delegate)
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        # إضافة كل شيء إلى التخطيط الرئيسي
        layout.addLayout(name_layout)
        layout.addLayout(phone_layout)
        layout.addLayout(area_layout)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def save_delegate(self):
        """حفظ بيانات المندوب"""
        try:
            name = self.name_input.text().strip()
            phone = self.phone_input.text().strip()
            area = self.area_input.text().strip()

            if not name or not phone or not area:
                QMessageBox.warning(self, "خطأ", "جميع الحقول مطلوبة")
                return

            if self.delegate_data:  # تعديل مندوب موجود
                self.db.update_delegate(self.delegate_data[0], name, phone, area)
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات المندوب بنجاح")
            else:  # إضافة مندوب جديد
                delegate_id = self.db.add_delegate(name, phone, area)
                QMessageBox.information(self, "نجاح", "تم إضافة المندوب بنجاح")

            self.accept()

        except ValueError as e:
            QMessageBox.warning(self, "خطأ", str(e))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

class SettingsWindow(QMainWindow):
    """نافذة الإعدادات المبسطة - بدون النسخ الاحتياطي"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = Database()

        # تعيين عنوان النافذة
        self.setWindowTitle("إعدادات النظام")

        # تعيين حجم النافذة
        self.resize(800, 600)

        # تعيين النمط
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                background-color: #f5f6fa;
            }
            QTabBar::tab:selected {
                background-color: #2ecc71;
                color: white;
            }
        """)

        self.init_ui()

        # تأخير استدعاء setup_tab_order حتى يتم إنشاء جميع العناصر
        QTimer.singleShot(100, self.setup_tab_order)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء التبويبات
        self.tabs = QTabWidget()

        # إنشاء التبويبات المختلفة
        price_tab = self.create_price_tab()
        users_tab = self.create_users_tab()
        advanced_tab = self.create_advanced_tab()

        # إضافة التبويبات إلى النافذة
        self.tabs.addTab(price_tab, "الأسعار")
        self.tabs.addTab(users_tab, "المستخدمين")
        self.tabs.addTab(advanced_tab, "إعدادات متقدمة")

        # إضافة التبويبات إلى النافذة
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tabs)

        # إنشاء ويدجت مركزي
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def create_price_tab(self):
        """إنشاء تبويب سعر المتر المربع"""
        price_tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة سعر المتر المربع
        price_group = QGroupBox("سعر المتر المربع")
        price_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        price_layout = QFormLayout()

        # حقل السعر الحالي
        self.current_price_label = QLabel()
        self.current_price_label.setStyleSheet("font-size: 14px;")
        price_layout.addRow("السعر الحالي:", self.current_price_label)

        # حقل السعر الجديد
        self.new_price_input = QLineEdit()
        self.new_price_input.setValidator(QIntValidator(1, 1000000))
        self.new_price_input.setPlaceholderText("أدخل السعر الجديد")
        self.new_price_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        price_layout.addRow("السعر الجديد:", self.new_price_input)

        # زر حفظ السعر
        save_button = QPushButton("حفظ السعر")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_button.clicked.connect(self.save_price)
        price_layout.addRow("", save_button)

        price_group.setLayout(price_layout)
        layout.addWidget(price_group)

        # مجموعة سعر البطانية
        blanket_group = QGroupBox("سعر البطانية")
        blanket_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        blanket_layout = QFormLayout()

        # حقل سعر البطانية الحالي
        self.current_blanket_price_label = QLabel()
        self.current_blanket_price_label.setStyleSheet("font-size: 14px;")
        blanket_layout.addRow("السعر الحالي:", self.current_blanket_price_label)

        # حقل سعر البطانية الجديد
        self.new_blanket_price_input = QLineEdit()
        self.new_blanket_price_input.setValidator(QIntValidator(1, 1000000))
        self.new_blanket_price_input.setPlaceholderText("أدخل سعر البطانية الجديد")
        self.new_blanket_price_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        blanket_layout.addRow("السعر الجديد:", self.new_blanket_price_input)

        # زر حفظ سعر البطانية
        save_blanket_button = QPushButton("حفظ سعر البطانية")
        save_blanket_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_blanket_button.clicked.connect(self.save_blanket_price)
        blanket_layout.addRow("", save_blanket_button)

        blanket_group.setLayout(blanket_layout)
        layout.addWidget(blanket_group)

        layout.addStretch()

        price_tab.setLayout(layout)
        self.update_current_price()
        self.update_current_blanket_price()
        return price_tab
