import os
import shutil
import time
import sqlite3
import logging
from datetime import datetime
from utils.config import load_config, update_config

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'backup_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('backup_system')

def create_backup(db_path='carpet_cleaning.db', custom_path=None):
    """
    إنشاء نسخة احتياطية من قاعدة البيانات
    
    Args:
        db_path (str): مسار ملف قاعدة البيانات
        custom_path (str, optional): مسار مخصص لحفظ النسخة الاحتياطية
    
    Returns:
        tuple: (نجاح العملية (bool), رسالة (str), مسار النسخة الاحتياطية (str))
    """
    try:
        # إضافة تسجيل بداية عملية النسخ الاحتياطي
        logger.info("بدء عملية إنشاء نسخة احتياطية")
        
        # التأكد من وجود ملف قاعدة البيانات
        if not os.path.exists(db_path):
            logger.error(f"ملف قاعدة البيانات غير موجود: {db_path}")
            return False, f"ملف قاعدة البيانات غير موجود: {db_path}", None
        
        # تحميل الإعدادات
        config = load_config()
        
        # تحديد مسار الحفظ
        backup_dir = custom_path if custom_path else config['backup_dir']
        
        # التأكد من وجود المجلد
        if not os.path.exists(backup_dir):
            logger.info(f"إنشاء مجلد النسخ الاحتياطية: {backup_dir}")
            os.makedirs(backup_dir)
        
        # إنشاء اسم الملف بناءً على التاريخ والوقت الحالي
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"carpet_cleaning_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # نسخ ملف قاعدة البيانات
        shutil.copy2(db_path, backup_path)
        
        # تحديث وقت آخر نسخة احتياطية
        update_config('last_backup', datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        logger.info(f"تم إنشاء نسخة احتياطية بنجاح: {backup_path}")
        return True, "تم إنشاء النسخة الاحتياطية بنجاح", backup_path
        
    except Exception as e:
        error_msg = f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
        logger.error(error_msg)
        return False, error_msg, None

def restore_backup(backup_path, db_path='carpet_cleaning.db'):
    """
    استعادة قاعدة البيانات من نسخة احتياطية
    
    Args:
        backup_path (str): مسار ملف النسخة الاحتياطية
        db_path (str): مسار ملف قاعدة البيانات الأصلي
    
    Returns:
        tuple: (نجاح العملية (bool), رسالة (str))
    """
    try:
        # التأكد من وجود ملف النسخة الاحتياطية
        if not os.path.exists(backup_path):
            return False, f"ملف النسخة الاحتياطية غير موجود: {backup_path}"
        
        # التحقق من صحة ملف قاعدة البيانات
        try:
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            conn.close()
            
            if result[0] != "ok":
                return False, "ملف النسخة الاحتياطية تالف"
        except Exception as e:
            return False, f"خطأ في التحقق من صحة النسخة الاحتياطية: {str(e)}"
        
        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup = f"carpet_cleaning_before_restore_{timestamp}.db"
        current_backup_path = os.path.join(os.path.dirname(db_path), current_backup)
        
        if os.path.exists(db_path):
            shutil.copy2(db_path, current_backup_path)
            logger.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة: {current_backup_path}")
        
        # استعادة النسخة الاحتياطية
        shutil.copy2(backup_path, db_path)
        
        logger.info(f"تم استعادة قاعدة البيانات بنجاح من: {backup_path}")
        return True, "تم استعادة قاعدة البيانات بنجاح"
        
    except Exception as e:
        error_msg = f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def check_daily_backup():
    """
    التحقق مما إذا كان يجب إنشاء نسخة احتياطية يومية
    
    Returns:
        bool: هل تم إنشاء نسخة احتياطية
    """
    try:
        config = load_config()
        
        # التحقق مما إذا كان النسخ الاحتياطي التلقائي مفعل
        if not config.get('auto_backup', True):
            logger.info("النسخ الاحتياطي التلقائي غير مفعل")
            return False
        
        # التحقق من تاريخ آخر نسخة احتياطية
        last_backup = config.get('last_backup')
        now = datetime.now()
        today = now.date()
        
        if last_backup:
            last_backup_time = datetime.strptime(last_backup, "%Y-%m-%d %H:%M:%S")
            last_backup_date = last_backup_time.date()
            
            # إذا تم إنشاء نسخة احتياطية اليوم، لا داعي لإنشاء نسخة جديدة
            if last_backup_date == today:
                logger.info("تم إنشاء نسخة احتياطية اليوم بالفعل")
                return False
        
        # التحقق من وقت النسخ الاحتياطي المحدد
        backup_time = config.get('backup_time', '00:00')
        hour, minute = map(int, backup_time.split(':'))
        scheduled_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # حساب الفرق بين الوقت الحالي والوقت المحدد بالدقائق
        time_diff_minutes = abs((now - scheduled_time).total_seconds() / 60)
        
        # إذا لم يكن الوقت الحالي قريبًا من الوقت المحدد (±5 دقائق)، لا تنشئ نسخة احتياطية
        if time_diff_minutes > 5:
            logger.info(f"الوقت الحالي ليس وقت النسخ الاحتياطي، الفرق {time_diff_minutes} دقيقة")
            return False
            
        # إنشاء نسخة احتياطية جديدة
        logger.info("جاري إنشاء النسخة الاحتياطية اليومية...")
        success, message, _ = create_backup()
        if success:
            logger.info("تم إنشاء النسخة الاحتياطية اليومية بنجاح")
            return True
        else:
            logger.error(f"فشل إنشاء النسخة الاحتياطية اليومية: {message}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في التحقق من النسخ الاحتياطي اليومي: {str(e)}")
        return False

def list_backups():
    """
    الحصول على قائمة النسخ الاحتياطية المتوفرة
    
    Returns:
        list: قائمة بمسارات ملفات النسخ الاحتياطية
    """
    try:
        config = load_config()
        backup_dir = config['backup_dir']
        
        if not os.path.exists(backup_dir):
            return []
        
        # البحث عن ملفات النسخ الاحتياطية
        backups = []
        for file in os.listdir(backup_dir):
            if file.startswith("carpet_cleaning_backup_") and file.endswith(".db"):
                backup_path = os.path.join(backup_dir, file)
                # الحصول على تاريخ إنشاء الملف
                creation_time = os.path.getctime(backup_path)
                backups.append({
                    'path': backup_path,
                    'filename': file,
                    'date': datetime.fromtimestamp(creation_time).strftime("%Y-%m-%d %H:%M:%S"),
                    'size': os.path.getsize(backup_path)
                })
        
        # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['date'], reverse=True)
        
        return backups
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {str(e)}")
        return []

def delete_old_backups(keep_days=30):
    """
    حذف النسخ الاحتياطية القديمة
    
    Args:
        keep_days (int): عدد الأيام للاحتفاظ بالنسخ الاحتياطية
    
    Returns:
        tuple: (عدد الملفات المحذوفة (int), رسالة (str))
    """
    try:
        config = load_config()
        backup_dir = config['backup_dir']
        
        if not os.path.exists(backup_dir):
            return 0, "مجلد النسخ الاحتياطية غير موجود"
        
        # الحصول على التاريخ الحالي
        now = time.time()
        deleted_count = 0
        
        for file in os.listdir(backup_dir):
            if file.startswith("carpet_cleaning_backup_") and file.endswith(".db"):
                backup_path = os.path.join(backup_dir, file)
                # الحصول على تاريخ إنشاء الملف
                creation_time = os.path.getctime(backup_path)
                # حساب عمر الملف بالأيام
                age_days = (now - creation_time) / (60 * 60 * 24)
                
                # حذف الملفات القديمة
                if age_days > keep_days:
                    os.remove(backup_path)
                    deleted_count += 1
                    logger.info(f"تم حذف نسخة احتياطية قديمة: {file}")
        
        return deleted_count, f"تم حذف {deleted_count} نسخة احتياطية قديمة"
        
    except Exception as e:
        error_msg = f"خطأ في حذف النسخ الاحتياطية القديمة: {str(e)}"
        logger.error(error_msg)
        return 0, error_msg

def setup_auto_backup(interval_minutes=5):
    """
    إعداد النسخ الاحتياطي التلقائي
    
    Args:
        interval_minutes (int): الفاصل الزمني بين عمليات فحص النسخ الاحتياطي بالدقائق
    """
    try:
        logger.info("جاري إعداد النسخ الاحتياطي التلقائي")
        
        # تحميل الإعدادات
        config = load_config()
        
        # التأكد من تفعيل النسخ الاحتياطي التلقائي
        if not config.get('auto_backup', True):
            logger.info("النسخ الاحتياطي التلقائي غير مفعل في الإعدادات")
            return
        
        # التحقق من النسخ الاحتياطي اليومي
        check_daily_backup()
        
        logger.info("تم إعداد النسخ الاحتياطي التلقائي بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في إعداد النسخ الاحتياطي التلقائي: {str(e)}")
