#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام النسخ الاحتياطي الاختياري - يعمل فقط عند التفعيل من الإعدادات
"""

import os
import shutil
import json
from datetime import datetime
from utils.config import load_config

def is_backup_enabled():
    """التحقق من تفعيل النسخ الاحتياطي في الإعدادات"""
    config = load_config()
    return config.get('backup_enabled', False)

def get_backup_directory():
    """الحصول على مجلد النسخ الاحتياطي من الإعدادات"""
    config = load_config()
    backup_dir = config.get('backup_dir', '')
    
    if not backup_dir:
        # إذا لم يتم تحديد مجلد، استخدم مجلد افتراضي
        backup_dir = os.path.join(os.getcwd(), 'backups')
    
    # إنشاء المجلد إذا لم يكن موجوداً
    if not os.path.exists(backup_dir):
        try:
            os.makedirs(backup_dir)
        except Exception as e:
            print(f"خطأ في إنشاء مجلد النسخ الاحتياطي: {str(e)}")
            return None
    
    return backup_dir

def create_backup():
    """إنشاء نسخة احتياطية - يعمل فقط إذا كان مفعلاً في الإعدادات"""
    try:
        # التحقق من تفعيل النسخ الاحتياطي
        if not is_backup_enabled():
            return False, "النسخ الاحتياطي غير مفعل في الإعدادات", None
        
        # الحصول على مجلد النسخ الاحتياطي
        backup_dir = get_backup_directory()
        if not backup_dir:
            return False, "لم يتم تحديد مجلد النسخ الاحتياطي", None
        
        # إنشاء اسم الملف مع التاريخ والوقت
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"carpet_cleaning_backup_{timestamp}.json"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # إنشاء النسخة الاحتياطية (هنا يمكن إضافة منطق النسخ الاحتياطي الفعلي)
        backup_data = {
            "timestamp": timestamp,
            "version": "1.0",
            "database_type": "mysql",
            "note": "نسخة احتياطية من إعدادات البرنامج"
        }
        
        # حفظ النسخة الاحتياطية
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        return True, f"تم إنشاء النسخة الاحتياطية بنجاح", backup_path
        
    except Exception as e:
        return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", None

def restore_backup(backup_path):
    """استعادة نسخة احتياطية"""
    try:
        if not os.path.exists(backup_path):
            return False, "ملف النسخة الاحتياطية غير موجود"
        
        # هنا يمكن إضافة منطق استعادة النسخة الاحتياطية
        return True, "تم استعادة النسخة الاحتياطية بنجاح"
        
    except Exception as e:
        return False, f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"

def list_backups():
    """قائمة النسخ الاحتياطية المتوفرة"""
    try:
        backup_dir = get_backup_directory()
        if not backup_dir or not os.path.exists(backup_dir):
            return []
        
        backups = []
        for filename in os.listdir(backup_dir):
            if filename.endswith('.json') and 'backup' in filename:
                file_path = os.path.join(backup_dir, filename)
                file_stat = os.stat(file_path)
                
                backups.append({
                    'filename': filename,
                    'path': file_path,
                    'size': file_stat.st_size,
                    'date': datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                })
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['date'], reverse=True)
        return backups
        
    except Exception as e:
        print(f"خطأ في قائمة النسخ الاحتياطية: {str(e)}")
        return []

def delete_old_backups(keep_count=10):
    """حذف النسخ الاحتياطية القديمة - الاحتفاظ بعدد محدد"""
    try:
        if not is_backup_enabled():
            return 0, "النسخ الاحتياطي غير مفعل"
        
        backups = list_backups()
        if len(backups) <= keep_count:
            return 0, f"عدد النسخ الاحتياطية ({len(backups)}) أقل من أو يساوي الحد المسموح ({keep_count})"
        
        # حذف النسخ الزائدة
        deleted_count = 0
        for backup in backups[keep_count:]:
            try:
                os.remove(backup['path'])
                deleted_count += 1
            except Exception as e:
                print(f"خطأ في حذف {backup['filename']}: {str(e)}")
        
        return deleted_count, f"تم حذف {deleted_count} نسخة احتياطية قديمة"
        
    except Exception as e:
        return 0, f"خطأ في حذف النسخ الاحتياطية القديمة: {str(e)}"

def setup_auto_backup():
    """إعداد النسخ الاحتياطي التلقائي - لا يعمل إلا إذا كان مفعلاً"""
    config = load_config()
    
    # التحقق من تفعيل النسخ الاحتياطي
    if not config.get('backup_enabled', False):
        print("النسخ الاحتياطي التلقائي غير مفعل في الإعدادات")
        return False
    
    # التحقق من تفعيل النسخ الاحتياطي التلقائي
    if not config.get('auto_backup', False):
        print("النسخ الاحتياطي التلقائي غير مفعل")
        return False
    
    print("تم تفعيل النسخ الاحتياطي التلقائي")
    return True

def create_exit_backup():
    """إنشاء نسخة احتياطية عند الخروج - إذا كان مفعلاً"""
    config = load_config()
    
    # التحقق من تفعيل النسخ الاحتياطي
    if not config.get('backup_enabled', False):
        return False, "النسخ الاحتياطي غير مفعل"
    
    # التحقق من تفعيل النسخ الاحتياطي عند الخروج
    if not config.get('exit_backup', False):
        return False, "النسخ الاحتياطي عند الخروج غير مفعل"
    
    return create_backup()
