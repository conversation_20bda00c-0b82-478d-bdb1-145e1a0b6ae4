#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لحذف جميع الطلبات من قاعدة البيانات (نسخة سطر الأوامر)
هذا السكريبت سيحذف جميع بيانات الطلبات بشكل نهائي
يرجى التأكد من أخذ نسخة احتياطية قبل تنفيذ هذا السكريبت
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from db.models import Database
    from utils.backup import create_backup
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {str(e)}")
    sys.exit(1)

def reset_orders():
    """حذف جميع الطلبات من قاعدة البيانات"""
    try:
        print("\n" + "=" * 50)
        print("تحذير هام".center(50))
        print("=" * 50)
        print("أنت على وشك حذف جميع الطلبات من قاعدة البيانات.")
        print("هذه العملية لا يمكن التراجع عنها وستؤدي إلى فقدان جميع بيانات الطلبات بشكل نهائي.")
        print("يرجى التأكد من أخذ نسخة احتياطية قبل المتابعة.")
        print("=" * 50 + "\n")
        
        # طلب تأكيد من المستخدم
        confirmation = input("هل أنت متأكد من حذف جميع الطلبات؟ (اكتب 'نعم' للتأكيد): ")
        if confirmation.strip() != "نعم":
            print("تم إلغاء العملية")
            return
        
        # سؤال المستخدم عن إنشاء نسخة احتياطية
        create_backup_confirmation = input("هل ترغب في إنشاء نسخة احتياطية قبل الحذف؟ (Y/n): ")
        if create_backup_confirmation.strip().lower() != "n":
            print("جاري إنشاء نسخة احتياطية...")
            success, message, backup_path = create_backup()
            if success:
                print(f"تم إنشاء نسخة احتياطية بنجاح: {backup_path}")
            else:
                print(f"فشل في إنشاء نسخة احتياطية: {message}")
                continue_confirmation = input("هل ترغب في المتابعة بدون نسخة احتياطية؟ (y/N): ")
                if continue_confirmation.strip().lower() != "y":
                    print("تم إلغاء العملية")
                    return
        
        # الاتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # حذف جميع السجلات من جدول أبعاد السجاد
        print("جاري حذف جميع السجلات من جدول أبعاد السجاد...")
        cursor.execute("DELETE FROM carpet_dimensions")
        carpet_dimensions_count = cursor.rowcount
        print(f"تم حذف {carpet_dimensions_count} سجل من جدول أبعاد السجاد")
        
        # حذف جميع السجلات من جدول طلبات المندوبين
        print("جاري حذف جميع السجلات من جدول طلبات المندوبين...")
        cursor.execute("DELETE FROM delegate_orders")
        delegate_orders_count = cursor.rowcount
        print(f"تم حذف {delegate_orders_count} سجل من جدول طلبات المندوبين")
        
        # حذف جميع السجلات من جدول الطلبات
        print("جاري حذف جميع السجلات من جدول الطلبات...")
        cursor.execute("DELETE FROM orders")
        orders_count = cursor.rowcount
        print(f"تم حذف {orders_count} سجل من جدول الطلبات")
        
        # إعادة تعيين قيمة المعرف التلقائي
        if hasattr(db, 'db_type') and db.db_type == 'mysql':
            print("جاري إعادة تعيين قيمة المعرف التلقائي (MySQL)...")
            cursor.execute("ALTER TABLE orders AUTO_INCREMENT = 1")
            cursor.execute("ALTER TABLE carpet_dimensions AUTO_INCREMENT = 1")
            cursor.execute("ALTER TABLE delegate_orders AUTO_INCREMENT = 1")
        else:
            print("جاري إعادة تعيين قيمة المعرف التلقائي (SQLite)...")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='orders'")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='carpet_dimensions'")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='delegate_orders'")
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        conn.close()
        
        print("\n" + "=" * 50)
        print("تم حذف جميع الطلبات من قاعدة البيانات بنجاح".center(50))
        print(f"تم حذف {orders_count} طلب و {carpet_dimensions_count} سجل أبعاد و {delegate_orders_count} سجل توزيع")
        print("=" * 50 + "\n")
        
    except Exception as e:
        print(f"خطأ في حذف الطلبات: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    reset_orders()
