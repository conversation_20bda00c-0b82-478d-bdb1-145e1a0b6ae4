#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نافذة تعديل بسيطة للطلبات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QSpinBox,
                            QDoubleSpinBox, QWidget, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from db.models import Database
import math
import traceback


class SimpleEditOrderDialog(QDialog):
    """نافذة تعديل بسيطة للطلبات"""
    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.db = Database()
        self.order_id = order_id
        self.is_loading = True  # معرّف للتحكم في تحميل البيانات

        self.setWindowTitle("تعديل الطلب")
        self.setModal(True)
        self.setMinimumWidth(450)  # زيادة العرض الأدنى للنافذة
        self.setFixedSize(450, 350)  # تثبيت حجم النافذة

        # تطبيق تصميم عصري للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border: 1px solid #dcdcdc;
                border-radius: 10px;
            }
        """)

        # تهيئة واجهة المستخدم
        self.setup_ui()

        # ربط حدث التغيير في حقل عدد السجاد مع نافذة الأبعاد
        self.carpet_count.valueChanged.connect(self.on_carpet_count_changed)
        self.carpet_count.editingFinished.connect(self.setup_carpet_dimensions_dialog)

        self.is_loading = False

        # تحميل بيانات الطلب إذا كان معرف الطلب متوفرًا
        if self.order_id:
            self.load_order_data()

            # وضع المؤشر في أول حقل عند فتح النافذة
            QTimer.singleShot(100, self.set_initial_focus)

    def set_initial_focus(self):
        """تعيين التركيز على حقل رقم الوصل وتحديد محتواه للتعديل السريع"""
        # مسح محتوى الحقول للتعديل الجديد إذا كان مطلوبًا
        # self.receipt_number.clear()
        self.receipt_number.setFocus()
        self.receipt_number.selectAll()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)  # هوامش أكبر للنافذة
        layout.setSpacing(15)  # زيادة المسافة بين العناصر

        # تطبيق نمط موحد للتسميات
        label_style = "QLabel { font-weight: bold; font-size: 12pt; color: #2c3e50; }"

        # رقم الوصل
        receipt_layout = QHBoxLayout()
        receipt_label = QLabel("رقم الوصل:")
        receipt_label.setStyleSheet(label_style)
        self.receipt_number = QLineEdit()
        receipt_layout.addWidget(receipt_label)
        receipt_layout.addWidget(self.receipt_number)
        layout.addLayout(receipt_layout)

        # عدد السجاد
        carpet_layout = QHBoxLayout()
        carpet_label = QLabel("عدد السجاد:")
        carpet_label.setStyleSheet(label_style)
        self.carpet_count = QSpinBox()
        # إزالة أسهم الزيادة والنقصان من حقل عدد السجاد
        self.carpet_count.setButtonSymbols(QSpinBox.NoButtons)
        self.carpet_count.setRange(0, 100)
        carpet_layout.addWidget(carpet_label)
        carpet_layout.addWidget(self.carpet_count)
        layout.addLayout(carpet_layout)

        # عدد البطانيات
        blanket_layout = QHBoxLayout()
        blanket_label = QLabel("عدد البطانيات:")
        blanket_label.setStyleSheet(label_style)
        self.blanket_count = QSpinBox()
        # إزالة أسهم الزيادة والنقصان من حقل عدد البطانيات
        self.blanket_count.setButtonSymbols(QSpinBox.NoButtons)
        self.blanket_count.setRange(0, 100)
        blanket_layout.addWidget(blanket_label)
        blanket_layout.addWidget(self.blanket_count)
        layout.addLayout(blanket_layout)

        # الملاحظات - تغييره من TextEdit إلى LineEdit للتوحيد مع باقي الحقول
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        notes_label.setStyleSheet(label_style)
        self.notes = QLineEdit()  # تغيير من QTextEdit إلى QLineEdit
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes)
        layout.addLayout(notes_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        layout.addLayout(buttons_layout)

        # ربط الأحداث
        self.save_button.clicked.connect(self.save_order)
        self.cancel_button.clicked.connect(self.reject)

        # تطبيق نمط موحد لجميع الحقول
        input_style = """
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            font-size: 12pt;
            min-height: 30px;
            background-color: #ffffff;
            color: #2c3e50;
        """

        focus_style = """
            border: 2px solid #3498db;
            background-color: #ecf0f1;
        """

        self.receipt_number.setStyleSheet(f"QLineEdit {{ {input_style} }} QLineEdit:focus {{ {focus_style} }}")

        # تنسيق حقل عدد السجاد
        self.carpet_count.setStyleSheet(f"""
            QSpinBox {{ {input_style} }}
            QSpinBox:focus {{ {focus_style} }}
            QSpinBox::up-button, QSpinBox::down-button {{
                width: 0px;
                height: 0px;
            }}
        """)

        # تنسيق حقل عدد البطانيات
        self.blanket_count.setStyleSheet(f"""
            QSpinBox {{ {input_style} }}
            QSpinBox:focus {{ {focus_style} }}
            QSpinBox::up-button, QSpinBox::down-button {{
                width: 0px;
                height: 0px;
            }}
        """)

        # تنسيق حقل الملاحظات
        self.notes.setStyleSheet(f"QLineEdit {{ {input_style} }} QLineEdit:focus {{ {focus_style} }}")

    def on_carpet_count_changed(self, value):
        """تنفيذ عند تغيير عدد السجاد"""
        if value > 0:
            # يمكنك إضافة أي منطق آخر هنا
            pass

    def setup_carpet_dimensions_dialog(self):
        """إعداد نافذة تسجيل أبعاد السجاد"""
        carpet_count = int(self.carpet_count.value())

        if carpet_count == 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال عدد السجاد أولاً")
            return

        # إنشاء نافذة فرعية بتصميم عصري
        dimensions_dialog = QDialog(self)
        dimensions_dialog.setWindowTitle("تسجيل أبعاد السجاد")
        dimensions_dialog.setMinimumWidth(550)  # زيادة العرض قليلاً
        dimensions_dialog.setMinimumHeight(550)  # زيادة الارتفاع قليلاً
        dimensions_dialog.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border: 1px solid #dcdcdc;
                border-radius: 10px;
            }
        """)

        # إعداد تخطيط النافذة الرئيسي كتخطيط عمودي مع شريط تمرير
        main_layout = QVBoxLayout(dimensions_dialog)
        main_layout.setContentsMargins(20, 20, 20, 20)  # هوامش أكبر للنافذة
        main_layout.setSpacing(15)  # زيادة المسافة بين العناصر

        # إنشاء منطقة تمرير بتصميم محسن
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #ffffff;
                border: none;
                border-radius: 8px;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #3498db;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # تخطيط للجزء الرئيسي (حقول الإدخال)
        input_container = QWidget()
        input_container.setStyleSheet("background-color: #ffffff;")
        layout = QVBoxLayout(input_container)
        layout.setContentsMargins(15, 15, 15, 15)  # هوامش داخلية
        layout.setSpacing(12)  # مسافة بين العناصر

        # إضافة عنوان بتصميم جذاب
        title_label = QLabel("الرجاء إدخال أبعاد السجاد (بالمتر)")
        title_label.setStyleSheet("""
            font-size: 16pt;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px;
            padding: 10px;
            border-bottom: 2px solid #3498db;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # تخزين حقول الإدخال للرجوع إليها لاحقاً
        self.carpet_dimensions = []

        # تحميل أبعاد السجاد الحالية من قاعدة البيانات إذا كانت موجودة
        existing_dimensions = []
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT length, width FROM carpet_dimensions
                    WHERE order_id = %s ORDER BY id
                """, (self.order_id,))
            else:
                cursor.execute("""
                    SELECT length, width FROM carpet_dimensions
                    WHERE order_id = ? ORDER BY id
                """, (self.order_id,))
            existing_dimensions = cursor.fetchall()
            conn.close()
            print(f"تم تحميل {len(existing_dimensions)} من أبعاد السجاد الحالية")
        except Exception as e:
            print(f"خطأ في تحميل أبعاد السجاد: {str(e)}")

        # إنشاء حقول إدخال لكل سجادة
        for i in range(carpet_count):
            # حاوية لكل سجادة
            carpet_container = QWidget()
            carpet_container.setStyleSheet("""
                QWidget {
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    margin: 5px 0px;
                }
            """)
            carpet_layout = QHBoxLayout(carpet_container)
            carpet_layout.setContentsMargins(10, 10, 10, 10)
            carpet_layout.setSpacing(10)

            # عنوان السجادة
            label = QLabel(f"السجادة {i+1}:")
            label.setMinimumWidth(80)
            label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    color: #2c3e50;
                    font-size: 12pt;
                }
            """)
            carpet_layout.addWidget(label)

            # حقل إدخال الطول
            length_label = QLabel("الطول:")
            length_label.setStyleSheet("color: #34495e; font-size: 11pt;")
            length_input = QDoubleSpinBox()
            # إزالة أسهم الزيادة والنقصان
            length_input.setButtonSymbols(QDoubleSpinBox.NoButtons)
            length_input.setRange(0.1, 10.0)
            length_input.setSingleStep(0.1)
            length_input.setValue(0.0)

            # حقل إدخال العرض
            width_label = QLabel("العرض:")
            width_label.setStyleSheet("color: #34495e; font-size: 11pt;")
            width_input = QDoubleSpinBox()
            # إزالة أسهم الزيادة والنقصان
            width_input.setButtonSymbols(QDoubleSpinBox.NoButtons)
            width_input.setRange(0.1, 10.0)
            width_input.setSingleStep(0.1)
            width_input.setValue(0.0)

            # تطبيق نمط CSS موحد لحقول الإدخال
            input_style = """
                QDoubleSpinBox {
                    padding: 8px;
                    border: 1px solid #bdc3c7;
                    border-radius: 6px;
                    font-size: 12pt;
                    background-color: #ffffff;
                    color: #2c3e50;
                }
                QDoubleSpinBox:focus {
                    border: 2px solid #3498db;
                    background-color: #ecf0f1;
                }
                QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                    width: 0px;
                    height: 0px;
                }
            """
            length_input.setStyleSheet(input_style)
            width_input.setStyleSheet(input_style)

            # إذا كانت هناك أبعاد موجودة، نستخدمها للسجادة الحالية
            if i < len(existing_dimensions):
                length_input.setValue(existing_dimensions[i][0])
                width_input.setValue(existing_dimensions[i][1])
                print(f"تم تعيين أبعاد السجادة {i+1}: الطول={existing_dimensions[i][0]}, العرض={existing_dimensions[i][1]}")
            else:
                # لا نضع قيم افتراضية للسجادات الجديدة
                length_input.clear()
                width_input.clear()

            # تخزين بيانات السجادة
            self.carpet_dimensions.append({
                'length_input': length_input,
                'width_input': width_input,
                'length': length_input.value(),
                'width': width_input.value()
            })

            # إضافة الحقول
            carpet_layout.addWidget(length_label)
            carpet_layout.addWidget(length_input)
            carpet_layout.addWidget(width_label)
            carpet_layout.addWidget(width_input)

            layout.addWidget(carpet_container)

        # وضع التركيز على أول حقل طول في أول سجادة
        if self.carpet_dimensions and len(self.carpet_dimensions) > 0:
            QTimer.singleShot(100, lambda: self.carpet_dimensions[0]['length_input'].setFocus())

        # أزرار الحفظ والإلغاء بتصميم عصري
        button_container = QWidget()
        button_container.setStyleSheet("background-color: #f0f0f0; border-top: 1px solid #e0e0e0; padding: 5px;")
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(15, 15, 15, 15)
        button_layout.setSpacing(15)

        save_btn = QPushButton("حفظ الأبعاد")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13pt;
                font-weight: bold;
                min-width: 120px;
                box-shadow: 0 3px 5px rgba(46, 204, 113, 0.3);
            }
            QPushButton:hover {
                background-color: #27ae60;
                box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
            }
            QPushButton:pressed {
                background-color: #27ae60;
                box-shadow: 0 2px 3px rgba(46, 204, 113, 0.3);
            }
        """)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13pt;
                font-weight: bold;
                min-width: 120px;
                box-shadow: 0 3px 5px rgba(231, 76, 60, 0.3);
            }
            QPushButton:hover {
                background-color: #c0392b;
                box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
            }
            QPushButton:pressed {
                background-color: #c0392b;
                box-shadow: 0 2px 3px rgba(231, 76, 60, 0.3);
            }
        """)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)

        layout.addWidget(button_container)

        # ربط الأحداث
        save_btn.clicked.connect(lambda: self.save_carpet_dimensions(dimensions_dialog))
        cancel_btn.clicked.connect(dimensions_dialog.reject)

        # إضافة حاوية الإدخال إلى منطقة التمرير
        scroll_area.setWidget(input_container)

        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        main_layout.addWidget(scroll_area)

        # عرض النافذة
        dimensions_dialog.exec_()

    def save_carpet_dimensions(self, dialog):
        """حفظ أبعاد السجاد"""
        try:
            # تحديث بيانات الأبعاد
            for i, dim in enumerate(self.carpet_dimensions):
                dim['length'] = dim['length_input'].value()
                dim['width'] = dim['width_input'].value()

            dialog.accept()
        except Exception as e:
            print(f"خطأ في حفظ أبعاد السجاد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ أبعاد السجاد: {str(e)}")

    def load_order_data(self):
        """تحميل بيانات الطلب"""
        try:
            conn = self.db.get_connection()
            if not conn:
                QMessageBox.critical(self, "خطأ", "فشل الاتصال بقاعدة البيانات.")
                return
            cursor = conn.cursor()

            if self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT receipt_number, notes, carpet_count, blanket_count
                    FROM orders WHERE id = %s
                """, (self.order_id,))
            else:
                cursor.execute("""
                    SELECT receipt_number, notes, carpet_count, blanket_count
                    FROM orders WHERE id = ?
                """, (self.order_id,))

            row = cursor.fetchone()
            if row:
                self.receipt_number.setText(row[0] or "")
                self.notes.setText(row[1] or "")  # تعديل لاستخدام setText بدلاً من setPlainText
                self.carpet_count.setValue(row[2] or 0)
                self.blanket_count.setValue(row[3] or 0)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الطلب: {str(e)}")

    def save_order(self):
        """حفظ التعديلات على الطلب"""
        try:
            print("بدء حفظ الطلب...")
            # التحقق من البيانات المدخلة
            receipt_number = self.receipt_number.text().strip()
            print(f"receipt_number: {receipt_number}")
            carpet_count = int(self.carpet_count.value())
            print(f"carpet_count: {carpet_count}")
            blanket_count = int(self.blanket_count.value())
            print(f"blanket_count: {blanket_count}")
            notes = self.notes.text().strip()  # تغيير من toPlainText إلى text
            print(f"notes: {notes}")

            # تحضير البيانات للتحديث
            order_data = {
                'id': self.order_id,
                'receipt_number': receipt_number,
                'carpet_count': carpet_count,
                'blanket_count': blanket_count,
                'notes': notes
            }
            print(f"order_data: {order_data}")

            # تحديث بيانات الطلب
            print("تحديث بيانات الطلب...")
            self.update_order_simple(order_data)
            print("تم تحديث بيانات الطلب بنجاح.")

            # تحديث أبعاد السجاد
            if hasattr(self, 'carpet_dimensions') and self.carpet_dimensions:
                print("تحديث أبعاد السجاد...")
                self.db.delete_carpet_dimensions(self.order_id)
                for dim in self.carpet_dimensions:
                    if dim['length'] > 0 and dim['width'] > 0:
                        self.db.add_carpet_dimension(
                            order_id=self.order_id,
                            length=dim['length'],
                            width=dim['width']
                        )
                print("تم تحديث أبعاد السجاد بنجاح.")

            print(f"تم حفظ الطلب {self.order_id} بنجاح")
            QMessageBox.information(self, "نجاح", "تم تحديث بيانات الطلب بنجاح وتم نقله إلى المخزن.")
            self.accept()  # إغلاق النافذة

        except Exception as e:
            print(f"خطأ في حفظ الطلب: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الطلب: {str(e)}")
        finally:
            # إصلاح الخطأ: التحقق من وجود الاتصال بطريقة آمنة
            try:
                if hasattr(self, 'db'):
                    self.db.close_connection()
                    print("تم إغلاق الاتصال بقاعدة البيانات.")
            except Exception:
                pass

    def update_order_simple(self, order_data):
        """تحديث معلومات الطلب الأساسية في قاعدة البيانات ونقله إلى المخزن"""
        conn = None
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # طباعة بيانات الطلب للتشخيص
            print(f"\nبدء تحديث الطلب رقم {order_data['id']}...")
            print(f"بيانات التحديث: {order_data}")

            # تحديث جدول orders وتغيير حالة الطلب إلى 'warehouse' وإزالة ارتباطه بالمندوب
            if self.db.db_type == 'mysql':
                orders_query = """
                    UPDATE orders SET
                        receipt_number = %s,
                        carpet_count = %s,
                        blanket_count = %s,
                        notes = %s,
                        status = 'warehouse',
                        delegate_name = NULL,
                        delegate_id = NULL
                    WHERE id = %s
                """
            else:
                orders_query = """
                    UPDATE orders SET
                        receipt_number = ?,
                        carpet_count = ?,
                        blanket_count = ?,
                        notes = ?,
                        status = 'warehouse',
                        delegate_name = NULL,
                        delegate_id = NULL
                    WHERE id = ?
                """
            cursor.execute(orders_query, (
                order_data['receipt_number'],
                order_data['carpet_count'],
                order_data['blanket_count'],
                order_data['notes'],
                order_data['id']
            ))

            # التأكد من تحديث حالة الطلب إلى 'warehouse'
            print(f"تم تحديث حالة الطلب إلى 'warehouse' وإزالة ارتباطه بالمندوب")

            # البحث عن سجل delegate_orders المرتبط
            if self.db.db_type == 'mysql':
                cursor.execute("SELECT id FROM delegate_orders WHERE order_id = %s", (order_data['id'],))
            else:
                cursor.execute("SELECT id FROM delegate_orders WHERE order_id = ?", (order_data['id'],))
            delegate_order = cursor.fetchone()
            delegate_order_id = delegate_order[0] if delegate_order else None

            # حذف الطلب من جدول delegate_orders إذا وجد
            if delegate_order_id:
                print(f"حذف الطلب من جدول delegate_orders بمعرف {delegate_order_id}")
                if self.db.db_type == 'mysql':
                    cursor.execute("DELETE FROM delegate_orders WHERE id = %s", (delegate_order_id,))
                else:
                    cursor.execute("DELETE FROM delegate_orders WHERE id = ?", (delegate_order_id,))
                print("تم حذف الطلب من جدول delegate_orders بنجاح")

            # حساب المساحة الإجمالية من أبعاد السجاد
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT COALESCE(SUM(total_area), 0) FROM carpet_dimensions WHERE order_id = %s
                """, (order_data['id'],))
            else:
                cursor.execute("""
                    SELECT COALESCE(SUM(total_area), 0) FROM carpet_dimensions WHERE order_id = ?
                """, (order_data['id'],))

            total_area_result = cursor.fetchone()
            total_area = total_area_result[0] if total_area_result and total_area_result[0] is not None else 0

            # الحصول على الإعدادات (سعر المتر المربع وسعر البطانية وسعر التوصيل)
            if self.db.db_type == 'mysql':
                cursor.execute("SELECT price_per_meter, blanket_price, delivery_price FROM settings ORDER BY id DESC LIMIT 1")
            else:
                cursor.execute("SELECT price_per_meter, blanket_price, delivery_price FROM settings ORDER BY id DESC LIMIT 1")
            settings = cursor.fetchone()

            if not settings:
                raise Exception("لم يتم العثور على إعدادات الأسعار")

            price_per_meter = settings[0]
            blanket_price = settings[1]
            delivery_price = settings[2]

            # تحويل جميع القيم إلى float للتأكد من توافق الأنواع
            total_area_float = float(total_area)
            price_per_meter_float = float(price_per_meter)
            blanket_price_float = float(blanket_price)
            delivery_price_float = float(delivery_price) if delivery_price is not None else 0.0

            # حساب السعر الإجمالي للسجاد
            carpet_price = total_area_float * price_per_meter_float
            # حساب سعر البطانيات
            blankets_price = int(order_data['blanket_count']) * blanket_price_float

            # إضافة أجور التوصيل إذا كانت المساحة 12 متر مربع أو أقل
            delivery_fee = 0.0
            if total_area_float <= 12:
                delivery_fee = delivery_price_float
                print(f"تم إضافة أجور التوصيل ({delivery_fee:,.0f} دينار) لأن المساحة ({total_area_float} متر مربع) أقل من أو تساوي 12 متر مربع")

            total_price = carpet_price + blankets_price + delivery_fee

            print(f"المساحة الإجمالية: {total_area}، سعر المتر: {price_per_meter}، سعر البطانية: {blanket_price}، أجور التوصيل: {delivery_fee}")
            print(f"سعر السجاد: {carpet_price}، سعر البطانيات: {blankets_price}، السعر الإجمالي: {total_price}")

            # تحديث السعر والمساحة الإجمالية في جدول orders
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    UPDATE orders SET
                        total_price = %s,
                        total_area = %s,
                        has_delivery_fee = %s,
                        delivery_fee = %s,
                        transport_fees = %s
                    WHERE id = %s
                """, (total_price, total_area, 1 if total_area <= 12 else 0, delivery_fee, delivery_fee, order_data['id']))
            else:
                cursor.execute("""
                    UPDATE orders SET
                        total_price = ?,
                        total_area = ?,
                        has_delivery_fee = ?,
                        transport_fees = ?
                    WHERE id = ?
                """, (total_price, total_area, 1 if total_area <= 12 else 0, delivery_fee, order_data['id']))

            print(f"تم تحديث السعر والمساحة في جدول orders")

            # تحديث السعر الإجمالي في جدول delegate_orders
            if delegate_order_id:
                if self.db.db_type == 'mysql':
                    cursor.execute("""
                        UPDATE delegate_orders SET
                            total_price = %s,
                            has_delivery_fee = %s
                        WHERE id = %s
                    """, (total_price, 1 if total_area <= 12 else 0, delegate_order_id))
                else:
                    cursor.execute("""
                        UPDATE delegate_orders SET
                            total_price = ?,
                            has_delivery_fee = ?
                        WHERE id = ?
                    """, (total_price, 1 if total_area <= 12 else 0, delegate_order_id))

                print(f"تم تحديث السعر الإجمالي في جدول delegate_orders")

            # التحقق من نجاح التحديث
            if self.db.db_type == 'mysql':
                cursor.execute("SELECT id, carpet_count, receipt_number, total_price FROM orders WHERE id = %s", (order_data['id'],))
            else:
                cursor.execute("SELECT id, carpet_count, receipt_number, total_price FROM orders WHERE id = ?", (order_data['id'],))
            order_check = cursor.fetchone()
            print(f"التحقق من التحديث - السجل في orders: {order_check}")

            if delegate_order_id:
                if self.db.db_type == 'mysql':
                    cursor.execute("SELECT id, carpet_count, receipt_number, total_price FROM delegate_orders WHERE id = %s", (delegate_order_id,))
                else:
                    cursor.execute("SELECT id, carpet_count, receipt_number, total_price FROM delegate_orders WHERE id = ?", (delegate_order_id,))
                delegate_check = cursor.fetchone()
                print(f"التحقق من التحديث - السجل في delegate_orders: {delegate_check}")

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في تحديث الطلب: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
                print("تم إغلاق الاتصال بقاعدة البيانات.")

    def update_area_and_price(self, total_area):
        """تحديث المساحة والسعر في قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()

            # تقريب المساحة حسب القاعدة المطلوبة
            decimal_part = total_area - int(total_area)
            if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
                total_area = int(total_area) + 1
            else:  # إذا كان الكسر أقل من 0.5
                total_area = int(total_area)

            # الحصول على الإعدادات
            if self.db.db_type == 'mysql':
                cursor.execute("SELECT price_per_meter, blanket_price FROM settings LIMIT 1")
            else:
                cursor.execute("SELECT price_per_meter, blanket_price FROM settings LIMIT 1")
            settings = cursor.fetchone()
            if not settings:
                raise Exception("لم يتم العثور على إعدادات الأسعار")

            price_per_meter = settings[0]
            blanket_price = settings[1]

            # تحويل جميع القيم إلى float للتأكد من توافق الأنواع
            total_area_float = float(total_area)
            price_per_meter_float = float(price_per_meter)
            blanket_price_float = float(blanket_price)
            blanket_count_float = float(self.blanket_count)

            # حساب السعر الإجمالي
            carpet_price = total_area_float * price_per_meter_float
            blanket_total = blanket_count_float * blanket_price_float
            total_price = carpet_price + blanket_total

            # تحديث المساحة والسعر في الطلب
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    UPDATE orders
                    SET total_area = %s, total_price = %s
                    WHERE id = %s
                """, (total_area, total_price, self.order_id))
            else:
                cursor.execute("""
                    UPDATE orders
                    SET total_area = ?, total_price = ?
                    WHERE id = ?
                """, (total_area, total_price, self.order_id))

            self.conn.commit()
            return True, ""

        except Exception as e:
            return False, str(e)

def edit_order(order_id):
    """فتح نافذة تعديل الطلب"""
    try:
        dialog = SimpleEditOrderDialog(order_id=order_id)
        dialog.exec_()
    except Exception as e:
        print(f"خطأ في فتح نافذة تعديل الطلب: {str(e)}")
        print(traceback.format_exc())