-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS carpet_cleaning_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE carpet_cleaning_service;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المندوبين (delegates)
CREATE TABLE IF NOT EXISTS delegates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- إن<PERSON><PERSON><PERSON> جدول الطلبات (orders)
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50),
    phone VARCHAR(50) NOT NULL,
    phone2 VARCHAR(50),
    address VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_price DECIMAL(10, 2) NOT NULL,
    total_area DECIMAL(10, 2) DEFAULT 0,
    notes TEXT,
    delegate_id INT DEFAULT NULL,
    delegate_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'جديد',
    blanket_count INT DEFAULT 0,
    completion_date TIMESTAMP NULL,
    FOREIGN KEY (delegate_id) REFERENCES delegates(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- إنشاء جدول أبعاد السجاد (carpet_dimensions)
CREATE TABLE IF NOT EXISTS carpet_dimensions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    length DECIMAL(10, 2) NOT NULL,
    width DECIMAL(10, 2) NOT NULL,
    total_area DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- إنشاء جدول الإعدادات (settings)
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    price_per_meter DECIMAL(10, 2) DEFAULT 1000.0,
    blanket_price DECIMAL(10, 2) DEFAULT 5000.0,
    delivery_price DECIMAL(10, 2) DEFAULT 2000.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- إنشاء جدول الموظفين (employees)
CREATE TABLE IF NOT EXISTS employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    salary DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- إنشاء جدول المصروفات (expenses)
CREATE TABLE IF NOT EXISTS expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    item VARCHAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- إنشاء جدول طلبات المندوبين (delegate_orders)
CREATE TABLE IF NOT EXISTS delegate_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50),
    phone_number VARCHAR(50) NOT NULL,
    area VARCHAR(255),
    carpet_count INT NOT NULL DEFAULT 0,
    blanket_count INT DEFAULT 0,
    total_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    order_id INT,
    delegate_id INT,
    representative_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    FOREIGN KEY (delegate_id) REFERENCES delegates(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- إنشاء جدول المستخدمين (users)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) UNIQUE,
    password VARCHAR(255),
    user_type VARCHAR(50)
) ENGINE=InnoDB;

-- إضافة مستخدم مدير افتراضي
INSERT INTO users (username, password, user_type) 
VALUES ('admin', 'admin123', 'admin')
ON DUPLICATE KEY UPDATE username = 'admin';

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_orders_phone ON orders(phone);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_delegate_id ON orders(delegate_id);
CREATE INDEX idx_carpet_dimensions_order_id ON carpet_dimensions(order_id);
CREATE INDEX idx_delegate_orders_order_id ON delegate_orders(order_id);
CREATE INDEX idx_delegate_orders_delegate_id ON delegate_orders(delegate_id);
CREATE INDEX idx_delegate_orders_representative_name ON delegate_orders(representative_name);

-- إدخال قيم افتراضية في جدول الإعدادات إذا كان فارغاً
INSERT INTO settings (price_per_meter, blanket_price, delivery_price)
SELECT 1000.0, 5000.0, 2000.0
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM settings LIMIT 1);
