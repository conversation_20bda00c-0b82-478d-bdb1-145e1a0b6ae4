#!/usr/bin/env python
import sqlite3
import os
import sys

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from db.models import Database

def add_blanket_count_column():
    """Add blanket_count column to delegate_orders table if it doesn't exist"""
    try:
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()

        # Check if column exists
        cursor.execute("PRAGMA table_info(delegate_orders)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'blanket_count' not in columns:
            print("Adding blanket_count column...")
            cursor.execute("""
                ALTER TABLE delegate_orders 
                ADD COLUMN blanket_count INTEGER DEFAULT 0
            """)
            print("Column added successfully")
        else:
            print("blanket_count column already exists")
        
        conn.commit()
        print("Schema update completed successfully")
        
    except Exception as e:
        print(f"Error updating schema: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_blanket_count_column()