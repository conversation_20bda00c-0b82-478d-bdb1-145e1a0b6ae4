#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريپت لاختبار تسجيل الدخول
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_login():
    """اختبار تسجيل الدخول"""
    try:
        print("\n" + "=" * 50)
        print("اختبار تسجيل الدخول".center(50))
        print("=" * 50)
        
        # استيراد وحدة قاعدة البيانات
        from db.models import Database
        
        # إنشاء اتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        print(f"✓ تم الاتصال بقاعدة البيانات بنجاح")
        print(f"نوع قاعدة البيانات: {db.db_type}")
        
        # التحقق من جدول المستخدمين
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()
        
        print(f"\nعدد المستخدمين في قاعدة البيانات: {len(users)}")
        
        if users:
            print("\nقائمة المستخدمين:")
            for user in users:
                print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}, كلمة المرور: {user[2]}")
                if len(user) > 3:
                    print(f"    الدور: {user[3]}")
        
        # اختبار تسجيل الدخول مع المستخدم admin
        print(f"\n" + "-" * 50)
        print("اختبار تسجيل الدخول مع المستخدم admin")
        print("-" * 50)
        
        username = "admin"
        password = "admin123"
        
        cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s", (username, password))
        user = cursor.fetchone()
        
        if user:
            print(f"✓ تم العثور على المستخدم: {user[1]}")
            print(f"✓ كلمة المرور صحيحة")
            if len(user) > 3:
                print(f"✓ الدور: {user[3]}")
            print("✓ تسجيل الدخول نجح!")
        else:
            print("✗ فشل تسجيل الدخول")
            print("التحقق من جميع المستخدمين...")
            
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            user_check = cursor.fetchone()
            
            if user_check:
                print(f"✓ تم العثور على المستخدم: {username}")
                print(f"✗ كلمة المرور غير صحيحة")
                print(f"كلمة المرور المحفوظة: {user_check[2]}")
                print(f"كلمة المرور المدخلة: {password}")
            else:
                print(f"✗ لم يتم العثور على المستخدم: {username}")
        
        # اختبار المستخدمين الآخرين
        print(f"\n" + "-" * 50)
        print("اختبار المستخدمين الآخرين")
        print("-" * 50)
        
        other_users = [
            ("زياد", "9999"),
            ("احمد", "2222")
        ]
        
        for test_username, test_password in other_users:
            cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s", (test_username, test_password))
            user = cursor.fetchone()
            
            if user:
                print(f"✓ المستخدم {test_username}: تسجيل الدخول نجح")
            else:
                print(f"✗ المستخدم {test_username}: فشل تسجيل الدخول")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("اكتمل اختبار تسجيل الدخول".center(50))
        print("=" * 50)
        
    except Exception as e:
        print(f"خطأ في اختبار تسجيل الدخول: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_login()
