from PyQt5.QtWidgets import (Q<PERSON><PERSON><PERSON><PERSON>ow, QWidget, Q<PERSON>oxLayout, QHB<PERSON>Layout,
                               QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
                               QHeaderView, QLabel, QMessageBox, QDateEdit, QDialog,
                               QApplication, QTabWidget, QGroupBox, QComboBox)
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
from db.models import Database

class AccountsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("قائمة الحسابات")
        self.setGeometry(100, 100, 1200, 600)

        # توسيط النافذة في وسط الشاشة
        self.center_window()

        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
            QTableWidget {
                background-color: white;
                gridline-color: #dcdcdc;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #e8f0fe;
                color: black;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-size: 12px;
                font-weight: bold;
            }
            QDateEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                background-color: #f5f6fa;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        # إنشاء القاعدة إذا لم تكن موجودة
        self.create_database()

        # تهيئة واجهة المستخدم
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        central_widget.setLayout(main_layout)

        # عنوان الصفحة
        title_label = QLabel("إدارة الحسابات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        main_layout.addWidget(title_label)

        # إنشاء مدير التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setContentsMargins(0, 10, 0, 0)

        # إضافة التبويبات
        expenses_tab = self.create_expenses_tab()

        self.tab_widget.addTab(expenses_tab, "المصروفات")

        # إضافة مدير التبويبات للتخطيط الرئيسي
        main_layout.addWidget(self.tab_widget)

    def create_expenses_tab(self):
        """إنشاء تبويب المصروفات"""
        expenses_tab = QWidget()
        expenses_layout = QVBoxLayout()
        expenses_layout.setSpacing(15)
        expenses_layout.setContentsMargins(15, 15, 15, 15)
        expenses_tab.setLayout(expenses_layout)

        # مجموعة أدوات التحكم بالمصروفات
        controls_group = QGroupBox("أدوات التحكم")
        controls_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        controls_layout = QVBoxLayout()

        # أزرار التحكم بالمصروفات
        expenses_controls = QHBoxLayout()
        expenses_controls.setSpacing(10)

        # زر إضافة مصروف
        self.add_expense_btn = QPushButton("إضافة مصروف")
        self.add_expense_btn.clicked.connect(self.add_expense)
        self.add_expense_btn.setFixedHeight(35)
        expenses_controls.addWidget(self.add_expense_btn)

        # زر حذف المصروفات
        self.delete_expense_btn = QPushButton("حذف مصروف")
        self.delete_expense_btn.clicked.connect(self.delete_expense)
        self.delete_expense_btn.setFixedHeight(35)
        self.delete_expense_btn.setStyleSheet("background-color: #e74c3c;")
        expenses_controls.addWidget(self.delete_expense_btn)

        controls_layout.addLayout(expenses_controls)

        # حقول البحث
        search_layout = QHBoxLayout()
        search_layout.setSpacing(10)

        self.item_search = QLineEdit()
        self.item_search.setPlaceholderText("بحث حسب البضاعة")
        self.item_search.textChanged.connect(self.filter_expenses)

        self.name_search = QLineEdit()
        self.name_search.setPlaceholderText("بحث حسب الاسم")
        self.name_search.textChanged.connect(self.filter_expenses)

        self.amount_search = QLineEdit()
        self.amount_search.setPlaceholderText("بحث حسب المبلغ")
        self.amount_search.textChanged.connect(self.filter_expenses)

        for search_field in [self.item_search, self.name_search, self.amount_search]:
            search_field.setFixedHeight(35)
            search_layout.addWidget(search_field)

        controls_layout.addLayout(search_layout)

        # إضافة قائمة منسدلة للتصفية حسب الفترة
        period_layout = QHBoxLayout()
        period_layout.setSpacing(10)

        period_label = QLabel("تصفية حسب الفترة:")
        period_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        period_layout.addWidget(period_label)

        self.period_combo = QComboBox()
        self.period_combo.addItems(["الكل", "اليوم", "الشهر", "السنة"])
        self.period_combo.setFixedHeight(35)
        self.period_combo.setFixedWidth(150)
        self.period_combo.currentIndexChanged.connect(self.filter_expenses_by_period)
        period_layout.addWidget(self.period_combo)

        # إضافة حقل المبلغ الإجمالي
        total_label = QLabel("المبلغ الإجمالي:")
        total_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        period_layout.addWidget(total_label)

        self.total_amount_label = QLabel("0")
        self.total_amount_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #27ae60;
            background-color: #f9f9f9;
            padding: 5px 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        """)
        self.total_amount_label.setAlignment(Qt.AlignCenter)
        self.total_amount_label.setFixedWidth(200)
        period_layout.addWidget(self.total_amount_label)

        period_layout.addStretch(1)
        controls_layout.addLayout(period_layout)

        controls_group.setLayout(controls_layout)
        expenses_layout.addWidget(controls_group)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(5)
        self.expenses_table.setHorizontalHeaderLabels(["تحديد", "الاسم", "المبلغ", "البضاعة", "تاريخ الإضافة"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.verticalHeader().setVisible(False)
        self.expenses_table.setAlternatingRowColors(True)
        expenses_layout.addWidget(self.expenses_table)

        # تحميل المصروفات
        self.load_expenses()

        return expenses_tab

    def create_database(self):
        # استخدام وحدة قاعدة البيانات
        self.db = Database()

        try:
            conn = self.db.get_connection()
            c = conn.cursor()

            # إنشاء جدول المستخدمين
            if self.db.db_type == 'mysql':
                c.execute('''CREATE TABLE IF NOT EXISTS users
                            (id INT AUTO_INCREMENT PRIMARY KEY,
                             username VARCHAR(255) UNIQUE,
                             password VARCHAR(255),
                             user_type VARCHAR(50))''')

                # إنشاء جدول المصروفات
                c.execute('''CREATE TABLE IF NOT EXISTS expenses
                            (id INT AUTO_INCREMENT PRIMARY KEY,
                             name VARCHAR(255),
                             item VARCHAR(255),
                             amount DECIMAL(10,2),
                             date VARCHAR(50))''')

                # إضافة مستخدم مدير افتراضي إذا لم يكن موجوداً
                c.execute("SELECT * FROM users WHERE username=%s", ('admin',))
                if not c.fetchone():
                    c.execute("INSERT INTO users (username, password, user_type) VALUES (%s, %s, %s)",
                             ('admin', 'admin123', 'admin'))
            else:
                # استخدام SQLite كاحتياطي
                c.execute('''CREATE TABLE IF NOT EXISTS users
                            (id INTEGER PRIMARY KEY,
                             username TEXT UNIQUE,
                             password TEXT,
                             user_type TEXT)''')

                # إنشاء جدول المصروفات
                c.execute('''CREATE TABLE IF NOT EXISTS expenses
                            (id INTEGER PRIMARY KEY,
                             name TEXT,
                             item TEXT,
                             amount REAL,
                             date TEXT)''')

                # إضافة مستخدم مدير افتراضي إذا لم يكن موجوداً
                c.execute("SELECT * FROM users WHERE username=?", ('admin',))
                if not c.fetchone():
                    c.execute("INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)",
                             ('admin', 'admin123', 'admin'))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء قاعدة البيانات: {str(e)}")

    def delete_expense(self):
        selected_rows = []
        for row in range(self.expenses_table.rowCount()):
            checkbox_item = self.expenses_table.item(row, 0)
            if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                selected_rows.append(row)

        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد مصروف واحد على الأقل للحذف")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف المصروفات المحددة؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                conn = self.db.get_connection()
                c = conn.cursor()

                for row in selected_rows:
                    name = self.expenses_table.item(row, 1).text()
                    amount_text = self.expenses_table.item(row, 2).text()
                    item = self.expenses_table.item(row, 3).text()
                    date_text = self.expenses_table.item(row, 4).text()

                    # إزالة التنسيق من المبلغ (إزالة الفواصل)
                    amount_text = amount_text.replace(',', '')

                    # محاولة العثور على السجل باستخدام الاسم والبند والتاريخ
                    if self.db.db_type == 'mysql':
                        c.execute("SELECT id FROM expenses WHERE name=%s AND item=%s AND date LIKE %s",
                                (name, item, f"%{date_text}%"))
                    else:
                        c.execute("SELECT id FROM expenses WHERE name=? AND item=? AND date LIKE ?",
                                (name, item, f"%{date_text}%"))
                    expense_id = c.fetchone()

                    if expense_id:
                        # حذف باستخدام المعرف
                        if self.db.db_type == 'mysql':
                            c.execute("DELETE FROM expenses WHERE id=%s", (expense_id[0],))
                        else:
                            c.execute("DELETE FROM expenses WHERE id=?", (expense_id[0],))
                        print(f"تم حذف المصروف بمعرف {expense_id[0]}")
                    else:
                        # محاولة الحذف باستخدام البيانات المتاحة
                        print(f"محاولة حذف المصروف: الاسم={name}, البند={item}, التاريخ={date_text}")
                        if self.db.db_type == 'mysql':
                            c.execute("DELETE FROM expenses WHERE name=%s AND item=%s AND date LIKE %s",
                                    (name, item, f"%{date_text}%"))
                        else:
                            c.execute("DELETE FROM expenses WHERE name=? AND item=? AND date LIKE ?",
                                    (name, item, f"%{date_text}%"))

                conn.commit()
                conn.close()

                # إعادة تحميل المصروفات
                self.load_expenses()
                QMessageBox.information(self, "نجاح", "تم حذف المصروفات المحددة بنجاح")
            except Exception as e:
                print(f"خطأ في حذف المصروف: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروفات: {str(e)}")
                if 'conn' in locals() and conn:
                    conn.close()

    def add_expense(self):
        dialog = ExpenseDialog()
        if dialog.exec():
            name, amount_str, item, date = dialog.get_expense_data()

            try:
                amount = float(amount_str or 0)

                conn = self.db.get_connection()
                c = conn.cursor()

                if self.db.db_type == 'mysql':
                    c.execute("INSERT INTO expenses (name, item, amount, date) VALUES (%s, %s, %s, %s)",
                            (name, item, amount, date))
                else:
                    c.execute("INSERT INTO expenses (name, item, amount, date) VALUES (?, ?, ?, ?)",
                            (name, item, amount, date))

                conn.commit()
                conn.close()

                self.load_expenses()
            except ValueError:
                QMessageBox.warning(self, "خطأ", "الرجاء إدخال قيمة صحيحة للمبلغ")
            except Exception as e:
                print(f"خطأ في إضافة المصروف: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"فشل في إضافة المصروف: {str(e)}")

    def filter_expenses(self):
        item_filter = self.item_search.text().lower()
        name_filter = self.name_search.text().lower()
        amount_filter = self.amount_search.text()

        for row in range(self.expenses_table.rowCount()):
            show_row = True
            if item_filter and item_filter not in self.expenses_table.item(row, 3).text().lower():
                show_row = False
            if name_filter and name_filter not in self.expenses_table.item(row, 1).text().lower():
                show_row = False
            if amount_filter and amount_filter not in self.expenses_table.item(row, 2).text():
                show_row = False

            self.expenses_table.setRowHidden(row, not show_row)

        # تحديث المبلغ الإجمالي بعد التصفية
        self.update_total_amount()

    def filter_expenses_by_period(self):
        """تصفية المصروفات حسب الفترة المحددة (اليوم، الشهر، السنة)"""
        self.load_expenses()

    def load_expenses(self):
        try:
            conn = self.db.get_connection()
            c = conn.cursor()

            # تحديد الاستعلام بناءً على الفترة المحددة
            period_filter = ""
            period_params = []
            period_index = getattr(self, 'period_combo', None)

            if period_index and hasattr(period_index, 'currentIndex'):
                period_index = period_index.currentIndex()
                today = datetime.now().strftime('%Y-%m-%d')

                if period_index == 1:  # اليوم
                    if self.db.db_type == 'mysql':
                        period_filter = " WHERE date = %s"
                    else:
                        period_filter = " WHERE date = ?"
                    period_params.append(today)
                elif period_index == 2:  # الشهر
                    current_month = datetime.now().strftime('%Y-%m')
                    if self.db.db_type == 'mysql':
                        period_filter = " WHERE date LIKE %s"
                    else:
                        period_filter = " WHERE date LIKE ?"
                    period_params.append(f"{current_month}%")
                elif period_index == 3:  # السنة
                    current_year = datetime.now().strftime('%Y')
                    if self.db.db_type == 'mysql':
                        period_filter = " WHERE date LIKE %s"
                    else:
                        period_filter = " WHERE date LIKE ?"
                    period_params.append(f"{current_year}%")

            # تنفيذ الاستعلام
            query = f"SELECT name, amount, item, date FROM expenses{period_filter}"
            c.execute(query, period_params)
            expenses = c.fetchall()

            # حساب المبلغ الإجمالي
            total_amount = 0
            for expense in expenses:
                total_amount += float(expense[1])  # المبلغ هو العنصر الثاني في كل صف

            # تحديث عرض المبلغ الإجمالي
            if hasattr(self, 'total_amount_label'):
                self.total_amount_label.setText(self.format_amount(total_amount))

            conn.close()

            self.expenses_table.setRowCount(len(expenses))
            for i, expense in enumerate(expenses):
                # إضافة خانة التحديد في العمود الأول
                checkbox = QTableWidgetItem()
                checkbox.setCheckState(Qt.Unchecked)
                checkbox.setTextAlignment(Qt.AlignCenter)
                self.expenses_table.setItem(i, 0, checkbox)

                # إضافة بيانات المصروف
                for j, value in enumerate(expense):
                    if j == 1:
                        item = QTableWidgetItem(self.format_amount(value))
                    else:
                        item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.expenses_table.setItem(i, j+1, item)
        except Exception as e:
            print(f"خطأ في تحميل المصروفات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المصروفات: {str(e)}")

    def format_amount(self, amount):
        """تنسيق المبلغ بوضع فاصلة بعد كل ثلاث مراتب وإزالة الأصفار بعد الفاصلة العشرية"""
        try:
            # تحويل المبلغ إلى رقم
            amount_float = float(amount)
            # إزالة الأصفار بعد الفاصلة العشرية إذا كان الرقم صحيحًا
            if amount_float == int(amount_float):
                amount_str = "{:,}".format(int(amount_float))
            else:
                # تنسيق الرقم مع الفاصلة العشرية
                amount_str = "{:,.2f}".format(amount_float).rstrip('0').rstrip('.') if '.' in "{:,.2f}".format(amount_float) else "{:,}".format(int(amount_float))
            return amount_str
        except (ValueError, TypeError):
            return str(amount)

    def center_window(self):
        """توسيط النافذة في وسط الشاشة"""
        screen_geometry = QApplication.desktop().screenGeometry()
        window_geometry = self.geometry()

        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2

        self.move(x, y)

    def update_total_amount(self):
        """تحديث المبلغ الإجمالي بناءً على الصفوف المرئية في الجدول"""
        total = 0
        for row in range(self.expenses_table.rowCount()):
            if not self.expenses_table.isRowHidden(row):
                amount_str = self.expenses_table.item(row, 2).text()
                try:
                    # إزالة الفواصل من النص قبل التحويل إلى رقم
                    amount_str = amount_str.replace(',', '')
                    amount = float(amount_str)
                    total += amount
                except (ValueError, AttributeError):
                    pass

        self.total_amount_label.setText(self.format_amount(total))

class ExpenseDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("إضافة مصروف جديد")
        self.setGeometry(200, 200, 400, 300)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QLineEdit, QDateEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                margin-bottom: 10px;
            }
            QLineEdit:focus, QDateEdit:focus {
                border: 2px solid #3498db;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
                color: white;
            }
            QPushButton[text="حفظ"] {
                background-color: #2ecc71;
            }
            QPushButton[text="حفظ"]:hover {
                background-color: #27ae60;
            }
            QPushButton[text="إلغاء"] {
                background-color: #e74c3c;
            }
            QPushButton[text="إلغاء"]:hover {
                background-color: #c0392b;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("الاسم")
        layout.addWidget(self.name_input)

        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("المبلغ")
        layout.addWidget(self.amount_input)

        self.item_input = QLineEdit()
        self.item_input.setPlaceholderText("البضاعة")
        layout.addWidget(self.item_input)

        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        layout.addWidget(self.date_input)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.accept)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def get_expense_data(self):
        return (
            self.name_input.text(),
            self.amount_input.text(),
            self.item_input.text(),
            self.date_input.date().toString("yyyy-MM-dd")
        )

    def accept(self):
        if not self.name_input.text() or not self.item_input.text() or not self.amount_input.text():
            QMessageBox.warning(self, "تنبيه", "الرجاء ملء جميع الحقول")
            return
        try:
            float(self.amount_input.text())
        except ValueError:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم صحيح في حقل المبلغ")
            return
        super().accept()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.accept()
        else:
            super().keyPressEvent(event)

class LoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول")
        self.setGeometry(300, 300, 400, 200)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                margin-bottom: 10px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
                color: white;
                background-color: #3498db;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QLabel {
                color: #2c3e50;
                font-size: 12px;
                margin-bottom: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        layout.addWidget(self.username_input)

        # كلمة المرور
        password_label = QLabel("كلمة المرور:")
        layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_input)

        # زر تسجيل الدخول
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.clicked.connect(self.check_login)
        layout.addWidget(login_btn)

        self.setLayout(layout)

        self.user_type = None  # لتخزين نوع المستخدم بعد تسجيل الدخول

    def check_login(self):
        username = self.username_input.text()
        password = self.password_input.text()

        if not username or not password:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # استخدام وحدة قاعدة البيانات
            db = Database()
            conn = db.get_connection()
            c = conn.cursor()

            if db.db_type == 'mysql':
                c.execute("SELECT user_type FROM users WHERE username=%s AND password=%s",
                        (username, password))
            else:
                c.execute("SELECT user_type FROM users WHERE username=? AND password=?",
                        (username, password))

            result = c.fetchone()
            conn.close()

            if result:
                self.user_type = result[0]
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تسجيل الدخول: {str(e)}")

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.check_login()
        else:
            super().keyPressEvent(event)

if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)

    # عرض نافذة تسجيل الدخول أولاً
    login_dialog = LoginDialog()
    if login_dialog.exec() == QDialog.Accepted:
        # إذا تم تسجيل الدخول بنجاح، افتح النافذة الرئيسية مع نوع المستخدم
        window = AccountsWindow()
        window.show()
        sys.exit(app.exec())
