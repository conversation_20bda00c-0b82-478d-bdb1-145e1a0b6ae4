#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لتشخيص مشاكل الاتصال بقاعدة البيانات
"""

import os
import sys
import traceback
import json

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mysql_connection():
    """اختبار الاتصال المباشر بـ MySQL"""
    try:
        print("\n" + "=" * 60)
        print("اختبار الاتصال المباشر بـ MySQL".center(60))
        print("=" * 60)
        
        # قراءة إعدادات قاعدة البيانات
        config_file = "app_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            print("✗ ملف التكوين غير موجود")
            return False
        
        # إعدادات الاتصال
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306')),
            'charset': 'utf8mb4'
        }
        
        print(f"إعدادات الاتصال:")
        print(f"  الخادم: {mysql_config['host']}")
        print(f"  المنفذ: {mysql_config['port']}")
        print(f"  المستخدم: {mysql_config['user']}")
        print(f"  قاعدة البيانات: {mysql_config['database']}")
        
        # محاولة الاتصال باستخدام PyMySQL
        try:
            import pymysql
            print("\n✓ تم العثور على PyMySQL")
            
            conn = pymysql.connect(**mysql_config)
            print("✓ تم الاتصال بـ MySQL باستخدام PyMySQL")
            
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ إصدار MySQL: {version[0]}")
            
            # التحقق من الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"✓ عدد الجداول: {len(tables)}")
            
            # التحقق من جدول المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✓ عدد المستخدمين: {user_count}")
            
            conn.close()
            return True
            
        except ImportError:
            print("✗ PyMySQL غير متاح")
            
        # محاولة الاتصال باستخدام mysql-connector-python
        try:
            import mysql.connector
            print("\n✓ تم العثور على mysql-connector-python")
            
            conn = mysql.connector.connect(**mysql_config)
            print("✓ تم الاتصال بـ MySQL باستخدام mysql-connector-python")
            
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ إصدار MySQL: {version[0]}")
            
            # التحقق من الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"✓ عدد الجداول: {len(tables)}")
            
            # التحقق من جدول المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✓ عدد المستخدمين: {user_count}")
            
            conn.close()
            return True
            
        except ImportError:
            print("✗ mysql-connector-python غير متاح")
            
        return False
        
    except Exception as e:
        print(f"✗ خطأ في الاتصال بـ MySQL: {str(e)}")
        traceback.print_exc()
        return False

def test_database_class():
    """اختبار فئة Database"""
    try:
        print("\n" + "=" * 60)
        print("اختبار فئة Database".center(60))
        print("=" * 60)
        
        from db.models import Database
        
        # إنشاء كائن Database
        db = Database()
        print(f"✓ تم إنشاء كائن Database")
        print(f"نوع قاعدة البيانات: {db.db_type}")
        
        # اختبار الاتصال
        conn = db.get_connection()
        print("✓ تم الحصول على اتصال من فئة Database")
        
        cursor = conn.cursor()
        
        # التحقق من جدول المستخدمين
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()
        print(f"✓ عدد المستخدمين في قاعدة البيانات: {len(users)}")
        
        if users:
            print("\nقائمة المستخدمين:")
            for user in users:
                print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار فئة Database: {str(e)}")
        traceback.print_exc()
        return False

def test_login_functionality():
    """اختبار وظيفة تسجيل الدخول"""
    try:
        print("\n" + "=" * 60)
        print("اختبار وظيفة تسجيل الدخول".center(60))
        print("=" * 60)
        
        # محاولة استيراد وحدة تسجيل الدخول
        try:
            from ui.login import LoginWindow
            print("✓ تم استيراد وحدة تسجيل الدخول")
        except Exception as e:
            print(f"✗ خطأ في استيراد وحدة تسجيل الدخول: {str(e)}")
            return False
        
        # اختبار الاتصال بقاعدة البيانات من وحدة تسجيل الدخول
        from db.models import Database
        
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # محاولة تسجيل الدخول
        username = "admin"
        password = "admin123"
        
        # استخدام نفس الاستعلام المستخدم في واجهة تسجيل الدخول
        if db.db_type == 'mysql':
            cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s", (username, password))
        else:
            cursor.execute("SELECT * FROM users WHERE username = ? AND password = ?", (username, password))
        
        user = cursor.fetchone()
        
        if user:
            print(f"✓ تسجيل الدخول نجح للمستخدم: {user[1]}")
            if len(user) > 3:
                print(f"✓ الدور: {user[3]}")
        else:
            print("✗ فشل تسجيل الدخول")
            
            # التحقق من وجود المستخدم
            if db.db_type == 'mysql':
                cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            else:
                cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            
            user_check = cursor.fetchone()
            if user_check:
                print(f"✓ المستخدم موجود: {user_check[1]}")
                print(f"✗ كلمة المرور غير صحيحة")
                print(f"كلمة المرور المحفوظة: '{user_check[2]}'")
                print(f"كلمة المرور المدخلة: '{password}'")
            else:
                print(f"✗ المستخدم غير موجود: {username}")
        
        conn.close()
        return user is not None
        
    except Exception as e:
        print(f"✗ خطأ في اختبار تسجيل الدخول: {str(e)}")
        traceback.print_exc()
        return False

def check_mysql_service():
    """التحقق من خدمة MySQL"""
    try:
        print("\n" + "=" * 60)
        print("التحقق من خدمة MySQL".center(60))
        print("=" * 60)
        
        import subprocess
        
        # التحقق من حالة خدمة MySQL
        try:
            result = subprocess.run(['sc', 'query', 'MySQL80'], 
                                  capture_output=True, text=True, shell=True)
            if 'RUNNING' in result.stdout:
                print("✓ خدمة MySQL تعمل")
                return True
            else:
                print("✗ خدمة MySQL لا تعمل")
                print("محاولة تشغيل خدمة MySQL...")
                
                # محاولة تشغيل الخدمة
                start_result = subprocess.run(['net', 'start', 'MySQL80'], 
                                            capture_output=True, text=True, shell=True)
                if start_result.returncode == 0:
                    print("✓ تم تشغيل خدمة MySQL")
                    return True
                else:
                    print(f"✗ فشل في تشغيل خدمة MySQL: {start_result.stderr}")
                    return False
        except Exception as e:
            print(f"✗ خطأ في التحقق من خدمة MySQL: {str(e)}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في فحص خدمة MySQL: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("تشخيص مشاكل الاتصال بقاعدة البيانات".center(60))
    print("=" * 60)
    
    # 1. التحقق من خدمة MySQL
    mysql_service_ok = check_mysql_service()
    
    # 2. اختبار الاتصال المباشر بـ MySQL
    mysql_connection_ok = test_mysql_connection()
    
    # 3. اختبار فئة Database
    database_class_ok = test_database_class()
    
    # 4. اختبار وظيفة تسجيل الدخول
    login_ok = test_login_functionality()
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("ملخص النتائج".center(60))
    print("=" * 60)
    
    print(f"خدمة MySQL: {'✓ تعمل' if mysql_service_ok else '✗ لا تعمل'}")
    print(f"الاتصال بـ MySQL: {'✓ يعمل' if mysql_connection_ok else '✗ لا يعمل'}")
    print(f"فئة Database: {'✓ تعمل' if database_class_ok else '✗ لا تعمل'}")
    print(f"تسجيل الدخول: {'✓ يعمل' if login_ok else '✗ لا يعمل'}")
    
    if all([mysql_service_ok, mysql_connection_ok, database_class_ok, login_ok]):
        print("\n✅ جميع الاختبارات نجحت! يجب أن يعمل تسجيل الدخول بشكل طبيعي.")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح.")
        
        if not mysql_service_ok:
            print("- تأكد من تشغيل خدمة MySQL")
        if not mysql_connection_ok:
            print("- تحقق من إعدادات الاتصال بـ MySQL")
        if not database_class_ok:
            print("- هناك مشكلة في فئة Database")
        if not login_ok:
            print("- هناك مشكلة في وظيفة تسجيل الدخول")

if __name__ == "__main__":
    main()
