"""
إصلاح جدول المستخدمين في قاعدة بيانات MySQL
"""

import pymysql
from utils.config import load_config

def fix_users_table():
    """إضافة عمود 'role' إلى جدول المستخدمين وإضافة مستخدم افتراضي"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()
        
        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
        
        # التحقق من وجود عمود 'role' في جدول المستخدمين
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'users' 
            AND column_name = 'role'
        """)
        column_exists = cursor.fetchone()[0]
        
        # إضافة عمود 'role' إذا لم يكن موجودًا
        if column_exists == 0:
            print("إضافة عمود 'role' إلى جدول المستخدمين")
            cursor.execute("ALTER TABLE users ADD COLUMN role VARCHAR(50) NOT NULL DEFAULT 'user'")
        else:
            print("عمود 'role' موجود بالفعل في جدول المستخدمين")
        
        # التحقق من وجود مستخدم افتراضي
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_exists = cursor.fetchone()[0]
        
        # إضافة مستخدم افتراضي إذا لم يكن موجودًا
        if admin_exists == 0:
            print("إضافة مستخدم افتراضي")
            cursor.execute("INSERT INTO users (username, password, role) VALUES ('admin', 'admin123', 'admin')")
        else:
            print("المستخدم الافتراضي موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        print("تم إصلاح جدول المستخدمين بنجاح")
        
        # إغلاق الاتصال
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في إصلاح جدول المستخدمين: {str(e)}")
        return False

if __name__ == "__main__":
    fix_users_table()
