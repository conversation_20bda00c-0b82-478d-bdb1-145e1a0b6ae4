#!/usr/bin/env python
"""
u0647u0630u0627 u0627u0644u0633u0643u0631u064au0628u062a u064au062au062du0642u0642 u0645u0646 u0647u064au0643u0644 u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a u0648u064au0637u0628u0639 u0642u0627u0626u0645u0629 u0627u0644u062cu062fu0627u0648u0644 u0627u0644u0645u0648u062cu0648u062fu0629
"""

import os
import sys
import sqlite3

# u0623u0636u0641 u0627u0644u0645u062cu0644u062f u0627u0644u0631u0626u064au0633u064a u0644u0644u0645u0634u0631u0648u0639 u0625u0644u0649 u0645u0633u0627u0631u0627u062a u0627u0644u0628u062du062b u0639u0646 u0627u0644u062du0632u0645
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# u0627u0633u062au064au0631u0627u062f u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a
from db.models import Database

def check_database_structure():
    """u0641u062du0635 u0647u064au0643u0644 u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a"""
    try:
        # u0627u0644u0627u062au0635u0627u0644 u0628u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()

        # u0627u0644u062du0635u0648u0644 u0639u0644u0649 u0642u0627u0626u0645u0629 u0627u0644u062cu062fu0627u0648u0644
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("\n=== u0642u0627u0626u0645u0629 u0627u0644u062cu062fu0627u0648u0644 u0627u0644u0645u0648u062cu0648u062fu0629 u0641u064a u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a ===")
        for i, table in enumerate(tables):
            print(f"{i+1}. {table[0]}")
            
        # u0641u062du0635 u0647u064au0643u0644 u062cu062fu0648u0644 orders
        print("\n=== u0647u064au0643u0644 u062cu062fu0648u0644 orders ===")
        cursor.execute("PRAGMA table_info(orders);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"- {col[1]} ({col[2]}){'  PRIMARY KEY' if col[5] == 1 else ''}")

        # u0641u062du0635 u0647u064au0643u0644 u062cu062fu0648u0644 representatives
        print("\n=== u0647u064au0643u0644 u062cu062fu0648u0644 representatives ===")
        cursor.execute("PRAGMA table_info(representatives);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"- {col[1]} ({col[2]}){'  PRIMARY KEY' if col[5] == 1 else ''}")

    except sqlite3.Error as e:
        print(f"\nu062eu0637u0623 u0641u064a u0641u062du0635 u0642u0627u0639u062fu0629 u0627u0644u0628u064au0627u0646u0627u062a: {e}")
    finally:
        # u0625u063au0644u0627u0642 u0627u0644u0627u062au0635u0627u0644
        if conn:
            conn.close()

if __name__ == "__main__":
    check_database_structure()
