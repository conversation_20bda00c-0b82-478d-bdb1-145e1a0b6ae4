from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QSpinBox, QPushButton, QMessageBox,
                           QListWidget, QListWidgetItem, QWidget)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QKeyEvent
from db.models import Database

class NewOrderDialog(QDialog):
    """نافذة إدخال/تعديل الطلب"""
    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.db = Database()
        self.order_id = order_id
        self.is_loading = True  # إضافة متغير للتحكم في تحميل البيانات
        self.setup_ui()
        self.is_loading = False

        if order_id:
            self.setWindowTitle("تعديل الطلب")
            self.load_order_data()
        else:
            self.setWindowTitle("طلب جديد")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # تعيين الهوامش للنافذة
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # رقم الهاتف
        phone_layout = QHBoxLayout()
        phone_label = QLabel("رقم الهاتف:")
        self.phone = QLineEdit()
        self.phone.installEventFilter(self)  # إضافة معالج حدث المفاتيح
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone)
        layout.addLayout(phone_layout)

        # رقم الهاتف البديل
        phone2_layout = QHBoxLayout()
        phone2_label = QLabel("رقم الهاتف 2:")
        self.phone2 = QLineEdit()
        self.phone2.installEventFilter(self)  # إضافة معالج حدث المفاتيح
        phone2_layout.addWidget(phone2_label)
        phone2_layout.addWidget(self.phone2)
        layout.addLayout(phone2_layout)

        # العنوان
        address_layout = QHBoxLayout()
        address_label = QLabel("العنوان:")
        self.address = QLineEdit()
        self.address.installEventFilter(self)  # إضافة معالج حدث المفاتيح
        address_layout.addWidget(address_label)
        address_layout.addWidget(self.address)
        layout.addLayout(address_layout)

        # رقم الوصل (مخفي)
        self.receipt_number = QLineEdit()
        self.receipt_number.hide()  # إخفاء الحقل بدلاً من حذفه

        # ملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        self.notes = QLineEdit()
        self.notes.installEventFilter(self)  # إضافة معالج حدث المفاتيح
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes)
        layout.addLayout(notes_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # المسافة بين الأزرار

        # إنشاء حاوية للأزرار لتحديد عرضها
        buttons_container = QWidget()
        buttons_container.setFixedWidth(200)  # تحديد عرض حاوية الأزرار
        buttons_container_layout = QHBoxLayout(buttons_container)
        buttons_container_layout.setContentsMargins(0, 0, 0, 0)

        save_btn = QPushButton("حفظ")
        save_btn.setFixedWidth(90)  # تحديد عرض زر الحفظ
        save_btn.clicked.connect(self.save_order)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFixedWidth(90)  # تحديد عرض زر الإلغاء
        cancel_btn.clicked.connect(self.reject)

        buttons_container_layout.addWidget(save_btn)
        buttons_container_layout.addWidget(cancel_btn)

        buttons_layout.addWidget(buttons_container, alignment=Qt.AlignCenter)
        layout.addLayout(buttons_layout)

    def eventFilter(self, obj, event):
        """معالجة أحداث المفاتيح للعناصر"""
        if event.type() == QKeyEvent.KeyPress:
            # معالجة مفتاح التاب للحقول الأخرى
            if event.key() == Qt.Key_Tab:
                if obj is self.phone:
                    self.phone2.setFocus()
                    return True
                elif obj is self.phone2:
                    self.address.setFocus()
                    return True
                elif obj is self.address:
                    self.notes.setFocus()
                    return True

            # معالجة مفتاح التاب للحقول الأخرى
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                if obj is self.phone:
                    self.phone2.setFocus()
                    return True
                elif obj is self.phone2:
                    self.address.setFocus()
                    return True
                elif obj is self.address:
                    self.notes.setFocus()
                    return True

        return super().eventFilter(obj, event)

    def load_order_data(self):
        """تحميل بيانات الطلب"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT phone, phone2, address, receipt_number, notes
                FROM orders WHERE id = ?
            """, (self.order_id,))

            row = cursor.fetchone()
            if row:
                self.is_loading = True  # تعيين العلم لتجنب فتح نافذة الأبعاد
                # تعيين البيانات في الحقول
                self.phone.setText(row[0] or "")        # رقم الهاتف
                self.phone2.setText(row[1] or "")       # رقم الهاتف البديل
                self.address.setText(row[2] or "")      # العنوان
                self.receipt_number.setText(row[3] or "") # رقم الوصل (مخفي)
                self.notes.setText(row[4] or "")       # ملاحظات
                self.is_loading = False

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطلب: {str(e)}")

    def save_order(self):
        """حفظ الطلب"""
        try:
            # التحقق من البيانات المطلوبة
            phone = self.phone.text().strip()
            if not phone:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الهاتف")
                self.phone.setFocus()
                return

            # التحقق من عدد أرقام الهاتف (يجب أن يكون 11 رقماً بالضبط)
            if len(phone) != 11 or not phone.isdigit():
                QMessageBox.warning(self, "تنبيه", "رقم الهاتف يجب أن يتكون من 11 رقماً بالضبط")
                self.phone.setFocus()
                return

            # التحقق من رقم الهاتف البديل (إذا تم إدخاله)
            phone2 = self.phone2.text().strip()
            if phone2 and (len(phone2) != 11 or not phone2.isdigit()):
                QMessageBox.warning(self, "تنبيه", "رقم الهاتف البديل يجب أن يتكون من 11 رقماً بالضبط")
                self.phone2.setFocus()
                return

            if not self.address.text().strip():
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال العنوان")
                self.address.setFocus()
                return

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # حساب المساحة الكلية والسعر
            total_area = 0
            total_price = 0

            if self.order_id:  # تعديل طلب موجود
                # تحديث بيانات الطلب
                cursor.execute("""
                    UPDATE orders
                    SET phone = ?, phone2 = ?, address = ?,
                        receipt_number = ?, total_area = ?, total_price = ?,
                        notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    phone,
                    phone2,
                    self.address.text().strip(),
                    self.receipt_number.text().strip(),
                    total_area,
                    total_price,
                    self.notes.text().strip(),
                    self.order_id
                ))

            else:  # طلب جديد
                cursor.execute("""
                    INSERT INTO orders (phone, phone2, address, receipt_number,
                          total_area, total_price, notes, created_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
                """, (
                    phone,
                    phone2,
                    self.address.text().strip(),
                    self.receipt_number.text().strip(),
                    total_area,
                    total_price,
                    self.notes.text().strip(),
                    'جديد'  # تعيين حالة الطلب كـ "جديد"
                ))
                self.order_id = cursor.lastrowid

            conn.commit()
            conn.close()
            self.accept()

        except Exception as e:
            print(f"خطأ في حفظ الطلب: {str(e)}")
            print("التفاصيل الكاملة للخطأ:")
            import traceback
            print(traceback.format_exc())
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الطلب")
