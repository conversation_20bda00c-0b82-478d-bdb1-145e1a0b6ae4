"""
تعديل الاستعلامات الخاطئة في ملفات البرنامج
"""

import os
import re

def fix_queries():
    """تعديل الاستعلامات الخاطئة في ملفات البرنامج"""
    try:
        # قائمة الملفات التي تحتاج إلى تعديل
        files_to_fix = [
            'src/warehouse.py',
            'src/completed_orders.py',
            'src/delegate_orders_simple.py',
            'src/delegate_distribution.py',
            'src/orders.py',
            'src/rep_orders_view.py'
        ]
        
        # قائمة التعديلات
        replacements = [
            # تعديل استعلامات SQLite إلى MySQL
            (r'\?', '%s'),  # تغيير علامة الاستفهام إلى %s
            (r'PRAGMA', '-- PRAGMA'),  # تعليق أوامر PRAGMA
            
            # تعديل الاستعلامات التي تستخدم أعمدة غير موجودة
            (r'o\.has_delivery_fee', 'o.has_delivery_fee'),  # تم إضافة هذا العمود
            (r'o\.delivery_fee', 'o.delivery_fee'),  # تم إضافة هذا العمود
            (r'o\.pickup_representative_id', 'o.pickup_representative_id'),  # تم إضافة هذا العمود
            
            # تعديل الاستعلامات التي تستخدم جداول غير موجودة
            (r'LEFT JOIN representatives r ON o\.pickup_representative_id = r\.id', 'LEFT JOIN representatives r ON o.pickup_representative_id = r.id'),  # تم إضافة هذا الجدول
        ]
        
        for file_path in files_to_fix:
            if not os.path.exists(file_path):
                print(f"الملف غير موجود: {file_path}")
                continue
            
            print(f"تعديل الاستعلامات في الملف: {file_path}")
            
            # قراءة محتوى الملف
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # تطبيق التعديلات
            modified_content = content
            for pattern, replacement in replacements:
                modified_content = re.sub(pattern, replacement, modified_content)
            
            # كتابة المحتوى المعدل
            if modified_content != content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(modified_content)
                print(f"تم تعديل الاستعلامات في الملف: {file_path}")
            else:
                print(f"لم يتم تعديل الاستعلامات في الملف: {file_path}")
        
        print("تم تعديل الاستعلامات الخاطئة في ملفات البرنامج بنجاح")
        
        return True
    except Exception as e:
        print(f"خطأ في تعديل الاستعلامات الخاطئة: {str(e)}")
        return False

if __name__ == "__main__":
    fix_queries()
