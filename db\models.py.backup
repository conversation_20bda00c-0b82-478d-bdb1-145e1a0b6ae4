import sqlite3
import os
import time

# استخدام PyMySQL بدلاً من mysql-connector-python
try:
    import pymysql
    from pymysql import Error
    USING_PYMYSQL = True
except ImportError:
    # الرجوع إلى mysql-connector-python إذا لم يكن PyMySQL متاحًا
    import mysql.connector
    from mysql.connector import Error
    USING_PYMYSQL = False
from sqlalchemy import create_engine, Column, Integer, String, Float, ForeignKey, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import datetime
from utils.config import load_config

Base = declarative_base()

class Order(Base):
    """نموذج الطلبات"""
    __tablename__ = 'orders'

    id = Column(Integer, primary_key=True)
    phone = Column(String, nullable=False)
    phone2 = Column(String)  # رقم الهاتف الثاني
    address = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)
    receipt_number = Column(String)  # رقم الوصل
    total_area = Column(Float, default=0)  # المساحة الكلية
    total_price = Column(Float, default=0)  # السعر الإجمالي
    notes = Column(String)  # ملاحظات
    delegate_id = Column(Integer, ForeignKey('delegates.id'), nullable=True)  # معرف المندوب
    delegate_name = Column(String)  # اسم المندوب
    status = Column(String, default='جديد')  # حالة الطلب

    # العلاقة مع أبعاد السجاد
    carpet_dimensions = relationship("CarpetDimension", back_populates="order", cascade="all, delete-orphan")

class CarpetDimension(Base):
    """نموذج أبعاد السجاد"""
    __tablename__ = 'carpet_dimensions'

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id', ondelete='CASCADE'), nullable=False)
    length = Column(Float, nullable=False)
    width = Column(Float, nullable=False)
    total_area = Column(Float, nullable=False)

    # العلاقة مع الطلب
    order = relationship("Order", back_populates="carpet_dimensions")

class Settings(Base):
    """نموذج الإعدادات"""
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    price_per_meter = Column(Float, default=1000.0)
    blanket_price = Column(Float, default=5000.0)  # إضافة حقل سعر البطانية
    delivery_price = Column(Float, default=2000.0)  # إضافة حقل سعر التوصيل
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Employee(Base):
    """نموذج الموظفين"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    phone = Column(String, nullable=False)
    salary = Column(Float, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)

class Expense(Base):
    """نموذج المصروفات"""
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    item = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)

class Delegate(Base):
    """نموذج المندوبين"""
    __tablename__ = 'delegates'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

class DelegateOrder(Base):
    """نموذج طلبات المندوبين"""
    __tablename__ = 'delegate_orders'

    id = Column(Integer, primary_key=True)
    receipt_number = Column(String)
    phone_number = Column(String, nullable=False)
    area = Column(String)
    carpet_count = Column(Integer, nullable=False, default=0)
    blanket_count = Column(Integer, default=0)
    total_price = Column(Float, nullable=False, default=0)
    order_id = Column(Integer, ForeignKey('orders.id'))
    delegate_id = Column(Integer, ForeignKey('delegates.id'))
    representative_name = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Database:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not Database._initialized:
            # تحميل إعدادات البرنامج
            self.config = load_config()
            self.db_type = self.config.get('db_type', 'sqlite')

            if self.db_type == 'mysql':
                # إعدادات الاتصال بـ MySQL
                self.mysql_config = {
                    'host': self.config.get('db_host', 'localhost'),
                    'user': self.config.get('db_user', 'carpet_user'),
                    'password': self.config.get('db_password', '@#57111819752#@'),
                    'database': self.config.get('db_name', 'carpet_cleaning_service'),
                    'port': int(self.config.get('db_port', '3306')),
                    'charset': 'utf8mb4',
                    'collation': 'utf8mb4_unicode_ci',
                    'raise_on_warnings': True
                }
            else:
                # استخدام مسار قاعدة البيانات المطلقة لـ SQLite
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(current_dir)
                self.db_path = os.path.join(project_root, 'carpet_cleaning.db')

            self.create_tables()
            self.update_tables()  # تحديث هيكل الجداول
            Database._initialized = True

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        if self.db_type == 'mysql':
            try:
                # تعديل إعدادات الاتصال لتجنب مشكلة access violation
                # إزالة بعض الخيارات التي قد تسبب مشاكل
                safe_config = {
                    'host': self.mysql_config['host'],
                    'user': self.mysql_config['user'],
                    'password': self.mysql_config['password'],
                    'database': self.mysql_config['database'],
                    'port': self.mysql_config['port']
                }

                # اتصال بقاعدة بيانات MySQL
                if USING_PYMYSQL:
                    # استخدام PyMySQL
                    conn = pymysql.connect(**safe_config)
                else:
                    # استخدام mysql-connector-python
                    conn = mysql.connector.connect(**safe_config)
                return conn
            except Error as e:
                print(f"خطأ في الاتصال بقاعدة بيانات MySQL: {e}")
                print("الرجوع إلى استخدام SQLite كاحتياطي")
                # الرجوع إلى استخدام SQLite في حالة فشل الاتصال بـ MySQL
                conn = sqlite3.connect('carpet_cleaning.db', timeout=30.0)
                conn.execute("PRAGMA foreign_keys = ON")
                conn.text_factory = str
                return conn
            except Exception as e:
                print(f"خطأ غير متوقع في الاتصال بقاعدة بيانات MySQL: {e}")
                print("الرجوع إلى استخدام SQLite كاحتياطي")
                # الرجوع إلى استخدام SQLite في حالة فشل الاتصال بـ MySQL
                conn = sqlite3.connect('carpet_cleaning.db', timeout=30.0)
                conn.execute("PRAGMA foreign_keys = ON")
                conn.text_factory = str
                return conn
        else:
            # اتصال بقاعدة بيانات SQLite
            # إضافة مهلة زمنية (30 ثانية) للانتظار في حالة كانت قاعدة البيانات مشغولة
            print(f"استخدام قاعدة بيانات SQLite: {self.db_path}")
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.execute("PRAGMA foreign_keys = ON")
            # إضافة دعم اللغة العربية
            conn.text_factory = str
            return conn

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        if self.db_type == 'mysql':
            # في حالة MySQL، نستخدم ملف SQL منفصل لإنشاء الجداول
            # تم تنفيذه بالفعل عند تهيئة قاعدة البيانات
            return

        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # جدول المندوبين - إنشاؤه أولاً لأن الجداول الأخرى تعتمد عليه
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS delegates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    phone TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول الطلبات - إنشاؤه قبل جدول أبعاد السجاد لأن الأخير يعتمد عليه
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    receipt_number TEXT,
                    phone TEXT NOT NULL,
                    phone2 TEXT,
                    address TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_price REAL NOT NULL,
                    total_area REAL DEFAULT 0,
                    notes TEXT,
                    delegate_id INTEGER DEFAULT NULL REFERENCES delegates(id),
                    delegate_name TEXT,
                    status TEXT DEFAULT 'جديد'
                )
            """)

            # جدول أبعاد السجاد - إنشاؤه بعد جدول الطلبات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS carpet_dimensions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER NOT NULL,
                    length REAL NOT NULL,
                    width REAL NOT NULL,
                    total_area REAL NOT NULL,
                    FOREIGN KEY (order_id) REFERENCES orders(id)
                )
            """)

            # جدول الإعدادات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    price_per_meter REAL DEFAULT 1000.0,
                    blanket_price REAL DEFAULT 5000.0,
                    delivery_price REAL DEFAULT 2000.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول الموظفين
            #cursor.execute("""
                #CREATE TABLE IF NOT EXISTS employees (
                    #id INTEGER PRIMARY KEY AUTOINCREMENT,
                    #name TEXT NOT NULL,
                    #phone TEXT NOT NULL,
                    #salary REAL NOT NULL,
                    #created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                #)
            #""")

            # جدول المصروفات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    item TEXT NOT NULL,
                    amount REAL NOT NULL,
                    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # تم نقل إنشاء جدول المندوبين إلى بداية الدالة

            # جدول طلبات المندوبين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS delegate_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    receipt_number TEXT,
                    phone_number TEXT NOT NULL,
                    area TEXT,
                    carpet_count INTEGER NOT NULL DEFAULT 0,
                    blanket_count INTEGER DEFAULT 0,
                    total_price REAL NOT NULL DEFAULT 0,
                    order_id INTEGER,
                    delegate_id INTEGER,
                    representative_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders(id),
                    FOREIGN KEY (delegate_id) REFERENCES delegates(id)
                )
            """)

            # التحقق من وجود جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL
                )
            """)

            # إضافة مستخدم افتراضي إذا كان الجدول فارغاً
            cursor.execute('SELECT COUNT(*) FROM users')
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO users (username, password, role)
                    VALUES (?, ?, ?)
                """, ('admin', 'admin123', 'admin'))

            conn.commit()
            conn.close()

        except Exception as e:
            pass

    def update_tables(self):
        """تحديث هيكل الجداول"""
        if self.db_type == 'mysql':
            # في حالة MySQL، نقوم بتحديث الجداول
            conn = None
            try:
                conn = self.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود عمود has_delivery_fee في جدول orders
                try:
                    cursor.execute("SHOW COLUMNS FROM orders LIKE 'has_delivery_fee'")
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
                except Exception as e:
                    pass

                # التحقق من وجود عمود delivery_fee في جدول orders
                try:
                    cursor.execute("SHOW COLUMNS FROM orders LIKE 'delivery_fee'")
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE orders ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00")
                except Exception as e:
                    pass

                # التحقق من وجود عمود pickup_representative_id في جدول orders
                try:
                    cursor.execute("SHOW COLUMNS FROM orders LIKE 'pickup_representative_id'")
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE orders ADD COLUMN pickup_representative_id INT DEFAULT NULL")
                except Exception as e:
                    pass

                # التحقق من وجود عمود completion_date في جدول orders
                try:
                    cursor.execute("SHOW COLUMNS FROM orders LIKE 'completion_date'")
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE orders ADD COLUMN completion_date TIMESTAMP NULL DEFAULT NULL")
                except Exception as e:
                    pass

                # التحقق من وجود عمود carpet_count في جدول orders
                try:
                    cursor.execute("SHOW COLUMNS FROM orders LIKE 'carpet_count'")
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE orders ADD COLUMN carpet_count INT DEFAULT 0")
                except Exception as e:
                    pass

                # حفظ التغييرات
                conn.commit()

            except Exception as e:
                if conn:
                    conn.rollback()
            finally:
                if conn:
                    conn.close()
            return

        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود عمود total_area في جدول orders
            cursor.execute("PRAGMA table_info(orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'total_area' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN total_area REAL DEFAULT 0")

            # التحقق من وجود عمود blanket_count في جدول orders
            if 'blanket_count' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN blanket_count INTEGER DEFAULT 0")

            # التحقق من وجود عمود total_area في جدول carpet_dimensions
            cursor.execute("PRAGMA table_info(carpet_dimensions)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'total_area' not in columns:
                cursor.execute("ALTER TABLE carpet_dimensions ADD COLUMN total_area REAL")

            # التحقق من وجود عمود blanket_price في جدول settings
            cursor.execute("PRAGMA table_info(settings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'blanket_price' not in columns:
                cursor.execute("ALTER TABLE settings ADD COLUMN blanket_price REAL DEFAULT 5000.0")

            # التحقق من وجود عمود delivery_price في جدول settings
            if 'delivery_price' not in columns:
                cursor.execute("ALTER TABLE settings ADD COLUMN delivery_price REAL DEFAULT 2000.0")

            # التحقق من وجود عمود phone2 في جدول orders
            cursor.execute("PRAGMA table_info(orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'phone2' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN phone2 TEXT")

            # التحقق من وجود عمود notes في جدول orders
            cursor.execute("PRAGMA table_info(orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'notes' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN notes TEXT")

            # التحقق من وجود جدول المندوبين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS delegates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    phone TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # التحقق من وجود جدول طلبات المندوبين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS delegate_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    receipt_number TEXT,
                    phone_number TEXT NOT NULL,
                    area TEXT,
                    carpet_count INTEGER NOT NULL DEFAULT 0,
                    blanket_count INTEGER DEFAULT 0,
                    total_price REAL NOT NULL DEFAULT 0,
                    order_id INTEGER,
                    delegate_id INTEGER,
                    representative_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (delegate_id) REFERENCES delegates(id),
                    FOREIGN KEY (order_id) REFERENCES orders(id)
                )
            """)

            # التحقق من وجود عمود blanket_count في جدول delegate_orders
            cursor.execute("PRAGMA table_info(delegate_orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'blanket_count' not in columns:
                cursor.execute("ALTER TABLE delegate_orders ADD COLUMN blanket_count INTEGER DEFAULT 0")

            # التحقق من وجود الأعمدة الجديدة في جدول orders
            cursor.execute("PRAGMA table_info(orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'delegate_id' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN delegate_id INTEGER DEFAULT NULL REFERENCES delegates(id)")

            if 'delegate_name' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN delegate_name TEXT")

            if 'status' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN status TEXT DEFAULT 'جديد'")

            # التحقق من وجود عمود order_id في جدول delegate_orders
            cursor.execute("PRAGMA table_info(delegate_orders)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'order_id' not in columns:
                cursor.execute("ALTER TABLE delegate_orders ADD COLUMN order_id INTEGER REFERENCES orders(id)")
                print("تم إضافة عمود order_id إلى جدول delegate_orders")

            conn.commit()

        except Exception as e:
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def is_admin(self, username):
        """التحقق مما إذا كان المستخدم مديراً"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # استخدام صيغة استعلام مختلفة حسب نوع قاعدة البيانات
            if self.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL/PyMySQL
                cursor.execute("SELECT role FROM users WHERE username = %s", (username,))
            else:
                # استخدام ? مع SQLite
                cursor.execute("SELECT role FROM users WHERE username = ?", (username,))

            result = cursor.fetchone()
            conn.close()
            return result and result[0] == 'admin'
        except Exception as e:
            print(f"خطأ في التحقق من صلاحيات المستخدم: {str(e)}")
            return False

    def authenticate(self, username, password):
        """التحقق من صحة بيانات تسجيل الدخول"""
        if not username or not password:
            return None

        try:
            conn = self.get_connection()
            if not conn:
                return None

            cursor = conn.cursor()

            # استخدام صيغة استعلام مختلفة حسب نوع قاعدة البيانات
            if self.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL/PyMySQL
                cursor.execute("""
                    SELECT id, username, role
                    FROM users
                    WHERE username = %s AND password = %s
                """, (username, password))
            else:
                # استخدام ? مع SQLite
                cursor.execute("""
                    SELECT id, username, role
                    FROM users
                    WHERE username = ? AND password = ?
                """, (username, password))

            result = cursor.fetchone()
            conn.close()

            if result:
                return result  # يعيد (id, username, role)
            else:
                return None

        except Exception as e:
            if conn:
                conn.close()
            return None

    def add_user(self, username, password, role):
        """إضافة مستخدم جديد"""
        try:
            conn = self.get_connection()
            if not conn:
                return False, "خطأ في الاتصال بقاعدة البيانات"

            cursor = conn.cursor()

            # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            if self.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL/PyMySQL
                cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            else:
                # استخدام ? مع SQLite
                cursor.execute("SELECT id FROM users WHERE username = ?", (username,))

            if cursor.fetchone():
                conn.close()
                return False, "اسم المستخدم مسجل مسبقاً"

            # إضافة المستخدم
            if self.db_type == 'mysql':
                # استخدام %s بدلاً من ? مع MySQL/PyMySQL
                cursor.execute(
                    "INSERT INTO users (username, password, role) VALUES (%s, %s, %s)",
                    (username, password, role)
                )
            else:
                # استخدام ? مع SQLite
                cursor.execute(
                    "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                    (username, password, role)
                )

            conn.commit()

            # الحصول على معرف المستخدم الجديد
            user_id = cursor.lastrowid
            conn.close()

            return True, user_id

        except Exception as e:
            if conn:
                conn.close()
            return False, str(e)

    def close_connection(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        try:
            conn = self.get_connection()
            if conn:
                conn.close()
        except Exception as e:
            print(f"خطأ في إغلاق الاتصال بقاعدة البيانات: {str(e)}")

    def fetch_all(self, query, params=()):
        """تنفيذ استعلام وإرجاع جميع النتائج"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if self.db_type == 'mysql':
                # في MySQL نستخدم %s بدلاً من ? للمعاملات
                if '?' in query:
                    query = query.replace('?', '%s')

            cursor.execute(query, params)
            results = cursor.fetchall()
            cursor.close()
            conn.close()  # إغلاق الاتصال بعد الانتهاء
            return results
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            if conn:
                conn.close()
            raise e

    def fetch_one(self, query, params=()):
        """تنفيذ استعلام وإرجاع نتيجة واحدة"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if self.db_type == 'mysql':
                # في MySQL نستخدم %s بدلاً من ? للمعاملات
                if '?' in query:
                    query = query.replace('?', '%s')

            cursor.execute(query, params)
            result = cursor.fetchone()
            return result
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def execute(self, query, params=()):
        """تنفيذ استعلام بدون إرجاع نتائج"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if self.db_type == 'mysql':
                # في MySQL نستخدم %s بدلاً من ? للمعاملات
                if '?' in query:
                    query = query.replace('?', '%s')

            cursor.execute(query, params)
            conn.commit()
            cursor.close()
            conn.close()  # إغلاق الاتصال بعد الانتهاء
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            if conn:
                conn.rollback()
                conn.close()
            raise e

    # دوال خاصة بالموظفين
    def add_employee(self, name, phone, salary):
        """إضافة موظف جديد"""
        if not name or not phone or not salary:
            raise ValueError("جميع الحقول مطلوبة")

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO employees (name, phone, salary)
                VALUES (?, ?, ?)
            """, (name, phone, float(salary)))

            employee_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return employee_id

        except Exception as e:
            if conn:
                conn.close()
            raise e

    def get_employees(self):
        """الحصول على قائمة الموظفين"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, name, phone, salary, created_at
                FROM employees
                ORDER BY name
            """)

            employees = cursor.fetchall()
            conn.close()

            return employees

        except Exception as e:
            if conn:
                conn.close()
            raise e

    def delete_employee(self, employee_id):
        """حذف موظف"""
        conn = None
        try:
            if not employee_id:
                raise ValueError("معرف الموظف مطلوب")

            conn = self.get_connection()
            cursor = conn.cursor()

            # حذف الموظف
            cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))

            conn.commit()
            conn.close()

        except Exception as e:
            if conn:
                conn.close()
            raise e

    # دوال خاصة بالمصروفات
    def add_expense(self, name, item, amount):
        """إضافة مصروف جديد"""
        if not name or not item or not amount:
            raise ValueError("جميع الحقول مطلوبة")

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO expenses (name, item, amount)
                VALUES (?, ?, ?)
            """, (name, item, amount))

            expense_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return expense_id

        except Exception as e:
            if conn:
                conn.close()
            raise e

    def get_expenses(self):
        """الحصول على قائمة المصروفات"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, name, item, amount, date
                FROM expenses
                ORDER BY date DESC
            """)

            expenses = cursor.fetchall()
            conn.close()

            return expenses

        except Exception as e:
            if conn:
                conn.close()
            raise e

    def transfer_to_delegate(self, order_id, delegate_id, representative_name):
        """نقل الطلب إلى المندوب"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الطلب والمندوب
            cursor.execute("SELECT id FROM orders WHERE id = ?", (order_id,))
            if not cursor.fetchone():
                raise ValueError(f"الطلب رقم {order_id} غير موجود")

            cursor.execute("SELECT id FROM delegates WHERE id = ?", (delegate_id,))
            if not cursor.fetchone():
                raise ValueError(f"المندوب رقم {delegate_id} غير موجود")

            # تحديث حالة الطلب أولاً
            cursor.execute("""
                UPDATE orders
                SET status = 'تم النقل للمندوب',
                    delegate_id = ?,
                    delegate_name = ?
                WHERE id = ?
            """, (delegate_id, representative_name, order_id))

            # جلب بيانات الطلب
            cursor.execute("""
                SELECT phone, phone2, carpet_count, total_price, receipt_number, address
                FROM orders WHERE id = ?
            """, (order_id,))
            order_data = cursor.fetchone()

            if order_data:
                phone, phone2, carpet_count, total_price, receipt_number, address = order_data
                phone_number = phone if phone else phone2

                # إضافة الطلب إلى جدول delegate_orders
                cursor.execute("""
                    INSERT INTO delegate_orders (
                        receipt_number, phone_number, carpet_count,
                        total_price, order_id, delegate_id,
                        representative_name, area
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    receipt_number, phone_number, carpet_count,
                    total_price, order_id, delegate_id,
                    representative_name, address
                ))

            conn.commit()
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"خطأ في نقل الطلب: {str(e)}")
            raise e
        finally:
            if conn:
                conn.close()

    def delete_carpet_dimensions(self, order_id):
        """حذف جميع أبعاد السجاد المرتبطة بطلب معين"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الطلب
            if self.db_type == 'mysql':
                cursor.execute("SELECT id FROM orders WHERE id = %s", (order_id,))
            else:
                cursor.execute("SELECT id FROM orders WHERE id = ?", (order_id,))
            if not cursor.fetchone():
                print(f"الطلب رقم {order_id} غير موجود")
                conn.close()
                return False

            # حذف جميع أبعاد السجاد المرتبطة بالطلب
            if self.db_type == 'mysql':
                cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = %s", (order_id,))
            else:
                cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = ?", (order_id,))
            deleted_count = cursor.rowcount

            conn.commit()
            conn.close()

            print(f"تم حذف {deleted_count} من أبعاد السجاد للطلب رقم {order_id}")
            return True

        except Exception as e:
            print(f"خطأ في حذف أبعاد السجاد: {str(e)}")
            if conn:
                conn.rollback()
                conn.close()
            return False

    def add_carpet_dimension(self, order_id, length, width):
        """إضافة بُعد سجادة جديد لطلب معين"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # حساب مساحة السجادة الحالية
            area = float(length) * float(width)

            # إضافة البعد الجديد
            if self.db_type == 'mysql':
                cursor.execute("""
                    INSERT INTO carpet_dimensions (order_id, length, width, total_area)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, length, width, area))
            else:
                cursor.execute("""
                    INSERT INTO carpet_dimensions (order_id, length, width, total_area)
                    VALUES (?, ?, ?, ?)
                """, (order_id, length, width, area))

            # حساب المساحة الإجمالية لكل السجاد في الطلب
            if self.db_type == 'mysql':
                cursor.execute("""
                    SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = %s
                """, (order_id,))
            else:
                cursor.execute("""
                    SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = ?
                """, (order_id,))
            total_area_result = cursor.fetchone()
            total_area = float(total_area_result[0]) if total_area_result and total_area_result[0] is not None else 0.0

            # تقريب المساحة الإجمالية حسب القاعدة المطلوبة
            decimal_part = total_area - int(total_area)
            if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
                total_area = int(total_area) + 1
            else:  # إذا كان الكسر أقل من 0.5
                total_area = int(total_area)

            # جلب سعر المتر المربع وسعر البطانية من الإعدادات
            if self.db_type == 'mysql':
                cursor.execute("SELECT price_per_meter, blanket_price, delivery_price FROM settings LIMIT 1")
            else:
                cursor.execute("SELECT price_per_meter, blanket_price, delivery_price FROM settings LIMIT 1")
            settings = cursor.fetchone()

            price_per_meter = float(settings[0]) if settings and settings[0] is not None else 2000.0
            blanket_price = float(settings[1]) if settings and len(settings) > 1 and settings[1] is not None else 5000.0
            delivery_price = float(settings[2]) if settings and len(settings) > 2 and settings[2] is not None else 10000.0

            # جلب عدد البطانية في الطلب
            if self.db_type == 'mysql':
                cursor.execute("SELECT blanket_count FROM orders WHERE id = %s", (order_id,))
            else:
                cursor.execute("SELECT blanket_count FROM orders WHERE id = ?", (order_id,))
            blanket_result = cursor.fetchone()
            blanket_count = int(blanket_result[0]) if blanket_result and blanket_result[0] is not None else 0

            # حساب السعر الإجمالي
            carpet_price = float(total_area) * float(price_per_meter)  # حساب سعر السجاد بعد تقريب المساحة
            blanket_total_price = float(blanket_count) * float(blanket_price)

            # إضافة رسوم التوصيل فقط إذا كانت المساحة أقل من أو تساوي 12 متر مربع
            delivery_fee = 0.0
            has_delivery = 0
            if total_area <= 12:
                delivery_fee = float(delivery_price)
                has_delivery = 1

            total_price = carpet_price + blanket_total_price + delivery_fee

            # تحديث المساحة الكلية والسعر في جدول الطلبات
            if self.db_type == 'mysql':
                cursor.execute("""
                    UPDATE orders
                    SET total_area = %s,
                        total_price = %s,
                        has_delivery_fee = %s,
                        delivery_fee = %s,
                        transport_fees = %s
                    WHERE id = %s
                """, (total_area, total_price, has_delivery, delivery_fee, delivery_fee, order_id))
            else:
                cursor.execute("""
                    UPDATE orders
                    SET total_area = ?,
                        total_price = ?,
                        has_delivery_fee = ?,
                        transport_fees = ?
                    WHERE id = ?
                """, (total_area, total_price, has_delivery, delivery_fee, order_id))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            print(f"خطأ في إضافة بعد السجادة: {str(e)}")
            raise e
