"""
ملف مؤقت يحتوي على النسخة المحسنة من دالة إدخال أطوال السجاد
"""

def show_carpet_dimensions_dialog(self, row, order_id, carpet_count):
    """عرض نافذة إدخال أبعاد السجاد"""
    dialog = QDialog(self)
    dialog.setWindowTitle("إدخال أبعاد السجاد")
    dialog.setMinimumWidth(500)
    
    layout = QVBoxLayout()
    
    # إضافة تعليمات للمستخدم
    instructions = QLabel("أدخل أبعاد كل سجادة بالمتر. سيتم حساب المساحة والسعر تلقائياً.")
    instructions.setStyleSheet("font-weight: bold; color: #3498db; margin-bottom: 10px;")
    layout.addWidget(instructions)
    
    # إنشاء حقول الإدخال لكل سجادة
    carpet_widgets = []
    for i in range(carpet_count):
        group_box = QGroupBox(f"سجادة رقم {i + 1}")
        group_layout = QGridLayout()
        
        length_label = QLabel("الطول (متر):")
        length_spin = QDoubleSpinBox()
        length_spin.setRange(0.1, 10.0)
        length_spin.setSingleStep(0.1)
        length_spin.setDecimals(2)
        length_spin.setValue(1.0)
        length_spin.setAlignment(Qt.AlignCenter)
        
        width_label = QLabel("العرض (متر):")
        width_spin = QDoubleSpinBox()
        width_spin.setRange(0.1, 10.0)
        width_spin.setSingleStep(0.1)
        width_spin.setDecimals(2)
        width_spin.setValue(1.0)
        width_spin.setAlignment(Qt.AlignCenter)
        
        area_label = QLabel("المساحة (متر²):")
        area_value = QLabel("1.00")
        area_value.setStyleSheet("font-weight: bold; color: #2c3e50;")
        area_value.setAlignment(Qt.AlignCenter)
        
        # إضافة الحقول إلى التخطيط
        group_layout.addWidget(length_label, 0, 0)
        group_layout.addWidget(length_spin, 0, 1)
        group_layout.addWidget(width_label, 0, 2)
        group_layout.addWidget(width_spin, 0, 3)
        group_layout.addWidget(area_label, 1, 0)
        group_layout.addWidget(area_value, 1, 1, 1, 3)
        
        # ربط تغيير القيم بحساب المساحة
        def update_area(l_spin=length_spin, w_spin=width_spin, area_lbl=area_value):
            # حساب المساحة الفردية فقط بدون تقريب
            area = l_spin.value() * w_spin.value()
            area_lbl.setText(f"{area:.2f}")
        
        length_spin.valueChanged.connect(update_area)
        width_spin.valueChanged.connect(update_area)
        
        group_box.setLayout(group_layout)
        layout.addWidget(group_box)
        
        carpet_widgets.append((length_spin, width_spin, area_value))
    
    # إضافة معلومات السعر
    price_info = QWidget()
    price_layout = QHBoxLayout(price_info)
    
    # الحصول على سعر المتر
    settings = self.db.fetch_one("SELECT price_per_meter FROM settings")
    price_per_meter = settings[0] if settings else 0
    
    price_per_meter_label = QLabel(f"سعر المتر المربع: {price_per_meter:,.0f} دينار")
    price_per_meter_label.setStyleSheet("font-weight: bold; color: #27ae60;")
    
    total_price_label = QLabel("السعر الإجمالي التقديري:")
    self.total_price_value = QLabel(f"{carpet_count * price_per_meter:,.0f} دينار")
    self.total_price_value.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
    
    price_layout.addWidget(price_per_meter_label)
    price_layout.addStretch()
    price_layout.addWidget(total_price_label)
    price_layout.addWidget(self.total_price_value)
    
    layout.addWidget(price_info)
    
    # إضافة زر لحساب السعر الإجمالي
    calc_btn = QPushButton("حساب السعر الإجمالي")
    calc_btn.setStyleSheet(get_button_style(COLORS['info']))
    calc_btn.clicked.connect(lambda: self.calculate_total_price(carpet_widgets, price_per_meter))
    layout.addWidget(calc_btn)
    
    # أزرار الحفظ والإلغاء
    buttons = QHBoxLayout()
    save_btn = QPushButton("حفظ")
    save_btn.setStyleSheet(get_button_style(COLORS['success']))
    cancel_btn = QPushButton("إلغاء")
    cancel_btn.setStyleSheet(get_button_style(COLORS['danger']))
    
    buttons.addWidget(save_btn)
    buttons.addWidget(cancel_btn)
    layout.addLayout(buttons)
    
    dialog.setLayout(layout)
    
    # ربط الأحداث
    save_btn.clicked.connect(lambda: self.save_carpet_dimensions(dialog, row, order_id, carpet_widgets))
    cancel_btn.clicked.connect(dialog.reject)
    
    dialog.exec()

def calculate_total_price(self, carpet_widgets, price_per_meter):
    """حساب السعر الإجمالي للسجاد"""
    total_area = 0
    # جمع كل المساحات الفردية
    for length_spin, width_spin, _ in carpet_widgets:
        length = length_spin.value()
        width = width_spin.value()
        area = length * width
        total_area += area
    
    # تقريب المساحة الإجمالية حسب القاعدة المطلوبة
    decimal_part = total_area - int(total_area)
    if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
        total_area = int(total_area) + 1
    else:  # إذا كان الكسر أقل من 0.5
        total_area = int(total_area)
    
    # الحصول على الأسعار من جدول الإعدادات
    settings = self.db.fetch_one("SELECT price_per_meter, blanket_price, delivery_price FROM settings ORDER BY id DESC LIMIT 1")
    if not settings:
        raise ValueError("لم يتم العثور على إعدادات الأسعار")
        
    price_per_meter = settings[0]
    blanket_price = settings[1]
    delivery_price = settings[2]
    
    # حساب سعر السجاد
    carpet_price = total_area * price_per_meter
    # إضافة رسوم التوصيل إذا كانت المساحة 12 متر مربع أو أقل
    delivery_fee = delivery_price if total_area <= 12 else 0
    
    # تحديث العرض
    self.total_area_label.setText(f"المساحة الكلية (بعد التقريب): {total_area:.2f} متر²")
    self.total_price_value.setText(f"سعر السجاد: {carpet_price:,.0f} دينار")
    if delivery_fee > 0:
        self.total_price_value.setText(f"{self.total_price_value.text()} + أجور نقل: {delivery_fee:,.0f} دينار")

def save_carpet_dimensions(self, dialog, row, order_id, carpet_widgets):
    """حفظ أبعاد السجاد في قاعدة البيانات"""
    try:
        # التحقق من وجود الطلب
        order = self.db.fetch_one("SELECT id FROM orders WHERE id = ?", (order_id,))
        if not order:
            raise ValueError("الطلب غير موجود")

        # الحصول على سعر المتر
        settings = self.db.fetch_one("SELECT price_per_meter FROM settings")
        if not settings:
            raise ValueError("لم يتم العثور على سعر المتر في الإعدادات")
        price_per_meter = settings[0]

        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # بدء المعاملة
            cursor.execute("BEGIN TRANSACTION")
            
            # حذف الأبعاد القديمة
            cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = ?", (order_id,))
            
            # حساب المساحة الكلية أولاً
            total_area = 0
            for length_spin, width_spin, _ in carpet_widgets:
                length = length_spin.value()
                width = width_spin.value()
                if length > 0 and width > 0:
                    area = length * width
                    total_area += area
            
            # تقريب المساحة الكلية حسب القاعدة المطلوبة
            decimal_part = total_area - int(total_area)
            if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
                total_area = int(total_area) + 1
            else:  # إذا كان الكسر أقل من 0.5
                total_area = int(total_area)
            
            # إضافة الأبعاد الجديدة
            dimensions_added = False
            
            for length_spin, width_spin, _ in carpet_widgets:
                try:
                    # الحصول على القيم من الحقول
                    length = length_spin.value()
                    width = width_spin.value()
                    
                    if length <= 0 or width <= 0:
                        continue
                    
                    # حساب مساحة السجادة
                    area = length * width
                    
                    # إضافة الأبعاد إلى قاعدة البيانات
                    cursor.execute(
                        """
                        INSERT INTO carpet_dimensions (order_id, length, width, total_area)
                        VALUES (?, ?, ?, ?)
                        """,
                        (order_id, length, width, area)
                    )
                    dimensions_added = True
                    
                except Exception as e:
                    conn.rollback()
                    raise ValueError(f"خطأ في حفظ الأبعاد: {str(e)}")
            
            if not dimensions_added:
                raise ValueError("لم يتم إدخال أي أبعاد صحيحة للسجاد")
            
            # حساب وتحديث السعر الإجمالي بعد التقريب
            total_price = total_area * price_per_meter
            
            # تحديث المساحة والسعر في جدول الطلبات
            cursor.execute("""
                UPDATE orders 
                SET total_area = ?, total_price = ?
                WHERE id = ?
            """, (total_area, total_price, order_id))
            
            # تحديث السعر الإجمالي في الجدول
            price_item = QTableWidgetItem(f"{total_price:,.0f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            price_item.setFlags(price_item.flags() & ~Qt.ItemIsEditable)
            self.orders_table.setItem(row, 6, price_item)
            
            # تنفيذ المعاملة
            conn.commit()
            
            # إغلاق مربع الحوار
            dialog.accept()
            QMessageBox.information(self, "نجاح", "تم حفظ أبعاد السجاد بنجاح")
            
        except Exception as e:
            conn.rollback()
            raise ValueError(f"خطأ في حفظ أبعاد السجاد: {str(e)}")
            
    except Exception as e:
        QMessageBox.critical(self, "خطأ", str(e))
