import sqlite3

def reorder_table():
    try:
        conn = sqlite3.connect('carpet_cleaning.db')
        cursor = conn.cursor()
        
        # إنشاء جدول جديد بالترتيب الصحيح
        cursor.execute("""
            CREATE TABLE orders_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                phone TEXT NOT NULL,
                address TEXT NOT NULL,
                carpet_count INTEGER DEFAULT 0 NOT NULL,
                carpet_type TEXT,
                area REAL,
                price REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'جديد'
            )
        """)
        
        # نقل البيانات إلى الجدول الجديد
        cursor.execute("""
            INSERT INTO orders_new (
                id, customer_name, phone, address, carpet_type,
                area, price
            )
            SELECT id, customer_name, phone, address, carpet_type,
                   area, price
            FROM orders
        """)
        
        # حذف الجدول القديم
        cursor.execute("DROP TABLE orders")
        
        # إعادة تسمية الجدول الجديد
        cursor.execute("ALTER TABLE orders_new RENAME TO orders")
        
        conn.commit()
        print("Table reordered successfully")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    reorder_table()
