from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QComboBox, QPushButton, QTableWidget, QAbstractItemView,
                           QTableWidgetItem, QWidget, QMessageBox, QStyledItemDelegate, QCheckBox,
                           QHeaderView, QRadioButton, QFormLayout, QDialogButtonBox, QSpacerItem,
                           QSizePolicy, QInputDialog)
from PyQt5.QtCore import Qt, QObject, QEvent
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWidgets import QApplication
from db.models import Database
from ui.styles import get_button_style, COLORS, TABLE_STYLE, SEARCH_STYLE

class OrdersWindow(QDialog):
    """نافذة عرض الطلبات الواردة"""

    def eventFilter(self, obj, event):
        """معالج الأحداث المخصص للتعامل مع أحداث حقل البحث والجدول"""
        # معالجة أحداث حقل البحث
        if obj == self.search_input and event.type() == QEvent.KeyPress:
            # معالجة مفاتيح الأسهم للتنقل بين الصفوف
            if event.key() == Qt.Key_Up or event.key() == Qt.Key_Down:
                # تمرير الحدث إلى الجدول
                self.orders_table.setFocus()
                self.keyPressEvent(event)
                return True
            # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
            elif event.key() == Qt.Key_Space:
                # الحصول على الصف الحالي
                current_row = self.orders_table.currentRow()
                if current_row >= 0 and not self.orders_table.isRowHidden(current_row):
                    # الحصول على مربع التحديد وتحديث حالته
                    checkbox_container = self.orders_table.cellWidget(current_row, 0)
                    if checkbox_container:
                        checkbox = checkbox_container.findChild(QCheckBox)
                        if checkbox:
                            # تبديل حالة مربع الاختيار
                            checkbox.setChecked(not checkbox.isChecked())
                            # تحديث عرض الجدول
                            self.orders_table.viewport().update()
                            # إعادة التركيز إلى حقل البحث
                            self.search_input.setFocus()
                            return True

        # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
        return super().eventFilter(obj, event)

    def setup_table_event_filter(self):
        """تثبيت مرشح الأحداث على جدول الطلبات لمعالجة أحداث المفاتيح بشكل صحيح."""
        # إنشاء مرشح أحداث مخصص للجدول
        class TableEventFilter(QObject):
            def eventFilter(self_filter, _, event):
                # معالجة أحداث المفاتيح فقط
                if event.type() == QEvent.KeyPress:
                    # الحصول على الصف الحالي
                    current_row = self.orders_table.currentRow()

                    # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
                    if event.key() == Qt.Key_Space and current_row >= 0:
                        # تأكد من أن الصف غير مخفي
                        if not self.orders_table.isRowHidden(current_row):
                            # الحصول على مربع التحديد وتحديث حالته
                            checkbox_container = self.orders_table.cellWidget(current_row, 0)
                            if checkbox_container:
                                checkbox = checkbox_container.findChild(QCheckBox)
                                if checkbox:
                                    # تبديل حالة مربع الاختيار
                                    checkbox.setChecked(not checkbox.isChecked())
                                    # تحديث عرض الجدول
                                    self.orders_table.viewport().update()
                                    # منع تمرير الحدث للفئة الأساسية
                                    return True

                    # معالجة مفاتيح الأسهم للتنقل بين الصفوف
                    elif event.key() == Qt.Key_Up:
                        # التنقل للصف السابق غير المخفي
                        row = current_row - 1
                        while row >= 0:
                            if not self.orders_table.isRowHidden(row):
                                self.orders_table.setCurrentCell(row, 0)
                                self.current_row = row
                                return True
                            row -= 1

                    elif event.key() == Qt.Key_Down:
                        # التنقل للصف التالي غير المخفي
                        row = current_row + 1
                        while row < self.orders_table.rowCount():
                            if not self.orders_table.isRowHidden(row):
                                self.orders_table.setCurrentCell(row, 0)
                                self.current_row = row
                                return True
                            row += 1

                # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
                return False

        # إنشاء مرشح الأحداث وتثبيته على الجدول
        self.table_event_filter = TableEventFilter()
        self.orders_table.installEventFilter(self.table_event_filter)

    def __init__(self, parent=None):
        super().__init__(parent)

        # تعيين عنوان النافذة وحجمها
        self.setWindowTitle("الطلبات الواردة")
        self.resize(1030, 690)

        # تتبع الصف الحالي للتنقل
        self.current_row = -1

        try:
            self.db = Database()
        except Exception:
            QMessageBox.critical(self, "خطأ", "فشل الاتصال بقاعدة البيانات")
            return

        # إنشاء العناصر
        self.orders_table = QTableWidget()
        self.search_input = QLineEdit()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تحميل البيانات
        self.load_orders()

        # تعيين التركيز على الجدول وتحديد الصف الأول إن وجد
        self.orders_table.setFocus()
        if self.orders_table.rowCount() > 0:
            self.orders_table.setCurrentCell(0, 0)
            self.current_row = 0

        # تثبيت مرشح الأحداث للجدول
        self.setup_table_event_filter()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # إضافة حقل البحث وأزرار التحكم
        controls_layout = QHBoxLayout()

        # إضافة حقل البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setStyleSheet("font-size: 12pt; font-weight: bold;")
        self.search_input.setPlaceholderText("ابحث في الطلبات...")
        self.search_input.textChanged.connect(self.filter_orders)
        self.search_input.setStyleSheet(SEARCH_STYLE)

        # إضافة معالج أحداث خاص لحقل البحث
        self.search_input.installEventFilter(self)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        controls_layout.addLayout(search_layout)

        # إضافة فاصل مرن
        controls_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # تمكين تتبع مفاتيح لوحة المفاتيح
        self.setFocusPolicy(Qt.StrongFocus)

        # إضافة زر نقل الطلبات إلى المندوب
        self.transfer_to_delegate_btn = QPushButton("نقل للمندوب")
        self.transfer_to_delegate_btn.setToolTip("نقل الطلبات المحددة إلى المندوب")
        self.transfer_to_delegate_btn.setStyleSheet(get_button_style(COLORS['primary']))
        self.transfer_to_delegate_btn.clicked.connect(self.transfer_to_delegate)
        controls_layout.addWidget(self.transfer_to_delegate_btn)

        layout.addLayout(controls_layout)

        # إعداد جدول الطلبات
        self.orders_table.setStyleSheet(TABLE_STYLE)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setFocusPolicy(Qt.StrongFocus)
        headers = [
            "تحديد",
            "رقم الطلب",
            "رقم الهاتف",
            "العنوان",
            "السعر الكلي",
            "ملحوظات",
            "الإجراءات"
        ]

        self.orders_table.setColumnCount(len(headers))
        self.orders_table.setHorizontalHeaderLabels(headers)

        # تعيين عرض الأعمدة
        self.orders_table.setColumnWidth(0, 40)   # تحديد
        self.orders_table.setColumnWidth(1, 0)    # رقم الطلب - مخفي
        self.orders_table.setColumnWidth(2, 120)  # رقم الهاتف
        self.orders_table.setColumnWidth(3, 250)  # العنوان
        self.orders_table.setColumnWidth(4, 120)  # السعر الكلي
        self.orders_table.setColumnWidth(5, 220)  # ملحوظات
        self.orders_table.setColumnWidth(6, 160)  # الإجراءات

        # تعيين ارتفاع الصفوف
        self.orders_table.verticalHeader().setDefaultSectionSize(45)
        self.orders_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)

        # تنسيق رأس الجدول
        header = self.orders_table.horizontalHeader()
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #273c75;
                padding: 8px;
                border: none;
                color: white;
                font-weight: bold;
                font-size: 10pt;
            }
        """)

        # إخفاء عمود رقم الطلب
        self.orders_table.hideColumn(1)

        layout.addWidget(self.orders_table)
        self.setLayout(layout)

    def load_orders(self):
        """تحميل الطلبات الواردة"""
        try:
            if not hasattr(self, 'db'):
                print("لا يوجد اتصال بقاعدة البيانات")
                return

            # إعادة تعيين الصف الحالي
            self.current_row = -1

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # استعلام للحصول على الطلبات الجديدة فقط (استبعاد الطلبات في المخزن وطلبات المندوبين وطلبات التوزيع والمكتملة)
            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT o.id, o.phone, o.phone2, o.address,
                           COALESCE(o.total_price, 0) as total_price,
                           o.notes
                    FROM orders o
                    WHERE (o.status = 'جديد' OR o.status IS NULL)
                    OR (o.status != 'تم النقل للمندوب' AND o.status != 'warehouse' AND o.status != 'distribution'
                    AND o.status != 'completed' AND o.status != 'مكتمل' AND o.status != 'واصل')
                    ORDER BY o.address ASC
                """)
            else:
                cursor.execute("""
                    SELECT o.id, o.phone, o.phone2, o.address,
                           COALESCE((
                               SELECT SUM(cd.total_area * s.price_per_meter)
                               FROM carpet_dimensions cd
                               CROSS JOIN settings s
                               WHERE cd.order_id = o.id
                           ), 0) as total_price,
                           o.notes
                    FROM orders o
                    WHERE (o.status = 'جديد' OR o.status IS NULL)
                    OR (o.status != 'تم النقل للمندوب' AND o.status != 'warehouse' AND o.status != 'distribution'
                    AND o.status != 'completed' AND o.status != 'مكتمل' AND o.status != 'واصل')
                    ORDER BY o.address ASC
                """)

            self.orders_table.setRowCount(0)
            for row_data in cursor.fetchall():
                row = self.orders_table.rowCount()
                self.orders_table.insertRow(row)

                # إضافة مربع تحديد
                checkbox = QCheckBox()
                checkbox_container = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_container)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                self.orders_table.setCellWidget(row, 0, checkbox_container)

                # إضافة بيانات الطلب
                order_id = row_data[0]  # تخزين معرف الطلب
                id_item = QTableWidgetItem(str(order_id))
                self.orders_table.setItem(row, 1, id_item)

                # إنشاء widget لعرض أرقام الهاتف بشكل عامودي
                phone_widget = QWidget()
                phone_layout = QVBoxLayout(phone_widget)
                phone_layout.setContentsMargins(2, 2, 2, 2)
                phone_layout.setSpacing(2)

                # إضافة الهاتف الأساسي
                phone1 = str(row_data[1]) if row_data[1] else ""
                if phone1:
                    phone1_label = QLabel(phone1)
                    phone1_label.setAlignment(Qt.AlignCenter)
                    phone1_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone1_label)

                # إضافة الهاتف الثاني إذا وجد
                phone2 = str(row_data[2]) if row_data[2] else ""
                if phone2:
                    phone2_label = QLabel(phone2)
                    phone2_label.setAlignment(Qt.AlignCenter)
                    phone2_label.setFont(QFont("Arial", 9))
                    phone_layout.addWidget(phone2_label)
                    # تعديل ارتفاع الصف ليناسب الرقمين
                    self.orders_table.setRowHeight(row, 60)
                else:
                    # ارتفاع طبيعي للصف في حالة رقم واحد
                    self.orders_table.setRowHeight(row, 40)

                self.orders_table.setCellWidget(row, 2, phone_widget)

                address_item = QTableWidgetItem(str(row_data[3]))
                address_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 3, address_item)

                price_item = QTableWidgetItem(self.format_price(row_data[4]))
                price_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 4, price_item)

                notes_item = QTableWidgetItem(str(row_data[5]) if row_data[5] else "")
                notes_item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, 5, notes_item)

                # إضافة أزرار الإجراءات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(2, 2, 2, 2)
                actions_layout.setSpacing(5)

                # زر الحذف
                delete_btn = QPushButton("حذف")
                delete_btn.setToolTip("حذف الطلب")
                delete_btn.setFixedHeight(25)
                delete_btn.setStyleSheet(get_button_style(COLORS['danger']))
                delete_btn.clicked.connect(lambda _, oid=order_id: self.delete_order_by_id(oid))
                actions_layout.addWidget(delete_btn)

                self.orders_table.setCellWidget(row, 6, actions_widget)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "فشل في تحميل الطلبات")

    def get_selected_orders(self):
        """الحصول على قائمة معرفات الطلبات المحددة"""
        selected_orders = []
        for row in range(self.orders_table.rowCount()):
            if not self.orders_table.isRowHidden(row):
                checkbox_container = self.orders_table.cellWidget(row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        order_id = int(self.orders_table.item(row, 1).text())
                        if order_id not in selected_orders:
                            selected_orders.append(order_id)
        return selected_orders

    def format_number(self, number):
        """تنسيق الأرقام بإضافة فواصل كل 3 أرقام"""
        try:
            # تحويل الرقم إلى عدد صحيح وإزالة الكسور
            num = int(float(number))
            # تحويل إلى نص وإضافة الفواصل
            return "{:,}".format(num)
        except:
            return str(number)

    def format_price(self, price):
        """تنسيق السعر بإزالة الأصفار والنقطة"""
        # تحويل السعر إلى رقم صحيح
        price = int(float(price))
        # تنسيق الرقم بإضافة فواصل
        return "{:,}".format(price)

    def delete_order_by_id(self, order_id):
        """حذف طلب بواسطة المعرف"""
        try:
            # عرض رسالة تأكيد قبل الحذف
            reply = QMessageBox.question(
                self,
                'تأكيد الحذف',
                'هل أنت متأكد من حذف هذا الطلب؟',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # حذف أبعاد السجاد المرتبطة بالطلب أولاً
                if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                    cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = %s", (order_id,))
                    # حذف الطلب
                    cursor.execute("DELETE FROM orders WHERE id = %s", (order_id,))
                else:
                    cursor.execute("DELETE FROM carpet_dimensions WHERE order_id = ?", (order_id,))
                    # حذف الطلب
                    cursor.execute("DELETE FROM orders WHERE id = ?", (order_id,))

                conn.commit()
                conn.close()

                # تحديث الجدول
                self.load_orders()

                # إعادة تطبيق التصفية
                if self.search_input.text().strip():
                    self.filter_orders()

                QMessageBox.information(self, "نجاح", "تم حذف الطلب بنجاح")

        except Exception as e:
            print(f"خطأ في حذف الطلب: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف الطلب")

    def keyPressEvent(self, event):
        # إذا كان التركيز على حقل البحث، نتعامل مع مفتاح المسافة بشكل خاص
        if self.search_input.hasFocus() and event.key() == Qt.Key_Space:
            # الحصول على الصف الحالي المحدد في الجدول
            current_row = self.orders_table.currentRow()
            if current_row >= 0 and not self.orders_table.isRowHidden(current_row):
                # الحصول على مربع التحديد وتحديث حالته
                checkbox_container = self.orders_table.cellWidget(current_row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox:
                        # تبديل حالة مربع الاختيار
                        checkbox.setChecked(not checkbox.isChecked())
                        # تحديث عرض الجدول
                        self.orders_table.viewport().update()
                        # منع تمرير الحدث للفئة الأساسية
                        event.accept()
                        return
            else:
                # إذا لم يكن هناك صف محدد، نمرر الحدث للفئة الأساسية
                super().keyPressEvent(event)
                return
        # إذا كان التركيز على حقل البحث ولكن ليس مفتاح المسافة، نمرر الحدث للفئة الأساسية
        elif self.search_input.hasFocus():
            super().keyPressEvent(event)
            return

        # إذا لم يكن التركيز على الجدول، ننقل التركيز إليه
        if not self.orders_table.hasFocus():
            self.orders_table.setFocus()

        # الحصول على الصف الحالي
        current_row = self.orders_table.currentRow()

        # إذا لم يتم تحديد أي صف، نحدد الصف الأول غير المخفي
        if current_row < 0 and event.key() in [Qt.Key_Up, Qt.Key_Down, Qt.Key_Space]:
            for row in range(self.orders_table.rowCount()):
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    current_row = row
                    self.current_row = row
                    break

        # معالجة مفتاح المسافة لتحديد/إلغاء تحديد الصف الحالي
        if event.key() == Qt.Key_Space and current_row >= 0:
            # تأكد من أن الصف غير مخفي
            if not self.orders_table.isRowHidden(current_row):
                # الحصول على مربع التحديد وتحديث حالته
                checkbox_container = self.orders_table.cellWidget(current_row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox:
                        # تبديل حالة مربع الاختيار
                        checkbox.setChecked(not checkbox.isChecked())
                        # تحديث عرض الجدول
                        self.orders_table.viewport().update()
                        # منع تمرير الحدث للفئة الأساسية
                        event.accept()
                        return

        # معالجة مفاتيح الأسهم للتنقل بين الصفوف
        elif event.key() == Qt.Key_Up:
            # التنقل للصف السابق غير المخفي
            row = current_row - 1
            while row >= 0:
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    self.current_row = row
                    # تمرير التركيز إلى الجدول
                    self.orders_table.setFocus()
                    # تحديث عرض الجدول
                    self.orders_table.viewport().update()
                    event.accept()
                    return
                row -= 1

        elif event.key() == Qt.Key_Down:
            # التنقل للصف التالي غير المخفي
            row = current_row + 1
            while row < self.orders_table.rowCount():
                if not self.orders_table.isRowHidden(row):
                    self.orders_table.setCurrentCell(row, 0)
                    self.current_row = row
                    # تمرير التركيز إلى الجدول
                    self.orders_table.setFocus()
                    # تحديث عرض الجدول
                    self.orders_table.viewport().update()
                    event.accept()
                    return
                row += 1

        # تمرير الحدث للفئة الأساسية إذا لم تتم معالجته
        super().keyPressEvent(event)

    def filter_orders(self, text=None):
        """تصفية الطلبات حسب النص المدخل"""
        try:
            # استخدام النص من حقل البحث إذا لم يتم تمرير نص
            search_text = text if text is not None else self.search_input.text()
            search_text = search_text.strip()  # إزالة المسافات

            if not search_text:  # إذا كان حقل البحث فارغاً، عرض جميع الطلبات
                for row in range(self.orders_table.rowCount()):
                    self.orders_table.setRowHidden(row, False)
                return

            for row in range(self.orders_table.rowCount()):
                show_row = False

                # معالجة خاصة لعمود الهاتف
                phone_widget = self.orders_table.cellWidget(row, 2)
                if phone_widget:
                    # البحث في كل أرقام الهواتف الموجودة في الخلية
                    phone_labels = phone_widget.findChildren(QLabel)
                    for label in phone_labels:
                        phone_number = label.text().strip()
                        # البحث في رقم الهاتف
                        if (search_text in phone_number or  # البحث في أي مكان
                            phone_number.startswith(search_text) or  # البحث من البداية
                            phone_number.endswith(search_text)):  # البحث من النهاية
                            show_row = True
                            break

                # البحث في باقي الأعمدة إذا لم يتم العثور على تطابق في الهاتف
                if not show_row:
                    for col in [3, 4, 5]:  # العنوان، السعر، الملاحظات
                        item = self.orders_table.item(row, col)
                        if item and search_text.lower() in item.text().lower():
                            show_row = True
                            break

                self.orders_table.setRowHidden(row, not show_row)
        except Exception as e:
            print(f"خطأ في تصفية الطلبات: {str(e)}")

    def transfer_to_delegate(self):
        # نقل الطلبات المحددة إلى المندوب
        try:
            # الحصول على أرقام الطلبات المحددة
            selected_orders = []
            for row in range(self.orders_table.rowCount()):
                checkbox = self.orders_table.cellWidget(row, 0).findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    order_id = int(self.orders_table.item(row, 1).text())
                    selected_orders.append(order_id)

            if not selected_orders:
                QMessageBox.warning(self, "تحذير", "لم يتم تحديد أي طلبات")
                return

            # فتح مربع حوار بسيط لإدخال اسم المندوب الجديد
            dialog = QDialog(self)
            dialog.setWindowTitle("إدخال اسم المندوب")
            dialog.setMinimumWidth(350)
            dialog.setLayoutDirection(Qt.RightToLeft)  # ضبط اتجاه الواجهة من اليمين لليسار

            layout = QVBoxLayout(dialog)

            # إضافة نص توضيحي
            instruction_label = QLabel("أدخل اسم المندوب الذي تريد نقل الطلبات إليه:")
            instruction_label.setStyleSheet("font-weight: bold;")
            layout.addWidget(instruction_label)

            # حقل إدخال اسم المندوب الجديد
            delegate_name_input = QLineEdit()
            delegate_name_input.setPlaceholderText("اسم المندوب")
            delegate_name_input.setStyleSheet('QLineEdit { padding: 8px; border: 1px solid #dcdde1; border-radius: 5px; font-size: 11pt; margin: 5px 0px; }')
            layout.addWidget(delegate_name_input)

            # أزرار الموافقة والإلغاء
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            buttons.button(QDialogButtonBox.Ok).setText("موافق")
            buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
            layout.addWidget(buttons)

            # تنفيذ الإجراء عند قبول الحوار
            if dialog.exec_() == QDialog.Accepted:
                representative_name = delegate_name_input.text().strip()

                # التحقق من إدخال اسم المندوب
                if not representative_name:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المندوب")
                    return

                # نقل الطلبات إلى نافذة استلام المندوبين باستخدام اسم المندوب الجديد
                try:
                    import time
                    # تم إزالة import sqlite3 - نستخدم MySQL فقط

                    # عدد المحاولات
                    max_retries = 5
                    retry_count = 0

                    while retry_count < max_retries:
                        try:
                            conn = self.db.get_connection()
                            cursor = conn.cursor()

                            # بدء معاملة SQLite
                            try:
                                # التحقق وإضافة المندوب الجديد إلى جدول delegates و representatives إذا لم يكن موجوداً
                                if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                                    # إضافة إلى جدول delegates
                                    cursor.execute("SELECT id FROM delegates WHERE name = %s", (representative_name,))
                                    if not cursor.fetchone():
                                        cursor.execute("INSERT INTO delegates (name) VALUES (%s)", (representative_name,))
                                        print(f"تم إضافة المندوب الجديد إلى delegates: {representative_name}")

                                    # إضافة إلى جدول representatives
                                    cursor.execute("SELECT id FROM representatives WHERE name = %s", (representative_name,))
                                    if not cursor.fetchone():
                                        cursor.execute("INSERT INTO representatives (name, phone) VALUES (%s, %s)", (representative_name, ''))
                                        print(f"تم إضافة المندوب الجديد إلى representatives: {representative_name}")

                                    conn.commit()  # تنفيذ إضافة المندوب فوراً
                                else:
                                    # إضافة إلى جدول delegates
                                    cursor.execute("SELECT id FROM delegates WHERE name = ?", (representative_name,))
                                    if not cursor.fetchone():
                                        cursor.execute("INSERT INTO delegates (name) VALUES (?)", (representative_name,))
                                        print(f"تم إضافة المندوب الجديد إلى delegates: {representative_name}")

                                    # إضافة إلى جدول representatives
                                    cursor.execute("SELECT id FROM representatives WHERE name = ?", (representative_name,))
                                    if not cursor.fetchone():
                                        cursor.execute("INSERT INTO representatives (name, phone) VALUES (?, ?)", (representative_name, ''))
                                        print(f"تم إضافة المندوب الجديد إلى representatives: {representative_name}")

                                    conn.commit()  # تنفيذ إضافة المندوب فوراً

                                # نقل الطلبات إلى جدول delegate_orders
                                for order_id in selected_orders:
                                    # الحصول على بيانات الطلب من جدول orders
                                    if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                                        cursor.execute("SELECT id, phone, total_price, receipt_number, phone2, address FROM orders WHERE id = %s", (order_id,))
                                    else:
                                        cursor.execute("SELECT id, phone, total_price, receipt_number, phone2, address FROM orders WHERE id = ?", (order_id,))
                                    order_data = cursor.fetchone()

                                    if order_data:
                                        # إضافة الطلب إلى جدول delegate_orders
                                        _, phone, total_price, receipt_number, phone2, address = order_data
                                        phone_number = phone if phone else (phone2 if phone2 else "")

                                        # الحصول على معرف المندوب
                                        if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                                            cursor.execute("SELECT id FROM delegates WHERE name = %s LIMIT 1", (representative_name,))
                                        else:
                                            cursor.execute("SELECT id FROM delegates WHERE name = ? LIMIT 1", (representative_name,))
                                        delegate_result = cursor.fetchone()
                                        delegate_id = delegate_result[0] if delegate_result else None

                                        # إضافة الطلب إلى جدول delegate_orders مع جميع الحقول المطلوبة
                                        if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                                            cursor.execute("""INSERT INTO delegate_orders (
                                                receipt_number, phone_number, total_price,
                                                representative_name, order_id, delegate_id, area
                                            ) VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                                                        (receipt_number or "", phone_number or "", total_price or 0,
                                                        representative_name or "", order_id,
                                                        delegate_id, address or ""))

                                            # تحديث حالة الطلب في جدول orders إلى 'distribution' ليظهر في قائمة استلام المندوبين
                                            cursor.execute("UPDATE orders SET status = 'distribution', delegate_name = %s, delegate_id = %s WHERE id = %s",
                                                        (representative_name, delegate_id, order_id))
                                        else:
                                            cursor.execute("""INSERT INTO delegate_orders (
                                                receipt_number, phone_number, total_price,
                                                representative_name, order_id, delegate_id, area
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?)""",
                                                        (receipt_number or "", phone_number or "", total_price or 0,
                                                        representative_name or "", order_id,
                                                        delegate_id, address or ""))

                                            # تحديث حالة الطلب في جدول orders إلى 'distribution' ليظهر في قائمة استلام المندوبين
                                            cursor.execute("UPDATE orders SET status = 'distribution', delegate_name = ?, delegate_id = ? WHERE id = ?",
                                                        (representative_name, delegate_id, order_id))

                                # إتمام العملية
                                conn.commit()

                            except Exception as tx_error:
                                # إلغاء العملية في حالة الخطأ
                                conn.rollback()
                                raise tx_error
                            finally:
                                # إغلاق الاتصال بعد الانتهاء
                                conn.close()

                            # تحديث جدول الطلبات بعد نقلها
                            self.load_orders()

                            QMessageBox.information(self, "نجاح", f"تم نقل {len(selected_orders)} طلب إلى المندوب {representative_name}")

                            # تم تنفيذ العملية بنجاح، الخروج من الحلقة
                            break

                        except Exception as e:
                            if "database is locked" in str(e) and retry_count < max_retries - 1:
                                print(f"محاولة {retry_count + 1}: قاعدة البيانات مقفلة، إعادة المحاولة بعد {(retry_count + 1) * 0.5} ثانية...")
                                retry_count += 1
                                # إغلاق الاتصال قبل إعادة المحاولة
                                if 'conn' in locals() and conn:
                                    try:
                                        conn.rollback()
                                        conn.close()
                                    except:
                                        pass
                                # انتظار قبل إعادة المحاولة مع زيادة الوقت تدريجياً
                                time.sleep((retry_count) * 0.5)
                            else:
                                # خطأ غير متوقع أو وصلنا للحد الأقصى من المحاولات
                                raise e

                    # إذا وصلنا للحد الأقصى من المحاولات دون نجاح
                    if retry_count >= max_retries:
                        raise Exception("فشل في نقل الطلبات بعد عدة محاولات، قاعدة البيانات مشغولة.")

                except Exception as e:
                    # إغلاق الاتصال في حالة الخطأ إذا كان مفتوحاً
                    if 'conn' in locals() and conn:
                        try:
                            conn.rollback()
                            conn.close()
                        except:
                            pass

                    print(f"خطأ في نقل الطلبات: {str(e)}")
                    QMessageBox.critical(self, "خطأ", f"خطأ في نقل الطلبات: {str(e)}")

        except Exception as e:
            print(f"خطأ في نقل الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء نقل الطلبات")
