from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                           QMessageBox, QSpinBox, QDoubleSpinBox, QDateEdit,
                           QFormLayout, QWidget, QHeaderView, QComboBox, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QTimer, QLocale
from PyQt5.QtGui import QColor, QDoubleValidator
from db.models import Database
from ui.styles import *
import datetime

class BaseDialog(QDialog):
    """الفئة الأساسية لجميع النوافذ"""
    def __init__(self, parent=None, title=""):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.db = Database()
        self.setup_ui()
        self.setStyleSheet(WINDOW_STYLE)

    def setup_ui(self):
        """تهيئة واجهة المستخدم"""
        self.layout = QVBoxLayout(self)

        # إضافة العنوان
        self.title_container = QWidget()
        self.title_container.setStyleSheet(TITLE_CONTAINER_STYLE)
        title_layout = QVBoxLayout(self.title_container)

        self.title_label = QLabel(self.windowTitle())
        self.title_label.setStyleSheet(TITLE_STYLE)
        self.title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.title_label)

        self.layout.addWidget(self.title_container)

class OrderForm(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = Database()
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("إدخال طلب جديد")
        self.setStyleSheet(WINDOW_STYLE)
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # عنوان النافذة
        title_widget = QWidget()
        title_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2980b9, stop:1 #3498db);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_widget)
        title_label = QLabel("إدخال طلب جديد")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)
        layout.addWidget(title_widget)

        # نموذج إدخال البيانات
        form_widget = QWidget()
        form_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(20)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # حقول الإدخال
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("أدخل رقم الهاتف")

        self.phone2 = QLineEdit()
        self.phone2.setPlaceholderText("أدخل رقم الهاتف البديل (اختياري)")

        self.address = QLineEdit()
        self.address.setPlaceholderText("أدخل العنوان")

        self.notes = QLineEdit()
        self.notes.setPlaceholderText("ملاحظات إضافية (اختياري)")

        # إضافة الحقول إلى النموذج
        form_layout.addRow("رقم الهاتف: *", self.phone)
        form_layout.addRow("رقم الهاتف البديل:", self.phone2)
        form_layout.addRow("العنوان: *", self.address)
        form_layout.addRow("ملاحظات إضافية:", self.notes)

        layout.addWidget(form_widget)

        # إضافة ملاحظة عن الحقول الإجبارية
        note_label = QLabel("* الحقول المطلوبة")
        note_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 14px;
                font-style: italic;
                margin-top: 5px;
            }
        """)
        note_label.setAlignment(Qt.AlignRight)
        layout.addWidget(note_label)

        # أزرار الحفظ والإلغاء
        buttons_widget = QWidget()
        buttons_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setSpacing(20)

        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 10px 30px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_btn.clicked.connect(self.save_order)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 30px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addWidget(buttons_widget)

        # تركيز على حقل اسم الزبون عند فتح النافذة
        self.phone.setFocus()

    def validate_number_input(self, text):
        """التحقق من صحة إدخال الأرقام"""
        if text:
            try:
                value = int(text)
                return True
            except ValueError:
                return False
        return True

    def clear_form(self):
        """تفريغ النموذج للطلب التالي"""
        self.phone.clear()
        self.phone2.clear()
        self.address.clear()
        self.notes.clear()
        self.phone.setFocus()

    def reset_form(self):
        """إعادة تهيئة النموذج"""
        self.phone.clear()
        self.phone2.clear()
        self.address.clear()
        self.notes.clear()
        self.phone.setFocus()

    def save_order(self):
        """حفظ الطلب في قاعدة البيانات"""
        try:
            # التحقق من الحقول الإجبارية
            if not self.phone.text():
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم الهاتف")
                self.phone.setFocus()
                return

            # التحقق من صحة رقم الهاتف (11 رقمًا بالضبط)
            phone = self.phone.text().strip()
            if len(phone) != 11 or not phone.isdigit():
                QMessageBox.warning(self, "تنبيه", "رقم الهاتف يجب أن يكون 11 رقمًا بالضبط")
                self.phone.setFocus()
                return

            # التحقق من رقم الهاتف البديل (11 رقمًا بالضبط إذا تم إدخاله)
            phone2 = self.phone2.text().strip()
            if phone2 and (len(phone2) != 11 or not phone2.isdigit()):
                QMessageBox.warning(self, "تنبيه", "رقم الهاتف البديل يجب أن يكون 11 رقمًا بالضبط")
                self.phone2.setFocus()
                return

            if not self.address.text():
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال العنوان")
                self.address.setFocus()
                return

            conn = self.db.get_connection()
            cursor = conn.cursor()

            try:
                # إدخال الطلب
                if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                    cursor.execute('''
                        INSERT INTO orders (
                            phone, phone2, address, notes, status, total_price, total_area
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ''', (
                        self.phone.text(),
                        self.phone2.text(),
                        self.address.text(),
                        self.notes.text(),
                        'جديد',
                        0.0,  # قيمة افتراضية للسعر الإجمالي
                        0.0   # قيمة افتراضية للمساحة الكلية
                    ))
                else:
                    cursor.execute('''
                        INSERT INTO orders (
                            phone, phone2, address, notes, status, total_price, total_area
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        self.phone.text(),
                        self.phone2.text(),
                        self.address.text(),
                        self.notes.text(),
                        'جديد',
                        0.0,  # قيمة افتراضية للسعر الإجمالي
                        0.0   # قيمة افتراضية للمساحة الكلية
                    ))

                order_id = cursor.lastrowid

                conn.commit()
                QMessageBox.information(self, "نجاح", "تم حفظ الطلب بنجاح")

                # تحديث قائمة الطلبات الواردة
                if hasattr(self.parent(), 'incoming_orders_dialog'):
                    self.parent().incoming_orders_dialog.load_orders()
                # تحديث جدول الطلبات الواردة إذا كان موجوداً (اسم متغير آخر)
                if hasattr(self.parent(), 'incoming_orders_table'):
                    self.parent().incoming_orders_table.load_orders()

                # تحديث عدد الطلبات على الأزرار
                if hasattr(self.parent(), 'update_order_counts'):
                    self.parent().update_order_counts()

                # إعادة تهيئة النموذج
                self.clear_form()

            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الطلب: {str(e)}")

class CarpetDimensionsDialog(QDialog):
    """نافذة إدخال أطوال السجاد"""
    def __init__(self, carpet_count, parent=None):
        super().__init__(parent)
        try:
            self.carpet_count = int(carpet_count)
        except ValueError:
            self.carpet_count = 0
        self.dimensions = []
        self.setup_ui()
        # Delay focus to ensure it works after dialog appears
        QTimer.singleShot(100, self.set_initial_focus)
        # تركيز المؤشر على حقل الطول للسجاد الأول
        self.setFocusPolicy(Qt.StrongFocus)



    def update_area(self, length_field, width_field, area_label):
        """تحديث حساب المساحة"""
        try:
            length_text = length_field.text().replace(',', '.')
            width_text = width_field.text().replace(',', '.')

            if length_text and width_text:
                length_val = float(length_text)
                width_val = float(width_text)
                area = length_val * width_val
                area_label.setText(f"{area:.2f} متر²")
            else:
                area_label.setText("0.00 متر²")
        except ValueError:
            area_label.setText("0.00 متر²")

        # تحديث المساحة الكلية
        self.update_total_area()

    def update_total_area(self):
        """تحديث المساحة الكلية"""
        total_area = 0
        for length, width, _ in self.carpet_inputs:
            try:
                length_val = float(length.text().replace(',', '.') or 0)
                width_val = float(width.text().replace(',', '.') or 0)
                total_area += length_val * width_val
            except ValueError:
                continue

        self.total_area_label.setText(f"المساحة الكلية: {total_area:.2f} متر²")

    def setup_ui(self):
        self.setWindowTitle("أطوال السجاد")
        self.setStyleSheet(WINDOW_STYLE)
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)  # زيادة الارتفاع الأدنى للنافذة
        layout = QVBoxLayout(self)

        # عنوان النافذة
        title_widget = QWidget()
        title_widget.setStyleSheet(TITLE_CONTAINER_STYLE)
        title_layout = QVBoxLayout(title_widget)
        title_label = QLabel("أدخل أبعاد السجاد")
        title_label.setStyleSheet(TITLE_STYLE)
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)
        layout.addWidget(title_widget)

        # إنشاء نموذج لكل سجادة
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(15)
        self.carpet_inputs = []

        for i in range(self.carpet_count):
            carpet_widget = QWidget()
            carpet_layout = QHBoxLayout(carpet_widget)
            carpet_layout.setSpacing(10)

            length = QLineEdit()
            length.setPlaceholderText("الطول بالمتر")
            length.setMaximumWidth(120)
            # إضافة مدقق للأرقام العشرية
            length.setValidator(QDoubleValidator(0.1, 100.0, 2))
            # تعيين الإعدادات المحلية لقبول النقطة والفاصلة
            length.validator().setLocale(QLocale(QLocale.C))

            width = QLineEdit()
            width.setPlaceholderText("العرض بالمتر")
            width.setMaximumWidth(120)
            # إضافة مدقق للأرقام العشرية
            width.setValidator(QDoubleValidator(0.1, 100.0, 2))
            # تعيين الإعدادات المحلية لقبول النقطة والفاصلة
            width.validator().setLocale(QLocale(QLocale.C))

            area_label = QLabel("0.00 متر²")
            area_label.setMinimumWidth(100)
            area_label.setStyleSheet("font-weight: bold;")

            carpet_layout.addWidget(QLabel("الطول:"))
            carpet_layout.addWidget(length)
            carpet_layout.addWidget(QLabel("العرض:"))
            carpet_layout.addWidget(width)
            carpet_layout.addWidget(QLabel("المساحة:"))
            carpet_layout.addWidget(area_label)
            carpet_layout.addStretch()

            # ربط دالة تحديث المساحة
            length.textChanged.connect(
                lambda text, l=length, w=width, a=area_label: self.update_area(l, w, a)
            )
            width.textChanged.connect(
                lambda text, l=length, w=width, a=area_label: self.update_area(l, w, a)
            )

            self.carpet_inputs.append((length, width, area_label))
            form_layout.addRow(f"سجادة {i+1}:", carpet_widget)

        # إضافة مساحة تمرير عند الحاجة
        scroll = QScrollArea()
        scroll.setWidget(form_widget)
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")
        layout.addWidget(scroll)

        # إضافة مجموع المساحة الكلية
        total_widget = QWidget()
        total_widget.setStyleSheet("background-color: #f5f6fa; padding: 10px; border-radius: 5px;")
        total_layout = QHBoxLayout(total_widget)

        self.total_area_label = QLabel("المساحة الكلية: 0.00 متر²")
        self.total_area_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #2c3e50;")
        total_layout.addStretch()
        total_layout.addWidget(self.total_area_label)
        total_layout.addStretch()

        layout.addWidget(total_widget)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(get_button_style('success'))
        save_btn.setMinimumWidth(120)
        save_btn.clicked.connect(self.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(get_button_style('danger'))
        cancel_btn.setMinimumWidth(120)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

    def set_initial_focus(self):
        """Set focus on first carpet length input"""
        if self.carpet_inputs and len(self.carpet_inputs) > 0:
            self.carpet_inputs[0][0].setFocus()
            self.carpet_inputs[0][0].selectAll()

    def get_dimensions(self):
        """الحصول على أبعاد السجاد"""
        dimensions = []
        for length, width, area_label in self.carpet_inputs:
            try:
                length_val = float(length.text().replace(',', '.') or 0)
                width_val = float(width.text().replace(',', '.') or 0)
                area = length_val * width_val
                dimensions.append((length_val, width_val, area))
            except ValueError:
                dimensions.append((0, 0, 0))
        return dimensions

class OrdersViewDialog(BaseDialog):
    """نافذة عرض الطلبات"""
    def __init__(self, parent=None):
        super().__init__(parent, "عرض الطلبات")

    def setup_ui(self):
        super().setup_ui()

        # أزرار التصفية
        filter_layout = QHBoxLayout()

        statuses = [("الكل", "#3498db"), ("جديد", "#2ecc71"),
                   ("قيد التنفيذ", "#f1c40f"), ("مكتمل", "#8e44ad")]

        for status, color in statuses:
            btn = QPushButton(status)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(lambda checked, s=status: self.filter_orders(s))
            filter_layout.addWidget(btn)

        self.layout.addLayout(filter_layout)

        # جدول الطلبات
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "رقم الهاتف", "رقم الهاتف البديل", "العنوان",
            "السعر", "الحالة", "التاريخ", "ملاحظات"
        ])

        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        self.orders_table.setStyleSheet(TABLE_STYLE)
        self.layout.addWidget(self.orders_table)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        refresh_btn = QPushButton("تحديث")
        refresh_btn.setStyleSheet(get_button_style('info'))
        refresh_btn.clicked.connect(lambda: self.load_orders())

        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet(get_button_style('secondary'))
        close_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(close_btn)
        self.layout.addLayout(buttons_layout)

        # تحميل الطلبات
        self.load_orders()

    def load_orders(self, status=None):
        """تحميل الطلبات من قاعدة البيانات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            query = """
                SELECT orders.id, orders.phone, orders.phone2, orders.address,
                       orders.status, orders.created_at, orders.notes
                FROM orders
            """

            params = []

            if status and status != "الكل":
                if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                    query += " WHERE orders.status = %s"
                else:
                    query += " WHERE orders.status = ?"
                params.append(status)

            query += " ORDER BY orders.created_at DESC"

            if hasattr(self.db, 'db_type') and self.db.db_type == 'mysql':
                cursor.execute(query, params)
            else:
                cursor.execute(query, params)
            orders = cursor.fetchall()

            self.orders_table.setRowCount(len(orders))

            for i, order in enumerate(orders):
                # تحويل السعر إلى تنسيق مقروء
                price = "{:,.0f}".format(0)  # لا يوجد سعر في قاعدة البيانات

                items = [
                    str(order[0]),
                    order[1],
                    order[2],
                    order[3],
                    f"{price} د.ع",
                    order[4],
                    order[5],
                    order[6]
                ]

                for j, item in enumerate(items):
                    table_item = QTableWidgetItem(str(item))
                    table_item.setTextAlignment(Qt.AlignCenter)

                    if j == 4:  # عمود الحالة
                        color = STATUS_COLORS.get(item, "#95a5a6")
                        table_item.setBackground(QColor(color))
                        table_item.setForeground(QColor("white"))

                    self.orders_table.setItem(i, j, table_item)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الطلبات: {str(e)}")
            print(f"Error loading orders: {str(e)}")

    def filter_orders(self, status):
        """تصفية الطلبات حسب الحالة"""
        self.load_orders(status)

    @staticmethod
    def darken_color(color):
        """تعتيم اللون للتأثير عند التحويم"""
        c = QColor(color)
        h, s, v, a = c.getHsv()
        return QColor.fromHsv(h, s, int(v * 0.8), a).name()
