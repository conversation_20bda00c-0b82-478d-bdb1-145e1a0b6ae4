"""
أداة التحقق من أرقام الوصل وإصلاحها
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# استيراد وحدة قاعدة البيانات
try:
    from db.models import Database
    # استخدام قاعدة البيانات المناسبة (SQLite أو MySQL) حسب الإعدادات
    db_instance = Database()
except ImportError:
    # استخدام SQLite كاحتياطي في حالة عدم وجود وحدة قاعدة البيانات
    import sqlite3

# إعداد الترميز
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

# إعداد التسجيل
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"receipt_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('receipt_verification')

class ReceiptVerifier:
    """فئة التحقق من أرقام الوصل وإصلاحها"""

    def __init__(self, db_path):
        """تهيئة المتحقق مع مسار قاعدة البيانات"""
        self.db_path = db_path
        # تحديد نوع قاعدة البيانات
        from utils import config
        app_config = config.load_config()
        self.db_type = app_config.get('db_type', 'sqlite')
        logger.info(f"تم تهيئة المتحقق مع قاعدة البيانات: {db_path}")

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            # استخدام وحدة قاعدة البيانات إذا كانت متاحة
            if 'db_instance' in globals():
                return db_instance.get_connection()
            else:
                # استخدام SQLite كاحتياطي
                return sqlite3.connect(self.db_path)
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            # استخدام SQLite كاحتياطي في حالة الخطأ
            return sqlite3.connect(self.db_path)

    def verify_all_receipts(self):
        """التحقق من جميع أرقام الوصل"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # الحصول على جميع الطلبات
            cursor.execute("SELECT id, receipt_number FROM orders WHERE receipt_number IS NOT NULL")
            orders = cursor.fetchall()
            conn.close()

            logger.info(f"تم العثور على {len(orders)} طلب برقم وصل")

            fixed_count = 0
            for order_id, receipt_number in orders:
                if receipt_number and receipt_number.strip():
                    # التحقق من أن رقم الوصل محفوظ بشكل صحيح
                    if self.verify_receipt(order_id, receipt_number):
                        fixed_count += 1

            logger.info(f"تم التحقق من {len(orders)} طلب، تم إصلاح {fixed_count} طلب")
            return fixed_count
        except Exception as e:
            logger.error(f"خطأ في التحقق من أرقام الوصل: {str(e)}")
            return 0

    def verify_recent_receipts(self, days=7):
        """التحقق من أرقام الوصل للطلبات الحديثة فقط

        Args:
            days (int): عدد الأيام السابقة للتحقق من الطلبات (الافتراضي: 7 أيام)

        Returns:
            int: عدد الطلبات التي تم إصلاحها
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # الحصول على الطلبات المضافة أو المعدلة في الفترة المحددة
            date_limit = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")

            # التحقق من وجود عمود updated_at
            try:
                # للتعامل مع MySQL
                if hasattr(self, 'db_type') and self.db_type == 'mysql':
                    try:
                        # استخدام وحدة التحقق الخاصة بـ MySQL
                        from src.mysql_receipt_verification import ReceiptVerifier as MySQLVerifier
                        mysql_verifier = MySQLVerifier(self.db_path)
                        return mysql_verifier.verify_recent_receipts(days)
                    except Exception as e:
                        logger.error(f"خطأ في استخدام وحدة التحقق الخاصة بـ MySQL: {str(e)}")
                        # استمرار باستخدام الطريقة الحالية
                        cursor.execute("""
                            SELECT COLUMN_NAME
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_SCHEMA = DATABASE()
                            AND TABLE_NAME = 'orders'
                        """)
                        columns = [col[0] for col in cursor.fetchall()]
                else:
                    # للتعامل مع SQLite
                    cursor.execute("PRAGMA table_info(orders)")
                    columns = [col[1] for col in cursor.fetchall()]
            except Exception as e:
                logger.warning(f"خطأ في التحقق من أعمدة الجدول: {str(e)}")
                columns = []

            if 'updated_at' in columns:
                # استخدام created_at و updated_at للبحث
                cursor.execute("""
                    SELECT id, receipt_number
                    FROM orders
                    WHERE receipt_number IS NOT NULL
                    AND (created_at >= ? OR updated_at >= ?)
                """, (date_limit, date_limit))
            else:
                # استخدام created_at فقط للبحث
                cursor.execute("""
                    SELECT id, receipt_number
                    FROM orders
                    WHERE receipt_number IS NOT NULL
                    AND created_at >= ?
                """, (date_limit,))

            orders = cursor.fetchall()
            conn.close()

            logger.info(f"تم العثور على {len(orders)} طلب حديث برقم وصل (آخر {days} أيام)")

            # التحقق من كل طلب
            fixed_count = 0
            for order_id, receipt_number in orders:
                if receipt_number and receipt_number.strip():
                    if self.verify_receipt(order_id, receipt_number):
                        fixed_count += 1

            logger.info(f"تم التحقق من {len(orders)} طلب حديث، تم إصلاح {fixed_count} طلب")
            return fixed_count
        except Exception as e:
            logger.error(f"خطأ في التحقق من أرقام الوصل الحديثة: {str(e)}")
            return 0

    def verify_receipt(self, order_id, expected_receipt):
        """التحقق من رقم وصل محدد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT receipt_number FROM orders WHERE id = ?", (order_id,))
            actual_receipt = cursor.fetchone()

            if actual_receipt:
                actual_receipt = actual_receipt[0]

                if actual_receipt != expected_receipt:
                    logger.warning(f"الطلب {order_id}: رقم الوصل المتوقع ({expected_receipt}) لا يتطابق مع القيمة المخزنة ({actual_receipt})")

                    # إصلاح رقم الوصل
                    cursor.execute("UPDATE orders SET receipt_number = ? WHERE id = ?", (expected_receipt, order_id))
                    conn.commit()

                    # التحقق من نجاح الإصلاح
                    cursor.execute("SELECT receipt_number FROM orders WHERE id = ?", (order_id,))
                    fixed_receipt = cursor.fetchone()[0]

                    if fixed_receipt == expected_receipt:
                        logger.info(f"تم إصلاح رقم الوصل للطلب {order_id} من ({actual_receipt}) إلى ({expected_receipt})")
                        conn.close()
                        return True
                    else:
                        logger.error(f"فشل إصلاح رقم الوصل للطلب {order_id}. القيمة الحالية: {fixed_receipt}")
                        conn.close()
                        return False
                else:
                    logger.info(f"الطلب {order_id}: رقم الوصل ({expected_receipt}) صحيح")
                    conn.close()
                    return False
            else:
                logger.error(f"لم يتم العثور على الطلب {order_id}")
                conn.close()
                return False
        except Exception as e:
            logger.error(f"خطأ في التحقق من رقم الوصل للطلب {order_id}: {str(e)}")
            return False

def run_verification(check_all=False, days=7):
    """تشغيل عملية التحقق من أرقام الوصل

    Args:
        check_all (bool): إذا كانت True، يتم التحقق من جميع الطلبات. وإلا، يتم التحقق من الطلبات الحديثة فقط.
        days (int): عدد الأيام للتحقق من الطلبات الحديثة (الافتراضي: 7 أيام).

    Returns:
        int: عدد الطلبات التي تم إصلاحها
    """
    try:
        # تحميل إعدادات البرنامج
        try:
            from utils import config
            app_config = config.load_config()
            days = app_config.get('receipt_check_days', days)

            # تحديد نوع قاعدة البيانات من الإعدادات
            db_type = app_config.get('db_type', 'sqlite')
            if db_type == 'mysql':
                # استخدام قاعدة بيانات MySQL
                db_path = app_config.get('db_name', 'carpet_cleaning_service')
                logger.info(f"استخدام قاعدة بيانات MySQL: {db_path}")

                # استخدام وحدة التحقق الخاصة بـ MySQL
                try:
                    from src.mysql_receipt_verification import ReceiptVerifier as MySQLVerifier
                    mysql_verifier = MySQLVerifier(
                        db_name=db_path,
                        host=app_config.get('db_host', 'localhost'),
                        user=app_config.get('db_user', 'carpet_user'),
                        password=app_config.get('db_password', '@#57111819752#@'),
                        port=int(app_config.get('db_port', '3306'))
                    )

                    # التحقق فقط من الطلبات الجديدة أو المعدلة حديثًا إلا إذا تم طلب فحص الكل
                    if check_all:
                        # استخدام الدالة الأصلية للتحقق من جميع الطلبات
                        fixed_count = mysql_verifier.verify_recent_receipts(days=365)  # استخدام فترة طويلة للحصول على جميع الطلبات
                        print(f"تم التحقق من جميع أرقام الوصل وإصلاح {fixed_count} طلب")
                    else:
                        # فحص الطلبات المضافة أو المعدلة في الفترة المحددة
                        fixed_count = mysql_verifier.verify_recent_receipts(days=days)
                        print(f"تم التحقق من أرقام الوصل الحديثة (آخر {days} أيام) وإصلاح {fixed_count} طلب")

                    print(f"تم حفظ سجل العمليات في: {log_file}")
                    return fixed_count
                except Exception as e:
                    logger.error(f"خطأ في استخدام وحدة التحقق الخاصة بـ MySQL: {str(e)}")
                    # استمرار باستخدام الطريقة الحالية
            else:
                # استخدام قاعدة بيانات SQLite
                db_path = "carpet_cleaning.db"
                logger.info(f"استخدام قاعدة بيانات SQLite: {db_path}")
        except Exception as e:
            logger.warning(f"خطأ في تحميل الإعدادات: {str(e)}. استخدام الإعدادات الافتراضية.")
            # استخدام القيمة الافتراضية في حالة حدوث خطأ
            db_path = "carpet_cleaning.db"

        verifier = ReceiptVerifier(db_path)

        # التحقق فقط من الطلبات الجديدة أو المعدلة حديثًا إلا إذا تم طلب فحص الكل
        if check_all:
            # استخدام الدالة الأصلية للتحقق من جميع الطلبات
            fixed_count = verifier.verify_all_receipts()
            print(f"تم التحقق من جميع أرقام الوصل وإصلاح {fixed_count} طلب")
        else:
            # فحص الطلبات المضافة أو المعدلة في الفترة المحددة
            fixed_count = verifier.verify_recent_receipts(days=days)
            print(f"تم التحقق من أرقام الوصل الحديثة (آخر {days} أيام) وإصلاح {fixed_count} طلب")

        print(f"تم حفظ سجل العمليات في: {log_file}")
        return fixed_count
    except Exception as e:
        logger.error(f"خطأ في تشغيل عملية التحقق: {str(e)}")
        print(f"حدث خطأ: {str(e)}")
        return 0

if __name__ == "__main__":
    print("بدء عملية التحقق من أرقام الوصل...")
    fixed_count = run_verification()
    print(f"اكتملت عملية التحقق. تم إصلاح {fixed_count} طلب.")
