#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة التحقق من أرقام الوصل وإصلاحها - MySQL فقط
"""

import os
import sys
import logging
from datetime import datetime, timedelta

try:
    from db.models import Database
    # استخدام قاعدة البيانات MySQL فقط
    db_instance = Database()
except ImportError:
    # تم إزالة SQLite - نستخدم MySQL فقط
    db_instance = None

# إعداد الترميز
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

# إعداد التسجيل
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"receipt_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('receipt_verification')

class ReceiptVerifier:
    """فئة التحقق من أرقام الوصل وإصلاحها - MySQL فقط"""

    def __init__(self, db_path):
        """تهيئة المتحقق مع مسار قاعدة البيانات"""
        self.db_path = db_path
        self.db_type = 'mysql'  # نستخدم MySQL فقط
        logger.info(f"تم تهيئة المتحقق مع قاعدة البيانات: {db_path}")

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات MySQL فقط"""
        try:
            if 'db_instance' in globals() and db_instance:
                return db_instance.get_connection()
            else:
                # استخدام Database مباشرة
                from db.models import Database
                db = Database()
                return db.get_connection()
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات MySQL: {str(e)}")
            raise

    def verify_recent_receipts(self, days=7):
        """التحقق من أرقام الوصل للطلبات الحديثة فقط - MySQL فقط
        
        Args:
            days (int): عدد الأيام السابقة للتحقق من الطلبات (الافتراضي: 7 أيام)
            
        Returns:
            dict: تفاصيل نتائج التحقق
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # الحصول على الطلبات المضافة أو المعدلة في الفترة المحددة
            date_limit = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")

            # استعلام للحصول على الطلبات الحديثة - MySQL فقط
            cursor.execute("""
                SELECT id, receipt_number, customer_name, status
                FROM orders
                WHERE receipt_number IS NOT NULL
                AND (created_at >= %s OR updated_at >= %s)
                ORDER BY created_at DESC
            """, (date_limit, date_limit))

            orders = cursor.fetchall()
            conn.close()

            logger.info(f"تم العثور على {len(orders)} طلب حديث (آخر {days} أيام)")

            # إحصائيات التحقق
            total_orders = len(orders)
            fixed_count = 0
            issues = []

            # التحقق من كل طلب
            for order in orders:
                order_id = order[0]
                receipt_number = order[1]
                customer_name = order[2]
                status = order[3]

                if receipt_number and receipt_number.strip():
                    # التحقق من صحة رقم الوصل
                    if self.verify_receipt_format(receipt_number):
                        logger.debug(f"الطلب {order_id}: رقم الوصل ({receipt_number}) صحيح")
                    else:
                        issues.append({
                            'order_id': order_id,
                            'receipt_number': receipt_number,
                            'customer_name': customer_name,
                            'status': status,
                            'issue': 'تنسيق رقم الوصل غير صحيح'
                        })
                        logger.warning(f"الطلب {order_id}: تنسيق رقم الوصل ({receipt_number}) غير صحيح")

            result = {
                'total': total_orders,
                'fixed': fixed_count,
                'issues': issues
            }

            logger.info(f"تم التحقق من {total_orders} طلب حديث، تم إصلاح {fixed_count} طلب")
            return result

        except Exception as e:
            logger.error(f"خطأ في التحقق من أرقام الوصل الحديثة: {str(e)}")
            return {'total': 0, 'fixed': 0, 'issues': []}

    def verify_receipt_format(self, receipt_number):
        """التحقق من تنسيق رقم الوصل"""
        if not receipt_number or not receipt_number.strip():
            return False
        
        # التحقق من أن رقم الوصل يحتوي على أرقام فقط أو أرقام مع أحرف
        receipt_clean = receipt_number.strip()
        
        # قبول أرقام الوصل التي تحتوي على أرقام وأحرف ورموز
        if len(receipt_clean) >= 1:
            return True
        
        return False

def run_verification(check_all=False, days=7):
    """تشغيل عملية التحقق من أرقام الوصل - MySQL فقط
    
    Args:
        check_all (bool): إذا كانت True، يتم التحقق من جميع الطلبات. وإلا، يتم التحقق من الطلبات الحديثة فقط.
        days (int): عدد الأيام للتحقق من الطلبات الحديثة (الافتراضي: 7 أيام).
        
    Returns:
        dict: نتائج التحقق
    """
    try:
        # تحميل إعدادات البرنامج
        try:
            from utils import config
            app_config = config.load_config()
            days = app_config.get('receipt_check_days', days)
            
            # استخدام قاعدة بيانات MySQL فقط
            db_path = app_config.get('db_name', 'carpet_cleaning_service')
            logger.info(f"استخدام قاعدة بيانات MySQL: {db_path}")
            
        except Exception as e:
            logger.warning(f"خطأ في تحميل الإعدادات: {str(e)}. استخدام الإعدادات الافتراضية.")
            db_path = "carpet_cleaning_service"

        verifier = ReceiptVerifier(db_path)

        # التحقق من الطلبات الحديثة فقط (لتحسين الأداء)
        if check_all:
            # استخدام فترة طويلة للحصول على جميع الطلبات
            result = verifier.verify_recent_receipts(days=365)
            print(f"تم التحقق من جميع أرقام الوصل وإصلاح {result}")
        else:
            # فحص الطلبات المضافة أو المعدلة في الفترة المحددة
            result = verifier.verify_recent_receipts(days=days)
            print(f"تم التحقق من أرقام الوصل الحديثة (آخر {days} أيام) وإصلاح {result}")

        print(f"تم حفظ سجل العمليات في: {log_file}")
        return result
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل عملية التحقق: {str(e)}")
        print(f"حدث خطأ: {str(e)}")
        return {'total': 0, 'fixed': 0, 'issues': []}

if __name__ == "__main__":
    print("بدء عملية التحقق من أرقام الوصل...")
    result = run_verification()
    print(f"اكتملت عملية التحقق. النتائج: {result}")
