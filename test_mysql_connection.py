"""
اختبار الاتصال بقاعدة بيانات MySQL
"""

import mysql.connector
from mysql.connector import Error

def test_connection():
    """اختبار الاتصال بقاعدة بيانات MySQL"""
    try:
        # إعدادات الاتصال
        config = {
            'host': 'localhost',
            'user': 'carpet_user',
            'password': '@#57111819752#@',
            'database': 'carpet_cleaning_service',
            'port': 3306,
            'charset': 'utf8mb4',
            'raise_on_warnings': True
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {config}")
        
        # محاولة الاتصال
        conn = mysql.connector.connect(**config)
        
        if conn.is_connected():
            print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
            
            # الحصول على معلومات الخادم
            db_info = conn.get_server_info()
            print(f"إصدار خادم MySQL: {db_info}")
            
            # الحصول على اسم قاعدة البيانات
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"قاعدة البيانات الحالية: {db_name}")
            
            # إغلاق الاتصال
            cursor.close()
            conn.close()
            print("تم إغلاق الاتصال بقاعدة بيانات MySQL")
            
            return True
    except Error as e:
        print(f"خطأ في الاتصال بقاعدة بيانات MySQL: {e}")
        return False

if __name__ == "__main__":
    test_connection()
