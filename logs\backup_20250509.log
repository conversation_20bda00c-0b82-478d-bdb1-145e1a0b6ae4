2025-05-09 03:32:35,695 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 03:32:35,712 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 03:32:35,712 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 03:32:35,712 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 03:48:33,125 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 03:48:33,126 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 03:48:33,126 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 03:48:33,126 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:11:37,700 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:11:37,703 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:11:37,704 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:11:37,704 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:18:55,890 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:18:55,890 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:18:55,941 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:18:55,943 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:19:24,223 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:19:53,236 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:27:51,354 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:27:51,369 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:27:51,371 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:27:51,371 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:28:38,307 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:28:43,798 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:28:56,734 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:29:01,650 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:29:06,084 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:32:53,631 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:32:53,631 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:32:53,631 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:32:53,631 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:33:07,712 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:33:13,790 - rep_orders_view - ERROR - خطأ في تحميل الطلبات: tuple indices must be integers or slices, not str
2025-05-09 04:39:24,744 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:39:24,744 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:39:24,744 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:39:24,744 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:39:38,073 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب حميد
2025-05-09 04:39:42,663 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب سرمد
2025-05-09 04:39:54,494 - warehouse - INFO - تغيير حالة مربع الاختيار في الصف 0 إلى 2
2025-05-09 04:39:54,494 - warehouse - INFO - بدء تحديث عدد الطلبات المحددة
2025-05-09 04:39:54,494 - warehouse - INFO - تم تحديد طلب واحد
2025-05-09 04:40:02,791 - distribution_window - ERROR - خطأ في إضافة المندوب: (1054, "Unknown column 'area' in 'field list'")
2025-05-09 04:44:48,696 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:44:48,706 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:44:48,706 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:44:48,706 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:45:32,655 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:45:32,655 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:45:32,655 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:45:32,655 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:45:53,463 - warehouse - INFO - تغيير حالة مربع الاختيار في الصف 0 إلى 2
2025-05-09 04:45:53,479 - warehouse - INFO - بدء تحديث عدد الطلبات المحددة
2025-05-09 04:45:53,479 - warehouse - INFO - تم تحديد طلب واحد
2025-05-09 04:46:18,199 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب سمير
2025-05-09 04:46:21,747 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب حميد
2025-05-09 04:46:24,804 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب سرمد
2025-05-09 04:46:32,176 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب حميد
2025-05-09 04:51:38,805 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:51:38,805 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:51:38,805 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:51:38,805 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:51:52,365 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب سمير
2025-05-09 04:51:55,915 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب حميد
2025-05-09 04:51:58,884 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب سرمد
2025-05-09 04:52:02,726 - rep_orders_view - INFO - تم تحميل 0 طلب للمندوب حميد
2025-05-09 04:56:49,750 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:56:49,750 - receipt_verification - INFO - استخدام قاعدة بيانات MySQL: carpet_cleaning_service
2025-05-09 04:56:49,750 - receipt_verification - INFO - تم تهيئة المتحقق مع قاعدة البيانات: carpet_cleaning_service
2025-05-09 04:56:49,750 - receipt_verification - ERROR - خطأ في التحقق من أرقام الوصل الحديثة: Connection.__init__() got an unexpected keyword argument 'raise_on_warnings'
2025-05-09 04:57:04,605 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:04,605 - rep_orders_view - INFO - معرف المندوب: 15, نوع: <class 'int'>
2025-05-09 04:57:04,606 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:04,608 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 4
2025-05-09 04:57:04,609 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 4
2025-05-09 04:57:04,661 - rep_orders_view - INFO - تم تحميل 4 طلب للمندوب حميد
2025-05-09 04:57:10,941 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:10,956 - rep_orders_view - INFO - معرف المندوب: 12, نوع: <class 'int'>
2025-05-09 04:57:10,956 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:10,969 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 1
2025-05-09 04:57:10,970 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 1
2025-05-09 04:57:10,978 - rep_orders_view - INFO - تم تحميل 1 طلب للمندوب سمير
2025-05-09 04:57:14,369 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:14,369 - rep_orders_view - INFO - معرف المندوب: 18, نوع: <class 'int'>
2025-05-09 04:57:14,369 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:14,369 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 1
2025-05-09 04:57:14,369 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 1
2025-05-09 04:57:14,382 - rep_orders_view - INFO - تم تحميل 1 طلب للمندوب سرمد
2025-05-09 04:57:23,581 - warehouse - INFO - تغيير حالة مربع الاختيار في الصف 0 إلى 2
2025-05-09 04:57:23,581 - warehouse - INFO - بدء تحديث عدد الطلبات المحددة
2025-05-09 04:57:23,581 - warehouse - INFO - تم تحديد طلب واحد
2025-05-09 04:57:48,736 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:48,736 - rep_orders_view - INFO - معرف المندوب: 16, نوع: <class 'int'>
2025-05-09 04:57:48,736 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:48,736 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 1
2025-05-09 04:57:48,736 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 1
2025-05-09 04:57:48,736 - rep_orders_view - INFO - تم تحميل 1 طلب للمندوب مصطفى
2025-05-09 04:57:54,072 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:54,072 - rep_orders_view - INFO - معرف المندوب: 15, نوع: <class 'int'>
2025-05-09 04:57:54,072 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:54,087 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 4
2025-05-09 04:57:54,087 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 4
2025-05-09 04:57:54,114 - rep_orders_view - INFO - تم تحميل 4 طلب للمندوب حميد
2025-05-09 04:57:57,688 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:57:57,688 - rep_orders_view - INFO - معرف المندوب: 12, نوع: <class 'int'>
2025-05-09 04:57:57,688 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:57:57,688 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 1
2025-05-09 04:57:57,688 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 1
2025-05-09 04:57:57,704 - rep_orders_view - INFO - تم تحميل 1 طلب للمندوب سمير
2025-05-09 04:58:01,514 - rep_orders_view - INFO - نوع قاعدة البيانات: mysql
2025-05-09 04:58:01,514 - rep_orders_view - INFO - معرف المندوب: 18, نوع: <class 'int'>
2025-05-09 04:58:01,514 - rep_orders_view - INFO - استعلام MySQL: 
            SELECT o.id, o.receipt_number, o.address, o.phone, o.phone2,
                   (SELECT COUNT(cd.id) FROM carpet_dimensions cd WHERE cd.order_id = o.id) as carpet_count,
                   o.total_area, o.blanket_count,
                   o.total_price, o.status, o.created_at, o.pickup_representative_id,
                   o.notes
            FROM orders o
            WHERE o.pickup_representative_id = %s AND (o.status = 'distribution' OR o.status = 'distribution_delivered')
            ORDER BY o.created_at DESC
            
2025-05-09 04:58:01,514 - rep_orders_view - INFO - عدد الطلبات للمندوب (بدون فلتر الحالة): 1
2025-05-09 04:58:01,514 - rep_orders_view - INFO - عدد الطلبات للمندوب (مع فلتر الحالة): 1
2025-05-09 04:58:01,514 - rep_orders_view - INFO - تم تحميل 1 طلب للمندوب سرمد
2025-05-09 17:12:36,271 - backup_system - INFO - بدء عملية إنشاء نسخة احتياطية
2025-05-09 17:12:36,295 - backup_system - INFO - تم إنشاء نسخة احتياطية بنجاح: C:/Users/<USER>/Desktop/aasseel-sajjad/نسخة احتياطية\carpet_cleaning_backup_20250509_171236.db
2025-05-09 17:15:43,274 - backup_system - INFO - بدء عملية إنشاء نسخة احتياطية
2025-05-09 17:15:43,274 - backup_system - INFO - تم إنشاء نسخة احتياطية بنجاح: C:/Users/<USER>/Desktop/aasseel-sajjad/نسخة احتياطية\carpet_cleaning_backup_20250509_171543.db
2025-05-09 17:16:45,657 - backup_system - INFO - بدء عملية إنشاء نسخة احتياطية
2025-05-09 17:16:45,657 - backup_system - INFO - تم إنشاء نسخة احتياطية بنجاح: C:/Users/<USER>/Desktop/aasseel-sajjad/نسخة احتياطية\carpet_cleaning_backup_20250509_171645.db
