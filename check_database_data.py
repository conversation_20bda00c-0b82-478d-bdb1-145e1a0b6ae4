#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من البيانات الموجودة في قاعدة البيانات
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_database_data():
    """التحقق من البيانات الموجودة في قاعدة البيانات"""
    try:
        print("\n" + "=" * 60)
        print("التحقق من البيانات في قاعدة البيانات".center(60))
        print("=" * 60)
        
        # استيراد وحدة قاعدة البيانات
        from db.models import Database
        
        # إنشاء اتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        print(f"✓ تم الاتصال بقاعدة البيانات: {db.db_type}")
        
        # 1. التحقق من جدول الطلبات
        print("\n1. جدول الطلبات (orders):")
        cursor.execute("SELECT COUNT(*) FROM orders")
        orders_count = cursor.fetchone()[0]
        print(f"عدد الطلبات: {orders_count}")
        
        if orders_count > 0:
            cursor.execute("SELECT id, phone, address, status, created_at FROM orders LIMIT 5")
            orders = cursor.fetchall()
            print("أول 5 طلبات:")
            for order in orders:
                print(f"  - ID: {order[0]}, الهاتف: {order[1]}, العنوان: {order[2][:30]}..., الحالة: {order[3]}")
        
        # 2. التحقق من جدول المندوبين
        print("\n2. جدول المندوبين (delegates):")
        cursor.execute("SELECT COUNT(*) FROM delegates")
        delegates_count = cursor.fetchone()[0]
        print(f"عدد المندوبين: {delegates_count}")
        
        if delegates_count > 0:
            cursor.execute("SELECT id, name FROM delegates")
            delegates = cursor.fetchall()
            print("المندوبين:")
            for delegate in delegates:
                print(f"  - ID: {delegate[0]}, الاسم: {delegate[1]}")
        
        # 3. التحقق من جدول طلبات المندوبين
        print("\n3. جدول طلبات المندوبين (delegate_orders):")
        cursor.execute("SELECT COUNT(*) FROM delegate_orders")
        delegate_orders_count = cursor.fetchone()[0]
        print(f"عدد طلبات المندوبين: {delegate_orders_count}")
        
        if delegate_orders_count > 0:
            cursor.execute("SELECT id, phone_number, delegate_id, created_at FROM delegate_orders LIMIT 5")
            delegate_orders = cursor.fetchall()
            print("أول 5 طلبات مندوبين:")
            for order in delegate_orders:
                print(f"  - ID: {order[0]}, الهاتف: {order[1]}, مندوب ID: {order[2]}")
        
        # 4. التحقق من جدول أبعاد السجاد
        print("\n4. جدول أبعاد السجاد (carpet_dimensions):")
        cursor.execute("SELECT COUNT(*) FROM carpet_dimensions")
        dimensions_count = cursor.fetchone()[0]
        print(f"عدد أبعاد السجاد: {dimensions_count}")
        
        # 5. التحقق من جدول الإعدادات
        print("\n5. جدول الإعدادات (settings):")
        cursor.execute("SELECT COUNT(*) FROM settings")
        settings_count = cursor.fetchone()[0]
        print(f"عدد الإعدادات: {settings_count}")
        
        if settings_count > 0:
            cursor.execute("SELECT price_per_meter, blanket_price, delivery_price FROM settings LIMIT 1")
            settings = cursor.fetchone()
            print(f"الأسعار: سعر المتر: {settings[0]}, سعر البطانية: {settings[1]}, سعر التوصيل: {settings[2]}")
        
        # 6. التحقق من جدول المصروفات
        print("\n6. جدول المصروفات (expenses):")
        cursor.execute("SELECT COUNT(*) FROM expenses")
        expenses_count = cursor.fetchone()[0]
        print(f"عدد المصروفات: {expenses_count}")
        
        # 7. التحقق من جدول المستخدمين
        print("\n7. جدول المستخدمين (users):")
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        print(f"عدد المستخدمين: {users_count}")
        
        cursor.execute("SELECT id, username, role FROM users")
        users = cursor.fetchall()
        print("المستخدمين:")
        for user in users:
            print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}, الدور: {user[2]}")
        
        # 8. التحقق من هيكل جدول الطلبات
        print("\n8. هيكل جدول الطلبات:")
        cursor.execute("DESCRIBE orders")
        columns = cursor.fetchall()
        print("الأعمدة:")
        for column in columns:
            print(f"  - {column[0]}: {column[1]}")
        
        # 9. التحقق من الطلبات حسب الحالة
        print("\n9. الطلبات حسب الحالة:")
        cursor.execute("SELECT status, COUNT(*) FROM orders GROUP BY status")
        status_counts = cursor.fetchall()
        for status, count in status_counts:
            print(f"  - {status}: {count} طلب")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("ملخص البيانات".center(60))
        print("=" * 60)
        
        print(f"إجمالي الطلبات: {orders_count}")
        print(f"إجمالي المندوبين: {delegates_count}")
        print(f"إجمالي طلبات المندوبين: {delegate_orders_count}")
        print(f"إجمالي المستخدمين: {users_count}")
        
        if orders_count == 0:
            print("\n⚠️  تحذير: لا توجد طلبات في قاعدة البيانات!")
            print("هذا قد يفسر سبب عدم ظهور البيانات في القوائم.")
        
        print("\n" + "=" * 60 + "\n")
        
    except Exception as e:
        print(f"خطأ في التحقق من البيانات: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    check_database_data()
