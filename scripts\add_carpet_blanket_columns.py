#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإضافة أعمدة عدد السجاد وعدد البطانية إلى جدول delegate_orders
"""

import sqlite3
import os
import sys

# إضافة مسار المشروع إلى المسارات المتاحة
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def add_columns_to_delegate_orders():
    """إضافة أعمدة عدد السجاد وعدد البطانية إلى جدول delegate_orders"""
    
    db_file = "carpet_cleaning.db"
    conn = None
    
    try:
        # التحقق من وجود ملف قاعدة البيانات
        if not os.path.exists(db_file):
            print(f"خطأ: ملف قاعدة البيانات '{db_file}' غير موجود.")
            return False
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # التحقق مما إذا كانت الأعمدة موجودة بالفعل
        cursor.execute("PRAGMA table_info(delegate_orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # إضافة عمود عدد السجاد إذا لم يكن موجوداً
        if 'carpet_count' not in column_names:
            print("إضافة عمود 'carpet_count' (عدد السجاد) إلى جدول delegate_orders...")
            cursor.execute("ALTER TABLE delegate_orders ADD COLUMN carpet_count INTEGER DEFAULT 0")
            print("تم إضافة عمود 'carpet_count' بنجاح.")
        else:
            print("عمود 'carpet_count' موجود بالفعل في جدول delegate_orders.")
        
        # إضافة عمود عدد البطانية إذا لم يكن موجوداً
        if 'blanket_count' not in column_names:
            print("إضافة عمود 'blanket_count' (عدد البطانية) إلى جدول delegate_orders...")
            cursor.execute("ALTER TABLE delegate_orders ADD COLUMN blanket_count INTEGER DEFAULT 0")
            print("تم إضافة عمود 'blanket_count' بنجاح.")
        else:
            print("عمود 'blanket_count' موجود بالفعل في جدول delegate_orders.")
        
        # تحديث قيم الأعمدة الجديدة من جدول الطلبات الرئيسي (orders)
        print("تحديث قيم عدد السجاد وعدد البطانية من جدول orders...")
        cursor.execute("""
            UPDATE delegate_orders
            SET carpet_count = (
                SELECT carpet_count FROM orders 
                WHERE orders.id = delegate_orders.order_id
            ),
            blanket_count = (
                SELECT blanket_count FROM orders 
                WHERE orders.id = delegate_orders.order_id
            )
            WHERE EXISTS (
                SELECT 1 FROM orders 
                WHERE orders.id = delegate_orders.order_id
            )
        """)
        
        affected_rows = cursor.rowcount
        print(f"تم تحديث {affected_rows} سجل في جدول delegate_orders.")
        
        # حفظ التغييرات
        conn.commit()
        print("تم حفظ التغييرات في قاعدة البيانات بنجاح.")
        
        return True
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("بدء إضافة أعمدة عدد السجاد وعدد البطانية إلى جدول delegate_orders...")
    
    if add_columns_to_delegate_orders():
        print("تمت العملية بنجاح!")
    else:
        print("فشلت العملية.")