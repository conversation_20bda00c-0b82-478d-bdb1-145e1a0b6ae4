# تعليمات إعداد MySQL لبرنامج غسيل السجاد

## 1. تثبيت MySQL Server

### لنظام Windows:
1. قم بتنزيل MySQL Community Server من الموقع الرسمي: https://dev.mysql.com/downloads/mysql/
2. اختر الإصدار 8.0 (64-bit) وهو إصدار مجاني ومدعوم بشكل جيد
3. قم بتثبيت البرنامج باتباع خطوات المثبت
4. تأكد من تحديد "Server only" أو "Custom" واختيار المكونات التالية:
   - MySQL Server
   - MySQL Workbench (أداة إدارة رسومية)
   - Connector/Python (للاتصال من Python)
5. قم بتعيين كلمة مرور قوية للمستخدم الجذر (root)

## 2. تكوين MySQL للاتصالات البعيدة

لتمكين الاتصال بقاعدة البيانات من أجهزة أخرى عبر الشبكة المحلية أو الإنترنت:

1. افتح MySQL Workbench
2. اتصل بالخادم المحلي باستخدام حساب المستخدم الجذر
3. انتقل إلى: Server > Options File
4. في قسم Networking، ابحث عن "bind-address" وقم بتغييره إلى "0.0.0.0" للسماح بالاتصالات من أي عنوان IP
5. انقر على "Apply" ثم أعد تشغيل خدمة MySQL

## 3. إنشاء قاعدة البيانات والمستخدم

1. افتح MySQL Workbench واتصل بالخادم المحلي
2. افتح ملف `create_mysql_database.sql` وقم بتنفيذه لإنشاء قاعدة البيانات والجداول
3. افتح ملف `create_mysql_user.sql` وقم بتعديل كلمة المرور ثم تنفيذه لإنشاء المستخدم

## 4. تثبيت حزم Python اللازمة

قم بتثبيت حزم Python اللازمة للاتصال بـ MySQL:

```
pip install mysql-connector-python sqlalchemy
```

## 5. تكوين البرنامج للاتصال بـ MySQL

1. قم بتعديل ملف `db_mysql_connection.py` وتحديث إعدادات الاتصال:
   - `host`: عنوان IP للخادم (استخدم 'localhost' للخادم المحلي)
   - `user`: اسم المستخدم ('carpet_user')
   - `password`: كلمة المرور التي قمت بتعيينها
   - `port`: منفذ MySQL (عادة 3306)

2. قم بتعديل البرنامج لاستخدام فئة `MySQLDatabase` بدلاً من فئة `Database` الحالية

## 6. إعداد جدار الحماية

إذا كنت تريد الاتصال بقاعدة البيانات من أجهزة أخرى:

1. تأكد من فتح منفذ MySQL (3306) في جدار الحماية على الخادم
2. لنظام Windows، افتح "Windows Defender Firewall" > "Advanced settings" > "Inbound Rules" > "New Rule"
3. اختر "Port" > "TCP" > أدخل "3306" > اختر "Allow the connection" > أكمل المعالج

## 7. الاتصال عبر الإنترنت

للاتصال بقاعدة البيانات عبر الإنترنت:

1. تأكد من أن لديك عنوان IP ثابت أو استخدم خدمة DNS ديناميكية مثل No-IP أو DynDNS
2. قم بإعادة توجيه المنفذ 3306 في جهاز التوجيه (Router) الخاص بك إلى الخادم
3. استخدم عنوان IP العام أو اسم المضيف DNS في إعدادات الاتصال

## ملاحظات هامة

1. **الأمان**: تأكد من استخدام كلمات مرور قوية وتقييد الوصول إلى قاعدة البيانات
2. **النسخ الاحتياطي**: قم بإعداد نسخ احتياطي منتظم لقاعدة البيانات
3. **الأداء**: قد تحتاج إلى ضبط إعدادات MySQL لتحسين الأداء مع زيادة حجم البيانات

## استكشاف الأخطاء وإصلاحها

إذا واجهت مشاكل في الاتصال:

1. تأكد من تشغيل خدمة MySQL
2. تحقق من إعدادات الاتصال (المضيف، المنفذ، اسم المستخدم، كلمة المرور)
3. تأكد من أن المستخدم لديه الصلاحيات المناسبة
4. تحقق من إعدادات جدار الحماية
5. تحقق من سجلات أخطاء MySQL في `C:\ProgramData\MySQL\MySQL Server 8.0\Data\<hostname>.err`
