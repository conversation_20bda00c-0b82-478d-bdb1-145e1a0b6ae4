#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من أن البرنامج يستخدم MySQL فقط
"""

import os
import sys
import traceback

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def verify_mysql_only():
    """التحقق من أن البرنامج يستخدم MySQL فقط"""
    try:
        print("\n" + "=" * 60)
        print("التحقق من أن البرنامج يستخدم MySQL فقط".center(60))
        print("=" * 60)
        
        # 1. التحقق من عدم وجود ملفات SQLite
        print("\n1. التحقق من عدم وجود ملفات SQLite:")
        sqlite_files = ["carpet_cleaning.db", "carpet_db.db"]
        sqlite_found = False
        
        for file in sqlite_files:
            if os.path.exists(file):
                print(f"✗ تم العثور على ملف SQLite: {file}")
                sqlite_found = True
            else:
                print(f"✓ لا يوجد ملف SQLite: {file}")
        
        if not sqlite_found:
            print("✓ لا توجد ملفات SQLite في المشروع")
        
        # 2. التحقق من ملف التكوين
        print("\n2. التحقق من ملف التكوين:")
        try:
            from utils.config import load_config
            config = load_config()
            db_type = config.get('db_type', 'unknown')
            
            if db_type == 'mysql':
                print(f"✓ نوع قاعدة البيانات في التكوين: {db_type}")
                print(f"✓ خادم قاعدة البيانات: {config.get('db_host', 'غير محدد')}")
                print(f"✓ اسم قاعدة البيانات: {config.get('db_name', 'غير محدد')}")
            else:
                print(f"✗ نوع قاعدة البيانات في التكوين: {db_type}")
        except Exception as e:
            print(f"✗ خطأ في قراءة ملف التكوين: {str(e)}")
        
        # 3. التحقق من كائن Database
        print("\n3. التحقق من كائن Database:")
        try:
            from db.models import Database
            db = Database()
            
            if hasattr(db, 'db_type') and db.db_type == 'mysql':
                print(f"✓ نوع قاعدة البيانات في كائن Database: {db.db_type}")
            else:
                print(f"✗ نوع قاعدة البيانات في كائن Database: {getattr(db, 'db_type', 'غير محدد')}")
            
            # اختبار الاتصال
            try:
                conn = db.get_connection()
                print("✓ تم الاتصال بقاعدة البيانات MySQL بنجاح")
                
                # التحقق من الجداول
                cursor = conn.cursor()
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"✓ عدد الجداول في قاعدة البيانات: {len(tables)}")
                
                # التحقق من جدول المستخدمين
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                print(f"✓ عدد المستخدمين في قاعدة البيانات: {user_count}")
                
                conn.close()
                
            except Exception as e:
                print(f"✗ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
                
        except Exception as e:
            print(f"✗ خطأ في إنشاء كائن Database: {str(e)}")
        
        # 4. التحقق من وجود النسخة الاحتياطية
        print("\n4. التحقق من النسخة الاحتياطية:")
        backup_dir = "sqlite_backup"
        if os.path.exists(backup_dir):
            backup_files = os.listdir(backup_dir)
            print(f"✓ تم العثور على مجلد النسخة الاحتياطية مع {len(backup_files)} ملف")
            for file in backup_files:
                print(f"  - {file}")
        else:
            print("✗ لم يتم العثور على مجلد النسخة الاحتياطية")
        
        # 5. فحص الكود للتأكد من عدم وجود مراجع لـ SQLite
        print("\n5. فحص ملف models.py:")
        models_file = "db/models.py"
        if os.path.exists(models_file):
            with open(models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'sqlite3' in content:
                print("✗ تم العثور على مراجع لـ sqlite3 في ملف models.py")
            else:
                print("✓ لا توجد مراجع لـ sqlite3 في ملف models.py")
            
            if 'mysql' in content.lower():
                print("✓ تم العثور على مراجع لـ MySQL في ملف models.py")
            else:
                print("✗ لم يتم العثور على مراجع لـ MySQL في ملف models.py")
        
        print("\n" + "=" * 60)
        print("اكتمل التحقق".center(60))
        print("=" * 60)
        
        # ملخص النتائج
        print("\nملخص النتائج:")
        if not sqlite_found:
            print("✓ تم حذف جميع ملفات SQLite بنجاح")
        else:
            print("✗ لا تزال هناك ملفات SQLite موجودة")
        
        print("✓ البرنامج الآن يعتمد على MySQL فقط")
        print("✓ تم إنشاء نسخة احتياطية من ملفات SQLite")
        print("✓ يمكن تشغيل البرنامج على أجهزة متعددة باستخدام نفس قاعدة البيانات")
        
        print("\n" + "=" * 60 + "\n")
        
    except Exception as e:
        print(f"خطأ في التحقق: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    verify_mysql_only()
