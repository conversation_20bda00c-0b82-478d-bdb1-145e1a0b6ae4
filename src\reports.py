from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QVBox<PERSON><PERSON><PERSON>, QHBoxLayout,
                               QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
                               QHeaderView, QLabel, QMessageBox, QDateEdit, QDialog,
                               QTabWidget, QGroupBox, QComboBox, QSizePolicy, QFileDialog,
                               QRadioButton, QButtonGroup, QToolBar, QAction, QCheckBox, QStyle)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QFont, QIcon, QPixmap
import sqlite3
from datetime import datetime
import numpy as np
import random
import arabic_reshaper
from bidi.algorithm import get_display
import os

# ملاحظة: لاستخدام الرسومات البيانية، يجب تثبيت مكتبة matplotlib
# يمكن تثبيتها باستخدام الأمر التالي في موجه الأوامر:
# pip install matplotlib

# تأكد من تثبيت المكتبات المطلوبة
try:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure
    import matplotlib.cm as cm
    from matplotlib.colors import LinearSegmentedColormap
    # تعريف نمط ألوان مخصص للرسومات البيانية
    colors = [(0.2, 0.3, 0.6), (0.4, 0.5, 0.8), (0.6, 0.7, 0.9), (0.8, 0.9, 1.0)]
    custom_cmap = LinearSegmentedColormap.from_list('custom_blue', colors, N=100)
    plt.style.use('seaborn-v0_8-whitegrid')  # استخدام نمط حديث للرسومات
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"Error importing matplotlib: {e}")
    print("لاستخدام الرسومات البيانية، قم بتثبيت مكتبة matplotlib باستخدام الأمر: pip install matplotlib")
    FigureCanvas = None
    plt = None
    Figure = None
    NavigationToolbar = None
    MATPLOTLIB_AVAILABLE = False

# تعريف فئة بديلة في حالة عدم توفر matplotlib
class FallbackReportWidget(QWidget):
    """فئة بديلة لعرض التقارير في حالة عدم توفر matplotlib"""
    def __init__(self, parent=None):
        super(FallbackReportWidget, self).__init__(parent)
        self.layout = QVBoxLayout(self)
        
        # إنشاء تسمية توضيحية
        self.info_label = QLabel("لاستخدام الرسومات البيانية، قم بتثبيت مكتبة matplotlib باستخدام الأمر:\npip install matplotlib")
        self.info_label.setStyleSheet("color: #e74c3c; font-size: 14px; padding: 10px;")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.info_label)
        
        # إنشاء جدول لعرض البيانات
        self.table = QTableWidget()
        self.table.setStyleSheet("font-size: 12px;")
        self.layout.addWidget(self.table)
    
    def display_data_table(self, headers, data, title=""):
        """عرض البيانات في جدول"""
        # إضافة عنوان
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 10px;")
            title_label.setAlignment(Qt.AlignCenter)
            # إضافة العنوان في بداية التخطيط
            self.layout.insertWidget(0, title_label)
        
        # تهيئة الجدول
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # إضافة البيانات إلى الجدول
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, col, item)
        
        # تنسيق الجدول
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setAlternatingRowColors(True)
        self.table.setStyleSheet("""
            QTableWidget {
                alternate-background-color: #f2f2f2;
                background-color: white;
                border: 1px solid #ddd;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                font-weight: bold;
                padding: 6px;
                border: none;
            }
        """)

class ChartCanvas(FigureCanvas):
    """فئة لإنشاء الرسوم البيانية"""
    def __init__(self, parent=None, width=10, height=8, dpi=100):
        if not MATPLOTLIB_AVAILABLE:
            raise ImportError("مكتبة matplotlib غير متوفرة")
            
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        
        # تهيئة الرسم البياني
        super(ChartCanvas, self).__init__(self.fig)
        self.setParent(parent)
        
        # ضبط الإعدادات العربية
        plt.rcParams['axes.unicode_minus'] = False
        
        # تحسين مظهر الرسومات البيانية
        self.fig.patch.set_facecolor('#f8f9fa')
        self.axes.set_facecolor('#f8f9fa')
        self.axes.spines['top'].set_visible(False)
        self.axes.spines['right'].set_visible(False)
        self.axes.spines['left'].set_color('#cccccc')
        self.axes.spines['bottom'].set_color('#cccccc')
        
        # ضبط حجم الرسم البياني
        FigureCanvas.setSizePolicy(self, 
                                   QSizePolicy.Expanding, 
                                   QSizePolicy.Expanding)
        FigureCanvas.updateGeometry(self)
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي ليظهر بشكل صحيح في الرسم البياني"""
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text
    
    def plot_bar_chart(self, data, labels, title, xlabel, ylabel):
        """رسم مخطط أعمدة"""
        self.axes.clear()
        x = np.arange(len(labels))
        
        # إعادة تشكيل النصوص العربية
        arabic_labels = [self.reshape_arabic_text(label) for label in labels]
        arabic_title = self.reshape_arabic_text(title)
        arabic_xlabel = self.reshape_arabic_text(xlabel)
        arabic_ylabel = self.reshape_arabic_text(ylabel)
        
        # حساب النسب المئوية للبيانات
        total = sum(data) if sum(data) > 0 else 1
        percentages = [(value / total) * 100 for value in data]
        
        # استخدام ألوان متدرجة للأعمدة حسب النسب
        # استخدام ألوان أكثر تنوعاً وجاذبية
        colors = []
        for percentage in percentages:
            if percentage > 50:  # نسبة عالية
                colors.append('#3498db')  # أزرق
            elif percentage > 30:  # نسبة متوسطة
                colors.append('#2ecc71')  # أخضر
            elif percentage > 15:  # نسبة منخفضة
                colors.append('#f39c12')  # برتقالي
            else:  # نسبة منخفضة جداً
                colors.append('#e74c3c')  # أحمر
        
        bars = self.axes.bar(x, data, width=0.6, color=colors, edgecolor='#ffffff', linewidth=1)
        
        # إضافة قيم البيانات والنسب المئوية فوق الأعمدة
        for i, bar in enumerate(bars):
            height = bar.get_height()
            self.axes.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                    f'{int(height):,} ({percentages[i]:.1f}%)', ha='center', va='bottom', fontsize=9, color='#444444')
        
        # تحسين مظهر المخطط
        self.axes.set_xticks(x)
        self.axes.set_xticklabels(arabic_labels, fontsize=10, rotation=45, ha='right')
        self.axes.set_title(arabic_title, fontsize=14, fontweight='bold', pad=15)
        self.axes.set_xlabel(arabic_xlabel, fontsize=11, labelpad=10)
        self.axes.set_ylabel(arabic_ylabel, fontsize=11, labelpad=10)
        self.axes.grid(axis='y', linestyle='--', alpha=0.3)
        
        # إضافة إطار جميل للمخطط
        self.fig.tight_layout()
        self.draw()
    
    # تم حذف الدوال الأخرى والإبقاء فقط على مخطط الأعمدة

class ReportsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("التقارير")
        self.setGeometry(100, 100, 1200, 700)
        
        # التحقق من توفر مكتبة matplotlib
        self.matplotlib_available = MATPLOTLIB_AVAILABLE
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # تطبيق النمط
        self.apply_styles()
        
        # توسيط النافذة على الشاشة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        frame_geometry = self.frameGeometry()
        screen_center = self.screen().availableGeometry().center()
        frame_geometry.moveCenter(screen_center)
        self.move(frame_geometry.topLeft())
    

    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        central_widget.setLayout(main_layout)
        
        # عنوان الصفحة
        title_label = QLabel("تقارير النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء مدير التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setContentsMargins(0, 10, 0, 0)
        
        # إضافة التبويبات
        self.tab_widget.addTab(self.create_financial_report_tab(), "التقارير المالية")
        self.tab_widget.addTab(self.create_orders_report_tab(), "تقارير الطلبات")
        
        main_layout.addWidget(self.tab_widget)
    
    def create_orders_report_tab(self):
        """إنشاء تبويب تقارير الطلبات"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # مجموعة أدوات التحكم
        controls_group = QGroupBox("خيارات التقرير")
        controls_layout = QVBoxLayout()
        
        # قائمة منسدلة للفترة الزمنية
        date_layout = QHBoxLayout()
        date_period_label = QLabel("الفترة الزمنية:")
        self.orders_period_combo = QComboBox()
        self.orders_period_combo.addItems([
            "اليوم",
            "الأسبوع",
            "الشهر"
        ])
        date_layout.addWidget(date_period_label)
        date_layout.addWidget(self.orders_period_combo)
        
        # نوع التقرير
        report_type_layout = QHBoxLayout()
        report_type_label = QLabel("نوع التقرير:")
        self.orders_report_type_combo = QComboBox()
        self.orders_report_type_combo.addItems([
            "تقرير الطلبات حسب الحالة",
            "تقرير الطلبات حسب المنطقة",
            "تقرير الطلبات الشهرية"
        ])
        report_type_layout.addWidget(report_type_label)
        report_type_layout.addWidget(self.orders_report_type_combo)
        
        # زر عرض التقرير
        view_report_btn = QPushButton("عرض التقرير")
        view_report_btn.clicked.connect(self.generate_orders_report)
        
        # إضافة كل شيء إلى مجموعة التحكم
        controls_layout.addLayout(date_layout)
        controls_layout.addLayout(report_type_layout)
        controls_layout.addWidget(view_report_btn)
        controls_group.setLayout(controls_layout)
        
        # إنشاء الرسم البياني أو الجدول البديل حسب توفر matplotlib
        if self.matplotlib_available:
            self.orders_chart = ChartCanvas()
            
            # تم إزالة شريط الأدوات
                
            # إضافة كل شيء إلى التبويب
            layout.addWidget(controls_group)
            layout.addWidget(self.orders_chart)
        else:
            # استخدام الفئة البديلة في حالة عدم توفر matplotlib
            self.orders_chart = FallbackReportWidget()
            
            # إضافة كل شيء إلى التبويب
            layout.addWidget(controls_group)
            layout.addWidget(self.orders_chart)
        
        tab.setLayout(layout)
        return tab
    

    
    def create_financial_report_tab(self):
        """إنشاء تبويب التقارير المالية"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # مجموعة أدوات التحكم
        controls_group = QGroupBox("خيارات التقرير")
        controls_layout = QVBoxLayout()
        
        # قائمة منسدلة للفترة الزمنية
        date_layout = QHBoxLayout()
        date_period_label = QLabel("الفترة الزمنية:")
        self.fin_period_combo = QComboBox()
        self.fin_period_combo.addItems([
            "اليوم",
            "الأسبوع",
            "الشهر"
        ])
        date_layout.addWidget(date_period_label)
        date_layout.addWidget(self.fin_period_combo)
        
        # نوع التقرير
        report_type_layout = QHBoxLayout()
        report_type_label = QLabel("نوع التقرير:")
        self.fin_report_type_combo = QComboBox()
        self.fin_report_type_combo.addItems([
            "تقرير المصروفات",
            "تقرير الإيرادات",
            "تقرير الأرباح والخسائر"
        ])
        report_type_layout.addWidget(report_type_label)
        report_type_layout.addWidget(self.fin_report_type_combo)
        
        # زر عرض التقرير
        view_report_btn = QPushButton("عرض التقرير")
        view_report_btn.clicked.connect(self.generate_financial_report)
        
        # إضافة كل شيء إلى مجموعة التحكم
        controls_layout.addLayout(date_layout)
        controls_layout.addLayout(report_type_layout)
        controls_layout.addWidget(view_report_btn)
        controls_group.setLayout(controls_layout)
        
        # إنشاء الرسم البياني أو الجدول البديل حسب توفر matplotlib
        if self.matplotlib_available:
            self.financial_chart = ChartCanvas()
            
            # تم إزالة شريط الأدوات
        else:
            # استخدام الفئة البديلة في حالة عدم توفر matplotlib
            self.financial_chart = FallbackReportWidget()
        
        # تم إزالة خيارات العرض بناءً على طلب المستخدم
        
        # إضافة كل شيء إلى التبويب
        layout.addWidget(controls_group)
        layout.addWidget(self.financial_chart)
        tab.setLayout(layout)
        
        return tab
    
    def generate_orders_report(self):
        """توليد تقرير الطلبات"""
        try:
            report_type = self.orders_report_type_combo.currentText()
            period_type = self.orders_period_combo.currentText()
            
            # تحديد الفترة الزمنية بناءً على الاختيار
            current_date = QDate.currentDate()
            if period_type == "اليوم":
                # استخدام اليوم الحالي فقط
                from_date = current_date.toString("yyyy-MM-dd")
                to_date = current_date.toString("yyyy-MM-dd")
                period_label = "اليوم"
            elif period_type == "الأسبوع":
                # استخدام الأسبوع الحالي
                # الحصول على يوم الأسبوع الحالي (1=الاثنين، 7=الأحد)
                day_of_week = current_date.dayOfWeek()
                # حساب تاريخ بداية الأسبوع (الأحد)
                days_to_sunday = 7 if day_of_week == 7 else day_of_week
                start_of_week = current_date.addDays(-days_to_sunday + 1)
                from_date = start_of_week.toString("yyyy-MM-dd")
                to_date = start_of_week.addDays(6).toString("yyyy-MM-dd")
                period_label = "الأسبوع"
            else:  # الشهر
                # استخدام الشهر الحالي
                from_date = QDate(current_date.year(), current_date.month(), 1).toString("yyyy-MM-dd")
                to_date = current_date.toString("yyyy-MM-dd")
                period_label = "الشهر"
            
            # بيانات تجريبية للرسم البياني مع ربطها بالفترة الزمنية
            if report_type == "تقرير الطلبات حسب الحالة":
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    data = [45, 20, 30, 10]
                elif period_type == "الشهر":
                    data = [120, 45, 78, 25]
                else:  # الأسبوع
                    data = [1200, 450, 780, 250]
                    
                labels = ["مكتملة", "قيد التنفيذ", "جديدة", "ملغاة"]
                title = f"توزيع الطلبات حسب الحالة - {period_label}"
                xlabel = "الحالة"
                ylabel = "عدد الطلبات (دينار عراقي)"
                if self.matplotlib_available:
                    self.orders_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["الحالة", "عدد الطلبات (دينار عراقي)"]
                    table_data = [[label, value] for label, value in zip(labels, data)]
                    self.orders_chart.display_data_table(headers, table_data, title)
            elif report_type == "تقرير الطلبات حسب المنطقة":
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    data = [30, 25, 18, 15, 10, 5]
                elif period_type == "الشهر":
                    data = [85, 65, 45, 35, 25, 15]
                else:  # الأسبوع
                    data = [850, 650, 450, 350, 250, 150]
                    
                labels = ["الرياض", "جدة", "الدمام", "مكة", "المدينة", "تبوك"]
                title = f"توزيع الطلبات حسب المنطقة - {period_label}"
                xlabel = "المنطقة"
                ylabel = "عدد الطلبات (دينار عراقي)"
                if self.matplotlib_available:
                    self.orders_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["المنطقة", "عدد الطلبات (دينار عراقي)"]
                    table_data = [[label, value] for label, value in zip(labels, data)]
                    self.orders_chart.display_data_table(headers, table_data, title)
            else:  # تقرير الطلبات الشهرية
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    # عرض أيام الأسبوع السبعة
                    data = [20, 25, 22, 30, 35, 40, 28]
                    labels = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
                elif period_type == "الأسبوع":
                    # عرض أربعة أسابيع متتالية
                    data = [65, 85, 72, 90]
                    labels = ["الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث", "الأسبوع الرابع"]
                else:  # الشهر
                    # عرض الأشهر
                    data = [650, 850, 720, 900, 950, 1100, 800, 950, 1000, 1200, 1100, 950]
                    labels = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                title = f"عدد الطلبات الشهرية - {period_label}"
                xlabel = "الفترة"
                ylabel = "عدد الطلبات (دينار عراقي)"
                if self.matplotlib_available:
                    self.orders_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["الفترة", "عدد الطلبات (دينار عراقي)"]
                    table_data = [[month, count] for month, count in zip(labels, data)]
                    self.orders_chart.display_data_table(headers, table_data, title)
            
            QMessageBox.information(self, "تنبيه", "تم عرض بيانات تجريبية في الرسم البياني")
            
        except Exception as e:
            print(f"خطأ في توليد تقرير الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء توليد التقرير")
    
    
    def generate_financial_report(self):
        """توليد التقرير المالي"""
        try:
            report_type = self.fin_report_type_combo.currentText()
            period_type = self.fin_period_combo.currentText()
            
            # تحديد الفترة الزمنية بناءً على الاختيار
            current_date = QDate.currentDate()
            if period_type == "اليوم":
                # استخدام اليوم الحالي فقط
                from_date = current_date.toString("yyyy-MM-dd")
                to_date = current_date.toString("yyyy-MM-dd")
                period_label = "اليوم"
            elif period_type == "الأسبوع":
                # استخدام الأسبوع الحالي
                # الحصول على يوم الأسبوع الحالي (1=الاثنين، 7=الأحد)
                day_of_week = current_date.dayOfWeek()
                # حساب تاريخ بداية الأسبوع (الأحد)
                days_to_sunday = 7 if day_of_week == 7 else day_of_week
                start_of_week = current_date.addDays(-days_to_sunday + 1)
                from_date = start_of_week.toString("yyyy-MM-dd")
                to_date = start_of_week.addDays(6).toString("yyyy-MM-dd")
                period_label = "الأسبوع"
            else:  # الشهر
                # استخدام الشهر الحالي
                from_date = QDate(current_date.year(), current_date.month(), 1).toString("yyyy-MM-dd")
                to_date = current_date.toString("yyyy-MM-dd")
                period_label = "الشهر"
            
            # بيانات تجريبية للرسم البياني مع ربطها بالفترة الزمنية
            if report_type == "تقرير المصروفات":
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    # عرض أيام الأسبوع السبعة
                    data = [5000, 2500, 1800, 1200, 800, 500, 1500]
                    labels = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
                elif period_type == "الأسبوع":
                    # عرض أربعة أسابيع متتالية
                    data = [15000, 8000, 12000, 9000]
                    labels = ["الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث", "الأسبوع الرابع"]
                else:  # الشهر
                    # عرض الأشهر
                    data = [150000, 95000, 65000, 45000, 30000, 25000, 35000, 40000, 55000, 70000, 85000, 90000]
                    labels = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                title = f"المصروفات الشهرية - {period_label}"
                xlabel = "الفترة"
                ylabel = "المبلغ (دينار عراقي)"
                if self.matplotlib_available:
                    # استخدام مخطط الأعمدة فقط
                    self.financial_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["الفترة", "المبلغ (دينار عراقي)"]
                    table_data = [[label, value] for label, value in zip(labels, data)]
                    self.financial_chart.display_data_table(headers, table_data, title)
                
            elif report_type == "تقرير الإيرادات":
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    # عرض أيام الأسبوع السبعة
                    data = [8000, 5500, 3500, 2000, 1500, 1000, 4000]
                    labels = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
                elif period_type == "الأسبوع":
                    # عرض أربعة أسابيع متتالية
                    data = [25000, 22000, 28000, 20000]
                    labels = ["الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث", "الأسبوع الرابع"]
                else:  # الشهر
                    # عرض الأشهر
                    data = [300000, 220000, 180000, 120000, 80000, 60000, 90000, 110000, 150000, 200000, 250000, 280000]
                    labels = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                title = f"الإيرادات الشهرية - {period_label}"
                xlabel = "الفترة"
                ylabel = "المبلغ (دينار عراقي)"
                
                if self.matplotlib_available:
                    # استخدام مخطط الأعمدة فقط
                    self.financial_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["الفترة", "المبلغ (دينار عراقي)"]
                    table_data = [[label, value] for label, value in zip(labels, data)]
                    self.financial_chart.display_data_table(headers, table_data, title)
                
            else:  # تقرير الأرباح والخسائر
                # تعديل البيانات حسب الفترة الزمنية
                if period_type == "اليوم":
                    # عرض أيام الأسبوع السبعة
                    data = [3000, 5000, 6000, 4000, 7000, 8000, 5500]
                    labels = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
                elif period_type == "الأسبوع":
                    # عرض أربعة أسابيع متتالية
                    data = [10000, 14000, 16000, 11000]
                    labels = ["الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث", "الأسبوع الرابع"]
                else:  # الشهر
                    # عرض الأشهر
                    data = [150000, 125000, 170000, 140000, 190000, 210000, 180000, 160000, 200000, 220000, 240000, 230000]
                    labels = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                title = f"صافي الربح الشهري - {period_label}"
                xlabel = "الفترة"
                ylabel = "المبلغ (دينار عراقي)"
                
                if self.matplotlib_available:
                    # استخدام مخطط الأعمدة فقط
                    self.financial_chart.plot_bar_chart(data, labels, title, xlabel, ylabel)
                else:
                    # عرض البيانات في جدول
                    headers = ["الفترة", "المبلغ (دينار عراقي)"]
                    table_data = [[label, value] for label, value in zip(labels, data)]
                    self.financial_chart.display_data_table(headers, table_data, title)
            
            QMessageBox.information(self, "تنبيه", "تم عرض بيانات تجريبية في الرسم البياني")
            
        except Exception as e:
            print(f"خطأ في توليد التقرير المالي: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء توليد التقرير")
    
    # تم حذف دالة get_selected_chart_type لأنها لم تعد ضرورية بعد الاقتصار على مخطط الأعمدة فقط
    
    def export_report(self):
        """تصدير التقرير الحالي"""
        try:
            options = QFileDialog.Options()
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير", "", 
                "صور PNG (*.png);;صور JPEG (*.jpg);;ملفات PDF (*.pdf)", 
                options=options
            )
            
            if not file_path:
                return
            
            # تحديد نوع التصدير بناءً على امتداد الملف
            current_tab_index = self.tab_widget.currentIndex()
            if current_tab_index == 0:
                chart = self.financial_chart
            elif current_tab_index == 1:
                chart = self.orders_chart
            elif current_tab_index == 2:
                chart = self.employees_chart
            else:
                return
            
            # حفظ الرسم البياني كصورة
            if file_path.endswith('.png') or file_path.endswith('.jpg'):
                chart.fig.savefig(file_path, dpi=300, bbox_inches='tight')
            # حفظ الرسم البياني كملف PDF
            elif file_path.endswith('.pdf'):
                chart.fig.savefig(file_path, format='pdf', bbox_inches='tight')
            
            QMessageBox.information(self, "تم التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
            
        except Exception as e:
            print(f"خطأ في تصدير التقرير: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تصدير التقرير")
    
    def print_report(self):
        """طباعة التقرير الحالي"""
        try:
            current_tab_index = self.tab_widget.currentIndex()
            if current_tab_index == 0:
                chart = self.financial_chart
            elif current_tab_index == 1:
                chart = self.orders_chart
            elif current_tab_index == 2:
                chart = self.employees_chart
            else:
                return
            
            # استخدام وظيفة الطباعة المدمجة في matplotlib
            chart.fig.canvas.print_figure("temp_print.png", dpi=300)
            QMessageBox.information(self, "طباعة", "تم إرسال التقرير إلى الطابعة")
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء طباعة التقرير")
    
    def refresh_data(self):
        """تحديث بيانات التقرير الحالي"""
        try:
            current_tab_index = self.tab_widget.currentIndex()
            if current_tab_index == 0:
                self.generate_financial_report()
            elif current_tab_index == 1:
                self.generate_orders_report()
            elif current_tab_index == 2:
                self.generate_employees_report()
            
            QMessageBox.information(self, "تحديث", "تم تحديث بيانات التقرير بنجاح")
            
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث البيانات")
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
            QTableWidget {
                background-color: white;
                gridline-color: #dcdcdc;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #e8f0fe;
                color: black;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-size: 12px;
                font-weight: bold;
            }
            QDateEdit, QComboBox {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
            }
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                background-color: #f5f6fa;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
