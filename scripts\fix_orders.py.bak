#!/usr/bin/env python
# -*- coding: utf-8 -*-
# هذا السكريبت يقوم بإصلاح وظيفة نقل الطلبات في ملف orders.py

import os
import sys
import re

# أضف المجلد الرئيسي للمشروع إلى مسارات البحث عن الحزم
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def create_updated_orders_file():
    # إنشاء ملف orders.py محدث بوظيفة نقل الطلبات المحدثة
    # قراءة ملف orders.py الحالي
    orders_file = os.path.join(project_root, 'src', 'orders.py')
    with open(orders_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # نسخة احتياطية من الملف الأصلي
    backup_file = orders_file + '.bak'
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"تم عمل نسخة احتياطية من الملف الأصلي في: {backup_file}")
    
    # البحث عن أي وظيفة باسم transfer_to_delegate وحذفها
    pattern = r'\s*def\s+transfer_to_delegate\s*\(self\)\s*:.*?(?=\s*def\s+|$)'
    content_without_transfer = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # التأكد من أن الملف الجديد يحتوي على حرف الأسطر المناسب
    if not content_without_transfer.endswith('\n'):
        content_without_transfer += '\n'
    
    # إضافة الوظيفة المحدثة
    updated_content = content_without_transfer + """
    def transfer_to_delegate(self):
        # نقل الطلبات المحددة إلى المندوب
        try:
            # الحصول على أرقام الطلبات المحددة
            selected_orders = []
            for row in range(self.orders_table.rowCount()):
                checkbox = self.orders_table.cellWidget(row, 0).findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    order_id = int(self.orders_table.item(row, 1).text())
                    selected_orders.append(order_id)
            
            if not selected_orders:
                QMessageBox.warning(self, "تحذير", "لم يتم تحديد أي طلبات")
                return
                
            # فتح مربع حوار بسيط لإدخال اسم المندوب الجديد
            dialog = QDialog(self)
            dialog.setWindowTitle("إدخال اسم المندوب")
            dialog.setMinimumWidth(350)
            dialog.setLayoutDirection(Qt.RightToLeft)  # ضبط اتجاه الواجهة من اليمين لليسار
            
            layout = QVBoxLayout(dialog)
            
            # إضافة نص توضيحي
            instruction_label = QLabel("أدخل اسم المندوب الذي تريد نقل الطلبات إليه:")
            instruction_label.setStyleSheet("font-weight: bold;")
            layout.addWidget(instruction_label)
            
            # حقل إدخال اسم المندوب الجديد
            delegate_name_input = QLineEdit()
            delegate_name_input.setPlaceholderText("اسم المندوب")
            delegate_name_input.setStyleSheet('QLineEdit { padding: 8px; border: 1px solid #dcdde1; border-radius: 5px; font-size: 11pt; margin: 5px 0px; }')
            layout.addWidget(delegate_name_input)
            
            # أزرار الموافقة والإلغاء
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            buttons.button(QDialogButtonBox.Ok).setText("موافق")
            buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
            layout.addWidget(buttons)
            
            # تنفيذ الإجراء عند قبول الحوار
            if dialog.exec_() == QDialog.Accepted:
                representative_name = delegate_name_input.text().strip()
                
                # التحقق من إدخال اسم المندوب
                if not representative_name:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المندوب")
                    return
                
                # نقل الطلبات إلى نافذة استلام المندوبين باستخدام اسم المندوب الجديد
                try:
                    conn = self.db.get_connection()
                    cursor = conn.cursor()
                    
                    # نقل الطلبات إلى جدول delegate_orders
                    for order_id in selected_orders:
                        # الحصول على بيانات الطلب من جدول orders
                        cursor.execute("""
                            SELECT id, phone, carpet_count, total_price, receipt_number, phone2, address
                            FROM orders WHERE id = ?
                        """, (order_id,))
                        order_data = cursor.fetchone()
                        
                        if order_data:
                            # إضافة الطلب إلى جدول delegate_orders
                            _, phone, carpet_count, total_price, receipt_number, phone2, address = order_data
                            customer_name = phone  # اسم العميل هو رقم الهاتف
                            phone_number = phone if phone else phone2
                            lengths = ""  # قيمة فارغة للطول
                            
                            # إضافة الطلب إلى جدول delegate_orders
                            cursor.execute("""
                                INSERT INTO delegate_orders 
                                (receipt_number, customer_name, phone_number, carpet_count, lengths, 
                                total_price, representative_name, order_date, status)
                                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
                            """, (receipt_number, customer_name, phone_number, carpet_count, lengths, 
                                   total_price, representative_name, "قيد الاستلام"))
                            
                            # تحديث حالة الطلب في جدول orders
                            cursor.execute("""
                                UPDATE orders 
                                SET status = 'تم النقل للمندوب',
                                    delegate_id = (SELECT id FROM delegates WHERE name = ? LIMIT 1)
                                WHERE id = ?
                            """, (representative_name, order_id))
                    
                    conn.commit()
                    conn.close()
                    
                    # تحديث الجدول
                    self.load_orders()
                    
                    # إعادة تطبيق التصفية
                    if self.search_input.text().strip():
                        self.filter_orders(self.search_input.text())
                    
                    QMessageBox.information(self, "نجاح", f"تم نقل {len(selected_orders)} طلب إلى المندوب {representative_name} بنجاح")
                
                except Exception as e:
                    print(f"خطأ في نقل الطلبات: {str(e)}")
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء نقل الطلبات")
        
        except Exception as e:
            print(f"خطأ في نقل الطلبات: {str(e)}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء نقل الطلبات")
    """
    
    # حفظ الملف المحدث
    with open(orders_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"تم تحديث ملف {orders_file} بنجاح!")

if __name__ == "__main__":
    create_updated_orders_file()
