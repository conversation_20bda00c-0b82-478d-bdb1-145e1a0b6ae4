from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                           QMessageBox, QGroupBox, QFormLayout, QCheckBox)
from PyQt5.QtCore import Qt
import sqlite3
import sys

class UsersWindow(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_database()
        self.init_ui()

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.conn = sqlite3.connect('carpet_cleaning.db')
            self.cursor = self.conn.cursor()
        except Exception as e:
            sys.stdout.buffer.write(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}\n".encode('utf-8'))
            raise

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المستخدمين")
        self.setFixedSize(800, 600)
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f6fa;
                font-family: 'Arial';
                font-size: 14px;
            }
            QGroupBox {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                margin-top: 1em;
                padding-top: 10px;
            }
            QGroupBox::title {
                color: #2c3e50;
                padding: 0 5px;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # مجموعة إضافة مستخدم جديد
        add_user_group = QGroupBox("إضافة مستخدم جديد")
        add_user_layout = QFormLayout(add_user_group)
        
        self.username_input = QLineEdit()
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        
        add_user_layout.addRow("اسم المستخدم:", self.username_input)
        add_user_layout.addRow("كلمة المرور:", self.password_input)
        
        add_btn = QPushButton("إضافة مستخدم")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        add_btn.clicked.connect(self.add_user)
        add_user_layout.addRow("", add_btn)
        
        layout.addWidget(add_user_group)
        
        # جدول المستخدمين
        users_group = QGroupBox("قائمة المستخدمين")
        users_layout = QVBoxLayout(users_group)
        
        # إضافة مربع تحديد وإلغاء تحديد وزر حذف
        selection_layout = QHBoxLayout()
        
        self.select_all_checkbox = QCheckBox("تحديد الكل")
        self.select_all_checkbox.clicked.connect(self.toggle_select_all)
        selection_layout.addWidget(self.select_all_checkbox)
        
        delete_selected_btn = QPushButton("حذف المحدد")
        delete_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_selected_btn.clicked.connect(self.delete_selected_users)
        selection_layout.addWidget(delete_selected_btn)
        
        selection_layout.addStretch()
        users_layout.addLayout(selection_layout)
        
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)  # زيادة عدد الأعمدة لإضافة مربع التحديد
        self.users_table.setHorizontalHeaderLabels(["", "اسم المستخدم", "الصلاحية", ""])
        self.users_table.horizontalHeader().setStretchLastSection(True)
        self.users_table.setAlternatingRowColors(True)
        
        users_layout.addWidget(self.users_table)
        layout.addWidget(users_group)
        
        # تحميل المستخدمين
        self.load_users()

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            self.cursor.execute("SELECT username, role FROM users")
            users = self.cursor.fetchall()
            
            self.users_table.setRowCount(len(users))
            for i, (username, role) in enumerate(users):
                # إضافة مربع تحديد
                checkbox_item = QTableWidgetItem()
                checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                checkbox_item.setCheckState(Qt.Unchecked)
                self.users_table.setItem(i, 0, checkbox_item)
                
                self.users_table.setItem(i, 1, QTableWidgetItem(username))
                self.users_table.setItem(i, 2, QTableWidgetItem(role))
                
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, u=username: self.delete_user(u))
                self.users_table.setCellWidget(i, 3, delete_btn)
            
            self.users_table.resizeColumnsToContents()
        except Exception as e:
            sys.stdout.buffer.write(f"خطأ في تحميل المستخدمين: {str(e)}\n".encode('utf-8'))

    def toggle_select_all(self):
        """تحديد أو إلغاء تحديد جميع المستخدمين"""
        check_state = Qt.Checked if self.select_all_checkbox.isChecked() else Qt.Unchecked
        for row in range(self.users_table.rowCount()):
            self.users_table.item(row, 0).setCheckState(check_state)

    def delete_selected_users(self):
        """حذف المستخدمين المحددين"""
        selected_users = []
        for row in range(self.users_table.rowCount()):
            if self.users_table.item(row, 0).checkState() == Qt.Checked:
                username = self.users_table.item(row, 1).text()
                if username != 'admin':  # لا يمكن حذف المستخدم الرئيسي
                    selected_users.append(username)
        
        if not selected_users:
            QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مستخدمين للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد",
                                   f"هل أنت متأكد من حذف {len(selected_users)} مستخدم؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                for username in selected_users:
                    self.cursor.execute("DELETE FROM users WHERE username = ?", (username,))
                
                self.conn.commit()
                self.load_users()
                self.select_all_checkbox.setChecked(False)
                sys.stdout.buffer.write(f"تم حذف {len(selected_users)} مستخدم بنجاح\n".encode('utf-8'))
            except Exception as e:
                sys.stdout.buffer.write(f"خطأ في حذف المستخدمين: {str(e)}\n".encode('utf-8'))

    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()
            
            if not username or not password:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال اسم المستخدم وكلمة المرور")
                return
            
            self.cursor.execute("INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                              (username, password, 'user'))
            self.conn.commit()
            
            self.username_input.clear()
            self.password_input.clear()
            self.load_users()
            
            sys.stdout.buffer.write("تم إضافة المستخدم بنجاح\n".encode('utf-8'))
        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "تنبيه", "اسم المستخدم موجود مسبقاً")
        except Exception as e:
            sys.stdout.buffer.write(f"خطأ في إضافة المستخدم: {str(e)}\n".encode('utf-8'))

    def delete_user(self, username):
        """حذف مستخدم"""
        try:
            if username == 'admin':
                QMessageBox.warning(self, "تنبيه", "لا يمكن حذف المستخدم الرئيسي")
                return
            
            reply = QMessageBox.question(self, "تأكيد",
                                       f"هل أنت متأكد من حذف المستخدم {username}؟",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.cursor.execute("DELETE FROM users WHERE username = ?", (username,))
                self.conn.commit()
                self.load_users()
                sys.stdout.buffer.write("تم حذف المستخدم بنجاح\n".encode('utf-8'))
        except Exception as e:
            sys.stdout.buffer.write(f"خطأ في حذف المستخدم: {str(e)}\n".encode('utf-8'))

    def closeEvent(self, event):
        """إغلاق الاتصال بقاعدة البيانات عند إغلاق النافذة"""
        self.conn.close()
        event.accept()
