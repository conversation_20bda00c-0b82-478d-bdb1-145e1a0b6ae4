"""
إصلاح هيكل قاعدة البيانات MySQL
"""

import pymysql
from utils.config import load_config

def fix_mysql_schema():
    """إضافة الأعمدة المفقودة في قاعدة البيانات MySQL"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()
        
        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }
        
        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")
        
        # إضافة الأعمدة المفقودة في جدول orders
        
        # التحقق من وجود عمود has_delivery_fee
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'has_delivery_fee'")
        if not cursor.fetchone():
            print("إضافة عمود has_delivery_fee إلى جدول orders")
            cursor.execute("ALTER TABLE orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
            print("تم إضافة عمود has_delivery_fee بنجاح")
        else:
            print("عمود has_delivery_fee موجود بالفعل في جدول orders")
        
        # التحقق من وجود عمود delivery_fee
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'delivery_fee'")
        if not cursor.fetchone():
            print("إضافة عمود delivery_fee إلى جدول orders")
            cursor.execute("ALTER TABLE orders ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00")
            print("تم إضافة عمود delivery_fee بنجاح")
        else:
            print("عمود delivery_fee موجود بالفعل في جدول orders")
        
        # التحقق من وجود عمود pickup_representative_id
        cursor.execute("SHOW COLUMNS FROM orders LIKE 'pickup_representative_id'")
        if not cursor.fetchone():
            print("إضافة عمود pickup_representative_id إلى جدول orders")
            cursor.execute("ALTER TABLE orders ADD COLUMN pickup_representative_id INT DEFAULT NULL")
            print("تم إضافة عمود pickup_representative_id بنجاح")
            
            # إضافة مفتاح أجنبي
            cursor.execute("""
                ALTER TABLE orders 
                ADD CONSTRAINT fk_orders_representatives 
                FOREIGN KEY (pickup_representative_id) 
                REFERENCES representatives(id) 
                ON DELETE SET NULL
            """)
            print("تم إضافة مفتاح أجنبي للعمود pickup_representative_id بنجاح")
        else:
            print("عمود pickup_representative_id موجود بالفعل في جدول orders")
        
        # حفظ التغييرات
        conn.commit()
        
        print("تم إصلاح هيكل قاعدة البيانات MySQL بنجاح")
        
        # إغلاق الاتصال
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"خطأ في إصلاح هيكل قاعدة البيانات MySQL: {str(e)}")
        return False

if __name__ == "__main__":
    fix_mysql_schema()
