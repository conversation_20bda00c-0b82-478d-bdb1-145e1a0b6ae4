#!/usr/bin/env python
"""
هذا السكريبت ينشئ جدول delegate_orders لربط الطلبات بالمندوبين
"""

import os
import sys
import sqlite3

# أضف المجلد الرئيسي للمشروع إلى مسارات البحث عن الحزم
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# قم باستيراد قاعدة البيانات
from db.models import Database

def create_delegate_orders_table():
    """إنشاء جدول delegate_orders لربط الطلبات بالمندوبين"""
    try:
        # الاتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()

        # إضافة عمود delegate_name إلى جدول orders إذا لم يكن موجوداً
        cursor.execute("""
        ALTER TABLE orders ADD COLUMN delegate_name TEXT;
        """)

        # إنشاء جدول delegate_orders إذا لم يكن موجودًا
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS delegate_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            delegate_id INTEGER NOT NULL,
            delegate_name TEXT,
            status TEXT NOT NULL DEFAULT 'قيد الاستلام',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (delegate_id) REFERENCES delegates (id)
        )
        """)

        # إنشاء فهرس لتحسين الأداء
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_delegate_orders_order_id ON delegate_orders (order_id)
        """)
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_delegate_orders_delegate_id ON delegate_orders (delegate_id)
        """)
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_delegate_orders_delegate_name ON delegate_orders (delegate_name)
        """)

        # التزام بالتغييرات
        conn.commit()
        print("تم تحديث هيكل قاعدة البيانات بنجاح")

    except sqlite3.Error as e:
        print(f"خطأ في تحديث هيكل قاعدة البيانات: {e}")
    finally:
        # إغلاق الاتصال
        if conn:
            conn.close()

if __name__ == "__main__":
    create_delegate_orders_table()
