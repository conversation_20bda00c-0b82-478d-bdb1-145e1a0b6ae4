import os
import time

# استخدام PyMySQL بدلاً من mysql-connector-python
try:
    import pymysql
    from pymysql import Error
    USING_PYMYSQL = True
except ImportError:
    # الرجوع إلى mysql-connector-python إذا لم يكن PyMySQL متاحًا
    import mysql.connector
    from mysql.connector import Error
    USING_PYMYSQL = False
from sqlalchemy import create_engine, Column, Integer, String, Float, ForeignKey, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import datetime
from utils.config import load_config

Base = declarative_base()

class Order(Base):
    """نموذج الطلبات"""
    __tablename__ = 'orders'

    id = Column(Integer, primary_key=True)
    phone = Column(String, nullable=False)
    phone2 = Column(String)  # رقم الهاتف الثاني
    address = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)
    receipt_number = Column(String)  # رقم الوصل
    total_area = Column(Float, default=0)  # المساحة الكلية
    total_price = Column(Float, default=0)  # السعر الإجمالي
    notes = Column(String)  # ملاحظات
    delegate_id = Column(Integer, ForeignKey('delegates.id'), nullable=True)  # معرف المندوب
    delegate_name = Column(String)  # اسم المندوب
    status = Column(String, default='جديد')  # حالة الطلب

    # العلاقة مع أبعاد السجاد
    carpet_dimensions = relationship("CarpetDimension", back_populates="order", cascade="all, delete-orphan")

class CarpetDimension(Base):
    """نموذج أبعاد السجاد"""
    __tablename__ = 'carpet_dimensions'

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id', ondelete='CASCADE'), nullable=False)
    length = Column(Float, nullable=False)
    width = Column(Float, nullable=False)
    total_area = Column(Float, nullable=False)

    # العلاقة مع الطلب
    order = relationship("Order", back_populates="carpet_dimensions")

class Settings(Base):
    """نموذج الإعدادات"""
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    price_per_meter = Column(Float, default=1000.0)
    blanket_price = Column(Float, default=5000.0)  # إضافة حقل سعر البطانية
    delivery_price = Column(Float, default=2000.0)  # إضافة حقل سعر التوصيل
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Employee(Base):
    """نموذج الموظفين"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    phone = Column(String, nullable=False)
    salary = Column(Float, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)

class Expense(Base):
    """نموذج المصروفات"""
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    item = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)

class Delegate(Base):
    """نموذج المندوبين"""
    __tablename__ = 'delegates'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

class DelegateOrder(Base):
    """نموذج طلبات المندوبين"""
    __tablename__ = 'delegate_orders'

    id = Column(Integer, primary_key=True)
    receipt_number = Column(String)
    phone_number = Column(String, nullable=False)
    area = Column(String)
    carpet_count = Column(Integer, nullable=False, default=0)
    blanket_count = Column(Integer, default=0)
    total_price = Column(Float, nullable=False, default=0)
    order_id = Column(Integer, ForeignKey('orders.id'))
    delegate_id = Column(Integer, ForeignKey('delegates.id'))
    representative_name = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Database:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not Database._initialized:
            # تحميل إعدادات البرنامج
            self.config = load_config()
            self.db_type = 'mysql'  # فقط MySQL - لا دعم لـ SQLite

            # إعدادات الاتصال بـ MySQL
            self.mysql_config = {
                'host': self.config.get('db_host', 'localhost'),
                'user': self.config.get('db_user', 'carpet_user'),
                'password': self.config.get('db_password', '@#57111819752#@'),
                'database': self.config.get('db_name', 'carpet_cleaning_service'),
                'port': int(self.config.get('db_port', '3306')),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'raise_on_warnings': True
            }

            self.update_tables()  # تحديث هيكل الجداول
            Database._initialized = True

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات MySQL فقط"""
        try:
            # تعديل إعدادات الاتصال لتجنب مشكلة access violation
            safe_config = {
                'host': self.mysql_config['host'],
                'user': self.mysql_config['user'],
                'password': self.mysql_config['password'],
                'database': self.mysql_config['database'],
                'port': self.mysql_config['port']
            }

            # اتصال بقاعدة بيانات MySQL
            if USING_PYMYSQL:
                # استخدام PyMySQL
                conn = pymysql.connect(**safe_config)
            else:
                # استخدام mysql-connector-python
                conn = mysql.connector.connect(**safe_config)
            return conn
        except Error as e:
            raise Exception(f"خطأ في الاتصال بقاعدة بيانات MySQL: {e}")
        except Exception as e:
            raise Exception(f"خطأ غير متوقع في الاتصال بقاعدة بيانات MySQL: {e}")

    def update_tables(self):
        """تحديث هيكل الجداول في MySQL"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود عمود has_delivery_fee في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'has_delivery_fee'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
            except Exception as e:
                pass

            # التحقق من وجود عمود delivery_fee في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'delivery_fee'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00")
            except Exception as e:
                pass

            # التحقق من وجود عمود pickup_representative_id في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'pickup_representative_id'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN pickup_representative_id INT DEFAULT NULL")
            except Exception as e:
                pass

            # التحقق من وجود عمود completion_date في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'completion_date'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN completion_date TIMESTAMP NULL DEFAULT NULL")
            except Exception as e:
                pass

            # التحقق من وجود عمود carpet_count في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'carpet_count'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN carpet_count INT DEFAULT 0")
            except Exception as e:
                pass

            # حفظ التغييرات
            conn.commit()

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
