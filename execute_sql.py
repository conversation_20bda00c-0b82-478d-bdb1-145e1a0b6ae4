"""
تنفيذ ملف SQL على قاعدة بيانات MySQL
"""

import pymysql
from utils.config import load_config

def execute_sql_file(sql_file):
    """تنفيذ ملف SQL على قاعدة بيانات MySQL"""
    try:
        # تحميل إعدادات البرنامج
        config = load_config()

        # إعدادات الاتصال بـ MySQL
        mysql_config = {
            'host': config.get('db_host', 'localhost'),
            'user': config.get('db_user', 'carpet_user'),
            'password': config.get('db_password', '@#57111819752#@'),
            'database': config.get('db_name', 'carpet_cleaning_service'),
            'port': int(config.get('db_port', '3306'))
        }

        print(f"محاولة الاتصال بـ MySQL باستخدام: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")

        # اتصال بقاعدة بيانات MySQL
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()

        print("تم الاتصال بقاعدة بيانات MySQL بنجاح")

        # قراءة ملف SQL
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql = f.read()

        # تنفيذ الملف بالكامل
        print(f"تنفيذ ملف SQL: {sql_file}")
        cursor.execute(sql)

        # حفظ التغييرات
        conn.commit()

        print("تم تنفيذ جميع الاستعلامات بنجاح")

        # إغلاق الاتصال
        cursor.close()
        conn.close()

        return True
    except Exception as e:
        print(f"خطأ في تنفيذ ملف SQL: {str(e)}")
        return False

if __name__ == "__main__":
    execute_sql_file("add_role_column.sql")
