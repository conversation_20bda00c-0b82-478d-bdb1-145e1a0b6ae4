#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from db.models import Database
    
    db = Database()
    conn = db.get_connection()
    cursor = conn.cursor()
    
    print("=== فحص سريع للبيانات ===")
    
    # عدد الطلبات
    cursor.execute("SELECT COUNT(*) FROM orders")
    orders_count = cursor.fetchone()[0]
    print(f"الطلبات: {orders_count}")
    
    # عدد المندوبين
    cursor.execute("SELECT COUNT(*) FROM delegates")
    delegates_count = cursor.fetchone()[0]
    print(f"المندوبين: {delegates_count}")
    
    # الطلبات حسب الحالة
    cursor.execute("SELECT status, COUNT(*) FROM orders GROUP BY status")
    statuses = cursor.fetchall()
    print("\nالطلبات حسب الحالة:")
    for status, count in statuses:
        print(f"  {status}: {count}")
    
    # أمثلة على الطلبات
    cursor.execute("SELECT id, phone, status, receipt_number FROM orders LIMIT 3")
    orders = cursor.fetchall()
    print(f"\nأمثلة على الطلبات:")
    for order in orders:
        print(f"  ID:{order[0]} - {order[1]} - {order[2]} - {order[3]}")
    
    conn.close()
    print("\n✅ البيانات متاحة!")
    
except Exception as e:
    print(f"خطأ: {e}")
    import traceback
    traceback.print_exc()
