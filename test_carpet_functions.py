#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لاختبار دوال أبعاد السجاد الجديدة
"""

import os
import sys

# إضافة المسار الجذر للمشروع إلى مسارات البحث
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_carpet_functions():
    """اختبار دوال أبعاد السجاد"""
    try:
        print("🧪 اختبار دوال أبعاد السجاد")
        print("=" * 40)
        
        from db.models import Database
        
        db = Database()
        
        # التحقق من وجود الدوال
        print("🔍 التحقق من وجود الدوال:")
        
        if hasattr(db, 'delete_carpet_dimensions'):
            print("✅ دالة delete_carpet_dimensions موجودة")
        else:
            print("❌ دالة delete_carpet_dimensions غير موجودة")
        
        if hasattr(db, 'add_carpet_dimension'):
            print("✅ دالة add_carpet_dimension موجودة")
        else:
            print("❌ دالة add_carpet_dimension غير موجودة")
        
        if hasattr(db, 'close_connection'):
            print("✅ دالة close_connection موجودة")
        else:
            print("❌ دالة close_connection غير موجودة")
        
        # اختبار إضافة أبعاد سجاد تجريبية
        print("\n➕ اختبار إضافة أبعاد سجاد:")
        
        # البحث عن طلب موجود
        orders = db.fetch_all("SELECT id FROM orders LIMIT 1")
        if orders:
            order_id = orders[0][0]
            print(f"استخدام الطلب رقم: {order_id}")
            
            # إضافة بعد سجادة تجريبي
            success = db.add_carpet_dimension(order_id, 2.5, 3.0)
            if success:
                print("✅ تم إضافة بعد السجادة بنجاح")
            else:
                print("❌ فشل في إضافة بعد السجادة")
            
            # التحقق من الإضافة
            dimensions = db.fetch_all("SELECT length, width, total_area FROM carpet_dimensions WHERE order_id = %s", (order_id,))
            print(f"أبعاد السجاد للطلب {order_id}:")
            for dim in dimensions:
                print(f"  الطول: {dim[0]}, العرض: {dim[1]}, المساحة: {dim[2]}")
            
            # اختبار حذف الأبعاد
            print(f"\n🗑️ اختبار حذف أبعاد السجاد للطلب {order_id}:")
            delete_success = db.delete_carpet_dimensions(order_id)
            if delete_success:
                print("✅ تم حذف أبعاد السجاد بنجاح")
            else:
                print("❌ فشل في حذف أبعاد السجاد")
            
            # التحقق من الحذف
            remaining_dimensions = db.fetch_all("SELECT COUNT(*) FROM carpet_dimensions WHERE order_id = %s", (order_id,))
            count = remaining_dimensions[0][0] if remaining_dimensions else 0
            print(f"عدد الأبعاد المتبقية: {count}")
            
        else:
            print("❌ لا توجد طلبات للاختبار")
        
        print("\n" + "=" * 40)
        print("✅ تم اختبار جميع الدوال بنجاح")
        print("✅ البرنامج جاهز لتحرير طلبات المندوبين")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_carpet_functions()
