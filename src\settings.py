from PyQt5.QtWidgets import (QDialog, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
                           QLabel, QLineEdit, QPushButton, QFormLayout, QTableWidget,
                           QTableWidgetItem, QFileDialog, QMessageBox, QComboBox, QFrame,
                           QHeaderView, QAction, QMainWindow, QMenuBar, QGroupBox, QCheckBox, QSpinBox,
                           QAbstractItemView, QTimeEdit, QListWidget, QListWidgetItem, QApplication)
from PyQt5.QtCore import Qt, QLocale, QTimer, QTime
from PyQt5.QtGui import QIntValidator, QFont
from db.models import Database
import os
import shutil
import locale
import sqlite3
import csv
import sys
from datetime import datetime
from utils.config import load_config, update_config
from utils.backup import create_backup, restore_backup, list_backups, delete_old_backups

class DelegateDialog(QDialog):
    """نافذة إضافة/تعديل مندوب"""
    def __init__(self, parent=None, delegate_data=None):
        super().__init__(parent)
        self.db = Database()
        self.delegate_data = delegate_data
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة مندوب جديد" if not self.delegate_data else "تعديل مندوب")
        self.setGeometry(200, 200, 400, 300)

        layout = QVBoxLayout()

        # حقل الاسم
        name_layout = QHBoxLayout()
        name_label = QLabel("الاسم:")
        self.name_input = QLineEdit()
        if self.delegate_data:
            self.name_input.setText(self.delegate_data[1])  # name
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input)

        # حقل رقم الهاتف
        phone_layout = QHBoxLayout()
        phone_label = QLabel("رقم الهاتف:")
        self.phone_input = QLineEdit()
        if self.delegate_data:
            self.phone_input.setText(self.delegate_data[2])  # phone
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone_input)

        # حقل المنطقة
        area_layout = QHBoxLayout()
        area_label = QLabel("المنطقة:")
        self.area_input = QLineEdit()
        if self.delegate_data:
            self.area_input.setText(self.delegate_data[3])  # area
        area_layout.addWidget(area_label)
        area_layout.addWidget(self.area_input)

        # أزرار
        button_layout = QHBoxLayout()
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_delegate)
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        # إضافة كل شيء إلى التخطيط الرئيسي
        layout.addLayout(name_layout)
        layout.addLayout(phone_layout)
        layout.addLayout(area_layout)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def save_delegate(self):
        """حفظ بيانات المندوب"""
        try:
            name = self.name_input.text().strip()
            phone = self.phone_input.text().strip()
            area = self.area_input.text().strip()

            if not name or not phone or not area:
                QMessageBox.warning(self, "خطأ", "جميع الحقول مطلوبة")
                return

            if self.delegate_data:  # تعديل مندوب موجود
                self.db.update_delegate(self.delegate_data[0], name, phone, area)
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات المندوب بنجاح")
            else:  # إضافة مندوب جديد
                delegate_id = self.db.add_delegate(name, phone, area)
                QMessageBox.information(self, "نجاح", "تم إضافة المندوب بنجاح")

            self.accept()

        except ValueError as e:
            QMessageBox.warning(self, "خطأ", str(e))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

class SettingsWindow(QMainWindow):
    """نافذة الإعدادات"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = Database()

        # تعيين عنوان النافذة
        self.setWindowTitle("إعدادات النظام")

        # تعيين حجم النافذة
        self.resize(800, 600)

        # تعيين النمط
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                background-color: #f5f6fa;
            }
            QTabBar::tab:selected {
                background-color: #2ecc71;
                color: white;
            }
        """)

        self.init_ui()

        # تأخير استدعاء setup_tab_order حتى يتم إنشاء جميع العناصر
        QTimer.singleShot(100, self.setup_tab_order)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء التبويبات
        self.tabs = QTabWidget()

        # إنشاء التبويبات المختلفة
        price_tab = self.create_price_tab()
        users_tab = self.create_users_tab()
        backup_tab = self.create_backup_tab()
        advanced_tab = self.create_advanced_tab()

        # إضافة التبويبات إلى النافذة
        self.tabs.addTab(price_tab, "الأسعار")
        self.tabs.addTab(users_tab, "المستخدمين")
        self.tabs.addTab(backup_tab, "النسخ الاحتياطي")
        self.tabs.addTab(advanced_tab, "إعدادات متقدمة")

        # إضافة التبويبات إلى النافذة
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tabs)

        # إنشاء ويدجت مركزي
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def create_price_tab(self):
        """إنشاء تبويب سعر المتر المربع"""
        price_tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة سعر المتر المربع
        price_group = QGroupBox("سعر المتر المربع")
        price_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        price_layout = QFormLayout()

        # حقل السعر الحالي
        self.current_price_label = QLabel()
        self.current_price_label.setStyleSheet("font-size: 14px;")
        price_layout.addRow("السعر الحالي:", self.current_price_label)

        # حقل السعر الجديد
        self.new_price_input = QLineEdit()
        self.new_price_input.setValidator(QIntValidator(1, 1000000))
        self.new_price_input.setPlaceholderText("أدخل السعر الجديد")
        self.new_price_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        price_layout.addRow("السعر الجديد:", self.new_price_input)

        # زر حفظ السعر
        save_button = QPushButton("حفظ السعر")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_button.clicked.connect(self.save_price)
        price_layout.addRow("", save_button)

        price_group.setLayout(price_layout)
        layout.addWidget(price_group)

        # مجموعة سعر البطانية
        blanket_group = QGroupBox("سعر البطانية")
        blanket_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        blanket_layout = QFormLayout()

        # حقل سعر البطانية الحالي
        self.current_blanket_price_label = QLabel()
        self.current_blanket_price_label.setStyleSheet("font-size: 14px;")
        blanket_layout.addRow("السعر الحالي:", self.current_blanket_price_label)

        # حقل سعر البطانية الجديد
        self.new_blanket_price_input = QLineEdit()
        self.new_blanket_price_input.setValidator(QIntValidator(1, 1000000))
        self.new_blanket_price_input.setPlaceholderText("أدخل سعر البطانية الجديد")
        self.new_blanket_price_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        blanket_layout.addRow("السعر الجديد:", self.new_blanket_price_input)

        # زر حفظ سعر البطانية
        save_blanket_button = QPushButton("حفظ سعر البطانية")
        save_blanket_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_blanket_button.clicked.connect(self.save_blanket_price)
        blanket_layout.addRow("", save_blanket_button)

        blanket_group.setLayout(blanket_layout)
        layout.addWidget(blanket_group)

        # مجموعة أسعار التوصيل
        delivery_group = QGroupBox("أسعار التوصيل")
        delivery_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        delivery_layout = QFormLayout()

        # حقل سعر التوصيل الحالي
        self.current_delivery_price_label = QLabel()
        self.current_delivery_price_label.setStyleSheet("font-size: 14px;")
        delivery_layout.addRow("السعر الحالي:", self.current_delivery_price_label)

        # حقل سعر التوصيل الجديد
        self.new_delivery_price_input = QLineEdit()
        self.new_delivery_price_input.setValidator(QIntValidator(1, 1000000))
        self.new_delivery_price_input.setPlaceholderText("أدخل سعر التوصيل الجديد")
        self.new_delivery_price_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        delivery_layout.addRow("السعر الجديد:", self.new_delivery_price_input)

        # زر حفظ سعر التوصيل
        save_delivery_button = QPushButton("حفظ سعر التوصيل")
        save_delivery_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        save_delivery_button.clicked.connect(self.save_delivery_price)
        delivery_layout.addRow("", save_delivery_button)

        delivery_group.setLayout(delivery_layout)
        layout.addWidget(delivery_group)

        layout.addStretch()

        price_tab.setLayout(layout)
        self.update_current_price()
        self.update_current_blanket_price()
        self.update_current_delivery_price()
        return price_tab

    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # نموذج إضافة مستخدم
        form_layout = QFormLayout()

        self.username_input = QLineEdit()
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)

        # ربط حدث Enter بإضافة المستخدم
        self.username_input.returnPressed.connect(lambda: self.password_input.setFocus())
        self.password_input.returnPressed.connect(lambda: self.user_type.setFocus())

        self.user_type = QComboBox()
        self.user_type.addItems(["مدير", "موظف"])

        form_layout.addRow("اسم المستخدم:", self.username_input)
        form_layout.addRow("كلمة المرور:", self.password_input)
        form_layout.addRow("الصلاحية:", self.user_type)

        # زر إضافة مستخدم
        add_user_btn = QPushButton("إضافة مستخدم")
        add_user_btn.clicked.connect(self.add_user)
        form_layout.addRow("", add_user_btn)

        # إضافة مربع تحديد وإلغاء تحديد وزر حذف
        selection_layout = QHBoxLayout()

        self.select_all_users_checkbox = QCheckBox("تحديد الكل")
        self.select_all_users_checkbox.clicked.connect(self.toggle_select_all_users)
        selection_layout.addWidget(self.select_all_users_checkbox)

        delete_selected_users_btn = QPushButton("حذف المحدد")
        delete_selected_users_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_selected_users_btn.clicked.connect(self.delete_selected_users)
        selection_layout.addWidget(delete_selected_users_btn)

        selection_layout.addStretch()

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)  # إضافة عمود لمربع التحديد
        self.users_table.setHorizontalHeaderLabels(["تحديد", "المعرف", "اسم المستخدم", "كلمة المرور", "الصلاحية"])
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setEditTriggers(QAbstractItemView.NoEditTriggers)  # منع التعديل المباشر

        # إضافة كل العناصر للتخطيط
        layout.addLayout(form_layout)
        layout.addLayout(selection_layout)
        layout.addWidget(self.users_table)

        # تحميل المستخدمين
        self.load_users()

        return tab

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_tab = QWidget()
        layout = QVBoxLayout()

        # تحميل الإعدادات الحالية
        config = load_config()

        # مجموعة إعدادات النسخ الاحتياطي التلقائي
        auto_backup_group = QGroupBox("النسخ الاحتياطي التلقائي")
        auto_backup_layout = QVBoxLayout()

        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup_checkbox = QCheckBox("تفعيل النسخ الاحتياطي التلقائي اليومي")
        self.auto_backup_checkbox.setChecked(config.get('auto_backup', True))
        self.auto_backup_checkbox.toggled.connect(lambda checked: update_config('auto_backup', checked))

        # وقت النسخ الاحتياطي
        time_layout = QHBoxLayout()
        time_label = QLabel("وقت النسخ الاحتياطي اليومي:")
        self.backup_time_edit = QTimeEdit()
        self.backup_time_edit.setDisplayFormat("hh:mm")

        # تعيين الوقت الحالي
        backup_time = config.get('backup_time', '00:00')
        hour, minute = map(int, backup_time.split(':'))
        self.backup_time_edit.setTime(QTime(hour, minute))

        self.backup_time_edit.timeChanged.connect(self.update_backup_time)
        time_layout.addWidget(time_label)
        time_layout.addWidget(self.backup_time_edit)

        # تفعيل النسخ الاحتياطي عند الخروج
        self.exit_backup_checkbox = QCheckBox("إنشاء نسخة احتياطية عند إغلاق البرنامج")
        self.exit_backup_checkbox.setChecked(config.get('exit_backup', True))
        self.exit_backup_checkbox.toggled.connect(lambda checked: update_config('exit_backup', checked))

        # إضافة العناصر إلى مجموعة النسخ الاحتياطي التلقائي
        auto_backup_layout.addWidget(self.auto_backup_checkbox)
        auto_backup_layout.addLayout(time_layout)
        auto_backup_layout.addWidget(self.exit_backup_checkbox)
        auto_backup_group.setLayout(auto_backup_layout)

        # مجموعة مجلد النسخ الاحتياطي
        backup_dir_group = QGroupBox("مجلد النسخ الاحتياطي")
        backup_dir_layout = QHBoxLayout()

        self.backup_dir_edit = QLineEdit()
        self.backup_dir_edit.setText(config.get('backup_dir', ''))
        self.backup_dir_edit.setReadOnly(True)

        browse_button = QPushButton("تصفح...")
        browse_button.clicked.connect(self.browse_backup_dir)

        backup_dir_layout.addWidget(self.backup_dir_edit)
        backup_dir_layout.addWidget(browse_button)
        backup_dir_group.setLayout(backup_dir_layout)

        # مجموعة النسخ الاحتياطية اليدوية
        manual_backup_group = QGroupBox("النسخ الاحتياطي اليدوي")
        manual_backup_layout = QVBoxLayout()

        # زر إنشاء نسخة احتياطية
        create_backup_button = QPushButton("إنشاء نسخة احتياطية الآن")
        create_backup_button.clicked.connect(self.create_manual_backup)

        # زر استعادة نسخة احتياطية
        restore_backup_button = QPushButton("استعادة نسخة احتياطية")
        restore_backup_button.clicked.connect(self.restore_from_backup)

        manual_backup_layout.addWidget(create_backup_button)
        manual_backup_layout.addWidget(restore_backup_button)
        manual_backup_group.setLayout(manual_backup_layout)

        # مجموعة النسخ الاحتياطية المتوفرة
        backups_group = QGroupBox("النسخ الاحتياطية المتوفرة")
        backups_layout = QVBoxLayout()

        self.backups_list = QListWidget()
        self.load_backups_list()

        refresh_backups_button = QPushButton("تحديث القائمة")
        refresh_backups_button.clicked.connect(self.load_backups_list)

        delete_old_backups_button = QPushButton("حذف النسخ الاحتياطية القديمة")
        delete_old_backups_button.clicked.connect(self.delete_old_backups)

        backups_layout.addWidget(self.backups_list)
        backups_layout.addWidget(refresh_backups_button)
        backups_layout.addWidget(delete_old_backups_button)
        backups_group.setLayout(backups_layout)

        # إضافة المجموعات إلى التخطيط الرئيسي
        layout.addWidget(auto_backup_group)
        layout.addWidget(backup_dir_group)
        layout.addWidget(manual_backup_group)
        layout.addWidget(backups_group)

        backup_tab.setLayout(layout)
        return backup_tab

    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        advanced_tab = QWidget()
        layout = QVBoxLayout()

        # تحميل الإعدادات الحالية
        config = load_config()

        # مجموعة إعدادات التحقق من أرقام الوصل
        receipt_group = QGroupBox("إعدادات التحقق من أرقام الوصل")
        receipt_layout = QVBoxLayout()

        # تفعيل التحقق من جميع أرقام الوصل
        self.check_all_receipts_checkbox = QCheckBox("التحقق من جميع أرقام الوصل عند بدء التشغيل")
        self.check_all_receipts_checkbox.setChecked(config.get('check_all_receipts', False))
        self.check_all_receipts_checkbox.toggled.connect(lambda checked: update_config('check_all_receipts', checked))

        # عدد الأيام للتحقق من أرقام الوصل الحديثة
        days_layout = QHBoxLayout()
        days_label = QLabel("عدد الأيام للتحقق من أرقام الوصل الحديثة:")
        self.receipt_check_days_spinbox = QSpinBox()
        self.receipt_check_days_spinbox.setMinimum(1)
        self.receipt_check_days_spinbox.setMaximum(365)
        self.receipt_check_days_spinbox.setValue(config.get('receipt_check_days', 7))
        self.receipt_check_days_spinbox.valueChanged.connect(lambda value: update_config('receipt_check_days', value))

        days_layout.addWidget(days_label)
        days_layout.addWidget(self.receipt_check_days_spinbox)

        # إضافة العناصر إلى مجموعة إعدادات التحقق من أرقام الوصل
        receipt_layout.addWidget(self.check_all_receipts_checkbox)
        receipt_layout.addLayout(days_layout)
        receipt_group.setLayout(receipt_layout)

        # إضافة المجموعات إلى التخطيط الرئيسي
        layout.addWidget(receipt_group)
        layout.addStretch()

        advanced_tab.setLayout(layout)
        return advanced_tab

    def setup_tab_order(self):
        """إعداد ترتيب التاب بين الحقول"""
        try:
            # التحقق من وجود العناصر قبل تعيين ترتيب التاب
            if hasattr(self, 'new_price_input') and hasattr(self, 'username_input'):
                self.setTabOrder(self.new_price_input, self.username_input)

            # المستخدمين
            if hasattr(self, 'username_input') and hasattr(self, 'password_input'):
                self.setTabOrder(self.username_input, self.password_input)

            if hasattr(self, 'password_input') and hasattr(self, 'user_type'):
                self.setTabOrder(self.password_input, self.user_type)
        except Exception as e:
            print(f"تحذير: حدث خطأ أثناء إعداد ترتيب التاب: {str(e)}")

    def update_current_price(self):
        """تحديث عرض السعر الحالي"""
        try:
            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT price_per_meter FROM settings LIMIT 1")
            result = cursor.fetchone()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            if result:
                # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
                if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                    current_price = result['price_per_meter']
                elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                    current_price = result[0]
                else:
                    print(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                    current_price = 0

                self.current_price_label.setText(f"{current_price:,.0f} دينار")
            else:
                self.current_price_label.setText("لم يتم تحديد السعر")
        except Exception as e:
            print(f"خطأ في تحديث السعر الحالي: {str(e)}")
            self.show_notification("حدث خطأ في تحديث السعر الحالي", error=True)

    def update_current_blanket_price(self):
        """تحديث عرض سعر البطانية الحالي"""
        try:
            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT blanket_price FROM settings LIMIT 1")
            result = cursor.fetchone()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            if result:
                # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
                if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                    current_price = result['blanket_price']
                elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                    current_price = result[0]
                else:
                    print(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                    current_price = 0

                self.current_blanket_price_label.setText(f"{current_price:,.0f} دينار")
            else:
                self.current_blanket_price_label.setText("لم يتم تحديد السعر")
        except Exception as e:
            print(f"خطأ في تحديث سعر البطانية الحالي: {str(e)}")
            self.show_notification("حدث خطأ في تحديث سعر البطانية الحالي", error=True)

    def update_current_delivery_price(self):
        """تحديث عرض سعر التوصيل الحالي"""
        try:
            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT delivery_price FROM settings LIMIT 1")
            result = cursor.fetchone()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            if result:
                # التعامل مع النتائج بطريقة متوافقة مع كل من MySQL و SQLite
                if isinstance(result, dict):  # في حالة استخدام row_factory مع SQLite
                    current_price = result['delivery_price']
                elif isinstance(result, tuple):  # في حالة MySQL أو SQLite بدون row_factory
                    current_price = result[0]
                else:
                    print(f"نوع غير معروف: {type(result)}, محتوى: {result}")
                    current_price = 0

                self.current_delivery_price_label.setText(f"{current_price:,.0f} دينار")
            else:
                self.current_delivery_price_label.setText("لم يتم تحديد السعر")
        except Exception as e:
            print(f"خطأ في تحديث سعر التوصيل الحالي: {str(e)}")
            self.show_notification("حدث خطأ في تحديث سعر التوصيل الحالي", error=True)

    def save_price(self):
        """حفظ السعر الجديد"""
        try:
            new_price = self.new_price_input.text().strip()
            if not new_price:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال السعر الجديد")
                return

            new_price = float(new_price)
            if new_price <= 0:
                QMessageBox.warning(self, "تنبيه", "يجب أن يكون السعر أكبر من صفر")
                return

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = "UPDATE settings SET price_per_meter = %s"
                print(f"استخدام استعلام MySQL: {query}")
            else:
                # استخدام ? للمعاملات مع SQLite
                query = "UPDATE settings SET price_per_meter = ?"
                print(f"استخدام استعلام SQLite: {query}")

            # تنفيذ الاستعلام
            cursor.execute(query, (new_price,))
            conn.commit()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            self.update_current_price()
            self.new_price_input.clear()
            QMessageBox.information(self, "نجاح", "تم حفظ السعر الجديد بنجاح")

        except ValueError:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم صحيح")
        except Exception as e:
            print(f"خطأ في حفظ السعر الجديد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ السعر الجديد: {str(e)}")

    def save_blanket_price(self):
        """حفظ سعر البطانية الجديد"""
        try:
            new_price = self.new_blanket_price_input.text().strip()
            if not new_price:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال سعر البطانية الجديد")
                return

            new_price = float(new_price)
            if new_price <= 0:
                QMessageBox.warning(self, "تنبيه", "يجب أن يكون السعر أكبر من صفر")
                return

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = "UPDATE settings SET blanket_price = %s"
                print(f"استخدام استعلام MySQL: {query}")
            else:
                # استخدام ? للمعاملات مع SQLite
                query = "UPDATE settings SET blanket_price = ?"
                print(f"استخدام استعلام SQLite: {query}")

            # تنفيذ الاستعلام
            cursor.execute(query, (new_price,))
            conn.commit()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            self.update_current_blanket_price()
            self.new_blanket_price_input.clear()
            QMessageBox.information(self, "نجاح", "تم حفظ سعر البطانية الجديد بنجاح")

        except ValueError:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم صحيح")
        except Exception as e:
            print(f"خطأ في حفظ سعر البطانية الجديد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ سعر البطانية الجديد: {str(e)}")

    def save_delivery_price(self):
        """حفظ سعر التوصيل الجديد"""
        try:
            new_price = self.new_delivery_price_input.text().strip()
            if not new_price:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال سعر التوصيل الجديد")
                return

            new_price = float(new_price)
            if new_price <= 0:
                QMessageBox.warning(self, "تنبيه", "يجب أن يكون السعر أكبر من صفر")
                return

            # تحديد نوع قاعدة البيانات
            db_type = getattr(self.db, 'db_type', 'sqlite')

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # بناء الاستعلام بناءً على نوع قاعدة البيانات
            if db_type == 'mysql':
                # استخدام %s للمعاملات مع MySQL
                query = "UPDATE settings SET delivery_price = %s"
                print(f"استخدام استعلام MySQL: {query}")
            else:
                # استخدام ? للمعاملات مع SQLite
                query = "UPDATE settings SET delivery_price = ?"
                print(f"استخدام استعلام SQLite: {query}")

            # تنفيذ الاستعلام
            cursor.execute(query, (new_price,))
            conn.commit()

            # إغلاق الاتصال (إذا كان SQLite)
            if db_type != 'mysql':
                conn.close()

            self.update_current_delivery_price()
            self.new_delivery_price_input.clear()
            QMessageBox.information(self, "نجاح", "تم حفظ سعر التوصيل الجديد بنجاح")

        except ValueError:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رقم صحيح")
        except Exception as e:
            print(f"خطأ في حفظ سعر التوصيل الجديد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ سعر التوصيل الجديد: {str(e)}")

    def load_users(self):
        """تحميل المستخدمين"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users")
            users = cursor.fetchall()

            self.users_table.setRowCount(len(users))
            for i, user in enumerate(users):
                # إضافة مربع تحديد
                checkbox = QTableWidgetItem()
                checkbox.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                checkbox.setCheckState(Qt.Unchecked)
                self.users_table.setItem(i, 0, checkbox)

                # إضافة بيانات المستخدم
                for j, value in enumerate(user):
                    item = QTableWidgetItem(str(value))
                    self.users_table.setItem(i, j + 1, item)
            conn.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المستخدمين: {str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            filename, _ = QFileDialog.getSaveFileName(self,
                "حفظ النسخة الاحتياطية",
                "",
                "Database Files (*.db)")

            if filename:
                shutil.copy2('carpet_cleaning.db', filename)
                self.show_notification("تم إنشاء النسخة الاحتياطية بنجاح")
        except Exception as e:
            self.show_notification(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            filename, _ = QFileDialog.getOpenFileName(self,
                "اختر النسخة الاحتياطية",
                "",
                "Database Files (*.db)")

            if filename:
                shutil.copy2(filename, 'carpet_cleaning.db')
                self.show_notification("تم استعادة النسخة الاحتياطية بنجاح")
                self.load_users()
                self.update_current_price()
        except Exception as e:
            self.show_notification(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def add_user(self):
        """إضافة مستخدم جديد"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        role = "admin" if self.user_type.currentText() == "مدير" else "employee"

        if not all([username, password]):
            self.show_notification("الرجاء ملء جميع الحقول")
            return

        success, result = self.db.add_user(username, password, role)

        if success:
            # إضافة المستخدم للجدول مباشرة
            row_position = self.users_table.rowCount()
            self.users_table.insertRow(row_position)

            new_user = (result, username, password, "مدير" if role == "admin" else "موظف")
            for j, value in enumerate(new_user):
                item = QTableWidgetItem(str(value))
                self.users_table.setItem(row_position, j, item)

            # تنظيف الحقول
            self.username_input.clear()
            self.password_input.clear()
            self.user_type.setCurrentIndex(0)
            self.username_input.setFocus()

            self.show_notification("تمت إضافة المستخدم بنجاح")
        else:
            self.show_notification(result)

    def show_notification(self, message, duration=3000):
        """عرض إشعار للمستخدم"""
        notification = QLabel(message, self)
        notification.setStyleSheet("""
            QLabel {
                background-color: #2ecc71;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
        """)

        # تحديد موقع الإشعار في أعلى النافذة
        notification.adjustSize()
        x = (self.width() - notification.width()) // 2
        notification.move(x, 20)
        notification.show()

        # إخفاء الإشعار بعد مدة محددة
        QTimer.singleShot(duration, notification.deleteLater)

    def toggle_select_all_users(self):
        """تحديد أو إلغاء تحديد جميع المستخدمين"""
        check_state = Qt.Checked if self.select_all_users_checkbox.isChecked() else Qt.Unchecked
        for row in range(self.users_table.rowCount()):
            self.users_table.item(row, 0).setCheckState(check_state)

    def delete_selected_users(self):
        """حذف المستخدمين المحددين"""
        try:
            # جمع المستخدمين المحددين
            selected_users = []
            for row in range(self.users_table.rowCount()):
                if self.users_table.item(row, 0).checkState() == Qt.Checked:
                    user_id = int(self.users_table.item(row, 1).text())
                    username = self.users_table.item(row, 2).text()
                    # لا يمكن حذف المستخدم الرئيسي admin
                    if username != 'admin':
                        selected_users.append((user_id, username))

            if not selected_users:
                QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مستخدم للحذف")
                return

            # تأكيد الحذف
            message = "هل أنت متأكد من حذف المستخدمين التاليين؟\n"
            for _, name in selected_users:
                message += f"- {name}\n"

            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف المستخدمين
                conn = self.db.get_connection()
                cursor = conn.cursor()

                for user_id, _ in selected_users:
                    cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

                conn.commit()
                conn.close()

                # تحديث الجدول
                self.load_users()

                # إعادة تعيين مربع التحديد
                self.select_all_users_checkbox.setChecked(False)

                QMessageBox.information(self, "نجاح", f"تم حذف {len(selected_users)} مستخدم بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدمين: {str(e)}")

    def update_backup_time(self):
        """تحديث وقت النسخ الاحتياطي"""
        time = self.backup_time_edit.time()
        time_str = f"{time.hour():02d}:{time.minute():02d}"
        update_config('backup_time', time_str)

    def browse_backup_dir(self):
        """اختيار مجلد النسخ الاحتياطي"""
        config = load_config()
        current_dir = config.get('backup_dir', '')

        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد النسخ الاحتياطي",
            current_dir,
            QFileDialog.ShowDirsOnly
        )

        if dir_path:
            self.backup_dir_edit.setText(dir_path)
            update_config('backup_dir', dir_path)

            # إنشاء المجلد إذا لم يكن موجوداً
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path)
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"خطأ في إنشاء المجلد: {str(e)}")

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        try:
            # عرض مؤشر الانتظار
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # تنفيذ النسخ الاحتياطي في وظيفة منفصلة للحماية من الإغلاق غير المقصود
            self.perform_backup_operation()

        except Exception as e:
            # إعادة المؤشر إلى الوضع الطبيعي في حالة الخطأ
            QApplication.restoreOverrideCursor()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def perform_backup_operation(self):
        """تنفيذ عملية النسخ الاحتياطي بشكل آمن"""
        try:
            # إنشاء نسخة احتياطية
            success, message, backup_path = create_backup()

            # إعادة المؤشر إلى الوضع الطبيعي
            QApplication.restoreOverrideCursor()

            if success:
                QMessageBox.information(self, "نجاح", f"تم إنشاء النسخة الاحتياطية بنجاح\n{os.path.basename(backup_path)}")
                # تحديث قائمة النسخ الاحتياطية
                self.load_backups_list()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            # إعادة المؤشر إلى الوضع الطبيعي في حالة الخطأ
            QApplication.restoreOverrideCursor()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع في النسخ الاحتياطي: {str(e)}")

    def restore_from_backup(self):
        """استعادة من نسخة احتياطية"""
        try:
            # التحقق من تحديد نسخة احتياطية
            selected_items = self.backups_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تحذير", "الرجاء تحديد نسخة احتياطية للاستعادة")
                return

            # الحصول على مسار النسخة الاحتياطية المحددة
            selected_item = selected_items[0]
            backup_path = selected_item.data(Qt.UserRole)

            # تأكيد الاستعادة
            reply = QMessageBox.question(
                self,
                "تأكيد الاستعادة",
                "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية المحددة.\n"
                "هل أنت متأكد من الاستمرار؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # عرض مؤشر الانتظار
                QApplication.setOverrideCursor(Qt.WaitCursor)

                # استعادة النسخة الاحتياطية
                success, message = restore_backup(backup_path)

                # إعادة المؤشر إلى الوضع الطبيعي
                QApplication.restoreOverrideCursor()

                if success:
                    QMessageBox.information(self, "نجاح", "تم استعادة قاعدة البيانات بنجاح\n"
                                          "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات")
                else:
                    QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            # إعادة المؤشر إلى الوضع الطبيعي في حالة الخطأ
            QApplication.restoreOverrideCursor()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def load_backups_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح القائمة الحالية
            self.backups_list.clear()

            # الحصول على قائمة النسخ الاحتياطية
            backups = list_backups()

            if not backups:
                # إضافة عنصر يشير إلى عدم وجود نسخ احتياطية
                item = QListWidgetItem("لا توجد نسخ احتياطية متوفرة")
                item.setFlags(item.flags() & ~Qt.ItemIsSelectable)
                self.backups_list.addItem(item)
                return

            # إضافة النسخ الاحتياطية إلى القائمة
            for backup in backups:
                # تنسيق حجم الملف
                size_kb = backup['size'] / 1024
                size_mb = size_kb / 1024

                if size_mb >= 1:
                    size_str = f"{size_mb:.2f} ميجابايت"
                else:
                    size_str = f"{size_kb:.2f} كيلوبايت"

                # إنشاء نص العنصر
                item_text = f"{backup['filename']} - {backup['date']} - {size_str}"

                # إضافة العنصر إلى القائمة
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, backup['path'])
                self.backups_list.addItem(item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل قائمة النسخ الاحتياطية: {str(e)}")

    def delete_old_backups(self):
        """حذف النسخ الاحتياطية القديمة"""
        try:
            # طلب تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "سيتم حذف النسخ الاحتياطية التي مر على إنشائها أكثر من 30 يوم.\n"
                "هل أنت متأكد من الاستمرار؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # عرض مؤشر الانتظار
                QApplication.setOverrideCursor(Qt.WaitCursor)

                # حذف النسخ الاحتياطية القديمة
                deleted_count, message = delete_old_backups()

                # إعادة المؤشر إلى الوضع الطبيعي
                QApplication.restoreOverrideCursor()

                # تحديث قائمة النسخ الاحتياطية
                self.load_backups_list()

                QMessageBox.information(self, "نجاح", message)

        except Exception as e:
            # إعادة المؤشر إلى الوضع الطبيعي في حالة الخطأ
            QApplication.restoreOverrideCursor()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق نافذة الإعدادات"""
        # قبول حدث الإغلاق فقط دون إغلاق البرنامج بالكامل
        event.accept()
