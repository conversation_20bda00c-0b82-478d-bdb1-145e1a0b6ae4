#!/usr/bin/env python
"""
إصلاح وظيفة نقل الطلبات في ملف orders.py
"""

import os
import re
import sys

# إضافة المجلد الرئيسي للمشروع إلى مسارات البحث عن الحزم
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# قراءة محتوى وظيفة نقل الطلبات المحدثة
transfer_function_file = os.path.join(project_root, 'scripts', 'transfer_function.py')
with open(transfer_function_file, 'r', encoding='utf-8') as f:
    transfer_function_content = f.read()

# إزالة السطور الأولى من الملف (التعليقات والترويسة)
lines = transfer_function_content.strip().split('\n')
for i, line in enumerate(lines):
    if line.startswith('def transfer_to_delegate'):
        transfer_function_content = '\n'.join(lines[i:])
        break

# إضافة المساحات البادئة للوظيفة (4 مسافات)
transfer_function_content = '\n'.join(['    ' + line for line in transfer_function_content.split('\n')])

# قراءة ملف orders.py
orders_file = os.path.join(project_root, 'src', 'orders.py')
with open(orders_file, 'r', encoding='utf-8') as f:
    orders_content = f.read()

# عمل نسخة احتياطية من الملف الأصلي
backup_file = orders_file + '.bak'
with open(backup_file, 'w', encoding='utf-8') as f:
    f.write(orders_content)
print(f"تم عمل نسخة احتياطية من الملف الأصلي في: {backup_file}")

# إزالة جميع وظائف transfer_to_delegate الموجودة
pattern = r'\s*def\s+transfer_to_delegate\s*\(self\)\s*:.*?(?=\s*def\s+|$)'
orders_content_without_transfer = re.sub(pattern, '', orders_content, flags=re.DOTALL)

# تحديد موضع إضافة الوظيفة الجديدة (قبل آخر تعريف لوظيفة في الملف)
pattern = r'(\s*def\s+[^\(]+\([^\)]*\)\s*:.*?)$'
match = re.search(pattern, orders_content_without_transfer, re.DOTALL)

if match:
    # إضافة الوظيفة قبل آخر وظيفة في الملف
    insert_position = match.start()
    updated_content = (orders_content_without_transfer[:insert_position] + 
                        transfer_function_content + '\n\n' + 
                        orders_content_without_transfer[insert_position:])
else:
    # إضافة الوظيفة في نهاية الملف
    updated_content = orders_content_without_transfer + '\n\n' + transfer_function_content

# حفظ الملف المحدث
with open(orders_file, 'w', encoding='utf-8') as f:
    f.write(updated_content)

print(f"تم تحديث ملف {orders_file} بنجاح!")
print("تم إزالة وظائف نقل الطلبات المكررة وإضافة الوظيفة المحدثة.")
