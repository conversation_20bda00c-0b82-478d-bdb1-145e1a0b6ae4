"""
ملف الستايلات المشتركة للبرنامج
"""

# الألوان الأساسية
COLORS = {
    'primary': '#3498db',    # أزرق
    'secondary': '#95a5a6',  # رمادي متوسط
    'success': '#2ecc71',    # أخضر
    'warning': '#f1c40f',    # أصفر
    'info': '#1abc9c',       # فيروزي
    'danger': '#e74c3c',     # أحمر
    'dark': '#2c3e50',       # أسود مائل للأزرق
    'gray': '#7f8c8d',       # رمادي
    'gray_light': '#95a5a6', # رمادي فاتح
    'white': '#ffffff',      # أبيض
    'background': '#f5f6fa', # رمادي فاتح جداً
    'border': '#dcdde1'      # رمادي للحدود
}

# الخصائص المشتركة
COMMON = {
    'border_radius': '8px',
    'padding': '12px',
    'font_family': 'Arial',
    'font_size': '14px'
}

# ستايل النافذة الرئيسية
WINDOW_STYLE = f"""
    QMainWindow {{
        background-color: {COLORS['background']};
    }}
"""

# ستايل حاوية العنوان
TITLE_CONTAINER_STYLE = f"""
    QWidget#titleContainer {{
        background-color: {COLORS['primary']};
        border-radius: {COMMON['border_radius']};
        padding: 15px;
        min-height: 120px;
        margin: 8px;
    }}
"""

# ستايل العنوان الرئيسي
TITLE_STYLE = f"""
    QLabel {{
        color: {COLORS['white']};
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 15px;
    }}
"""

# ستايل العبارات الدعائية
SLOGAN_STYLE = f"""
    QLabel {{
        color: {COLORS['white']};
        font-size: 22px;
        font-weight: bold;
        margin: 10px 0;
    }}
"""

# ستايل التاريخ
DATE_STYLE = f"""
    QLabel {{
        color: {COLORS['white']};
        font-size: 16px;
        margin-top: 10px;
    }}
"""

# ستايل حقول الإدخال
INPUT_STYLE = f"""
    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox {{
        background-color: {COLORS['white']};
        border: 2px solid {COLORS['border']};
        border-radius: {COMMON['border_radius']};
        padding: {COMMON['padding']};
        font-size: {COMMON['font_size']};
        min-height: 45px;
        color: {COLORS['dark']};
    }}
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus {{
        border-color: {COLORS['primary']};
    }}
    QLineEdit::placeholder {{
        color: {COLORS['gray']};
        font-size: 13px;
    }}
"""

INPUT_STYLE_NEW = """
QLineEdit {
    padding: 8px;
    border: 1px solid #dcdde1;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
}

QLineEdit:focus {
    border: 2px solid #3498db;
}

QLineEdit::placeholder {
    color: #95a5a6;
}
"""

FORM_LABEL_STYLE = """
QLabel {
    font-size: 14px;
    color: #2c3e50;
    padding: 5px;
}
"""

WINDOW_STYLE += INPUT_STYLE_NEW + FORM_LABEL_STYLE

# ستايل الجدول
TABLE_STYLE = f"""
    QTableWidget {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['border']};
        border-radius: {COMMON['border_radius']};
        gridline-color: {COLORS['border']};
        font-size: 13px;
    }}
    QTableWidget::item {{
        padding: 4px;
        border-bottom: 1px solid {COLORS['border']};
    }}
    QHeaderView::section {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        padding: 6px;
        border: none;
        font-weight: bold;
        font-size: 13px;
    }}
    QTableWidget::item:selected {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
    }}
"""

# ستايل حقول البحث
SEARCH_STYLE = f"""
    QLineEdit {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['border']};
        border-radius: 5px;
        padding: 5px;
        font-size: 14px;
    }}
    QLineEdit:focus {{
        border: 2px solid {COLORS['primary']};
    }}
"""

# ستايل المجموعات
GROUP_STYLE = f"""
    QGroupBox {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['border']};
        border-radius: {COMMON['border_radius']};
        margin-top: 20px;
        font-weight: bold;
    }}
    QGroupBox::title {{
        color: {COLORS['dark']};
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 3px;
    }}
"""

# نمط النوافذ الحوارية
DIALOG_STYLE = """
QDialog {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
}

QLabel {
    color: #333333;
    font-size: 12px;
    font-weight: bold;
}

QLineEdit, QSpinBox {
    padding: 5px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: white;
    min-height: 25px;
}

QLineEdit:focus, QSpinBox:focus {
    border: 1px solid #4a90e2;
}

QPushButton {
    padding: 5px 15px;
    border-radius: 3px;
    font-weight: bold;
    min-width: 80px;
    min-height: 30px;
}
"""

# ستايل الرسائل المنبثقة
MESSAGEBOX_STYLE = f"""
    QMessageBox {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['border']};
        border-radius: {COMMON['border_radius']};
    }}
    QMessageBox QLabel {{
        color: {COLORS['dark']};
        font-size: {COMMON['font_size']};
        padding: 10px;
    }}
    QMessageBox QPushButton {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        border: none;
        border-radius: 5px;
        padding: 8px 16px;
        min-width: 80px;
    }}
    QMessageBox QPushButton:hover {{
        background-color: {COLORS['info']};
    }}
"""

# ستايل نافذة تسجيل الدخول
LOGIN_STYLE = f"""
    QDialog {{
        background-color: {COLORS['background']};
    }}
    QLabel {{
        color: {COLORS['dark']};
        font-size: {COMMON['font_size']};
    }}
    QLineEdit {{
        padding: {COMMON['padding']};
        border: 2px solid {COLORS['border']};
        border-radius: {COMMON['border_radius']};
        font-size: {COMMON['font_size']};
        margin: 12px 0;
        background-color: {COLORS['white']};
        min-height: 30px;
        color: {COLORS['dark']};
    }}
    QLineEdit::placeholder {{
        color: {COLORS['gray']};
        font-size: 13px;
    }}
    QLineEdit:focus {{
        border-color: {COLORS['primary']};
    }}
    QPushButton#loginButton {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        border: none;
        padding: {COMMON['padding']};
        border-radius: {COMMON['border_radius']};
        font-size: 16px;
        font-weight: bold;
        margin-top: 30px;
        min-height: 45px;
    }}
    QPushButton#loginButton:hover {{
        background-color: {COLORS['primary']}dd;
    }}
    QPushButton#loginButton:pressed {{
        background-color: {COLORS['primary']}ee;
    }}
    QWidget#formContainer {{
        background-color: {COLORS['white']};
        border-radius: 15px;
        padding: 20px;
    }}
"""

# ستايل حاويات البحث والأزرار
CONTAINER_STYLE = f"""
    background-color: {COLORS['white']};
    border-radius: 10px;
    padding: 15px;
"""

# ستايل حقول البحث
SEARCH_FIELD_STYLE = f"""
    border: 1px solid {COLORS['border']};
    border-radius: 5px;
    padding: 10px;
    font-size: 14px;
    background-color: {COLORS['background']};
"""

# ستايل الأزرار
SELECT_ALL_BUTTON_STYLE = f"""
    background-color: {COLORS['info']};
    color: {COLORS['white']};
    border: none;
    border-radius: 5px;
    padding: 12px;
    font-size: 14px;
    font-weight: bold;
"""

PRINT_BUTTON_STYLE = f"""
    background-color: {COLORS['primary']};
    color: {COLORS['white']};
    border: none;
    border-radius: 5px;
    padding: 12px;
    font-size: 14px;
    font-weight: bold;
"""

# ستايل شريط الحالة
STATUS_BAR_STYLE = f"""
    background-color: {COLORS['white']};
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
"""

# ستايل نافذة إدخال أبعاد السجاد
CARPET_DIALOG_STYLE = f"""
    QDialog {{
        background-color: {COLORS['background']};
    }}
    QGroupBox {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['border']};
        border-radius: 8px;
        margin-top: 20px;
        font-weight: bold;
    }}
    QGroupBox::title {{
        color: {COLORS['dark']};
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px;
    }}
    QLineEdit {{
        border: 1px solid {COLORS['border']};
        border-radius: 5px;
        padding: 10px;
        font-size: 14px;
        background-color: {COLORS['background']};
    }}
    QLineEdit:focus {{
        border: 2px solid {COLORS['primary']};
    }}
    QPushButton {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        border: none;
        border-radius: 5px;
        padding: 12px;
        font-size: 14px;
        font-weight: bold;
    }}
    QPushButton:hover {{
        background-color: {COLORS['primary']}dd;
    }}
"""

# ستايل عنوان الصفحة
TITLE_LABEL_STYLE = f"""
    color: {COLORS['white']};
    font-size: 24px;
    font-weight: bold;
"""

# ستايل تعليمات
INSTRUCTIONS_STYLE = f"""
    font-weight: bold; 
    color: {COLORS['primary']}; 
    margin-bottom: 15px;
    background-color: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    font-size: 14px;
"""

def get_button_style(color):
    """إنشاء ستايل للأزرار"""
    return f"""
        QPushButton {{
            background-color: {color};
            color: {COLORS['white']};
            border: none;
            border-radius: {COMMON['border_radius']};
            padding: 10px;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {color}dd;
            /* تم إزالة خصائص CSS غير المدعومة */
        }}
        QPushButton:pressed {{
            background-color: {color}ee;
            padding-top: 12px;
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_light']};
            color: {COLORS['gray']};
        }}
    """
