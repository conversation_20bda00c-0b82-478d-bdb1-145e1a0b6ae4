#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت شامل لإزالة SQLite والاعتماد بالكامل على MySQL
"""

import os
import sys
import shutil
import traceback
import json

def backup_sqlite_files():
    """إنشاء نسخة احتياطية من ملفات SQLite قبل حذفها"""
    print("جاري إنشاء نسخة احتياطية من ملفات SQLite...")
    
    backup_dir = "sqlite_backup"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    sqlite_files = [
        "carpet_cleaning.db",
        "carpet_db.db"
    ]
    
    backup_files = []
    for file in sqlite_files:
        if os.path.exists(file):
            backup_path = os.path.join(backup_dir, file)
            shutil.copy2(file, backup_path)
            backup_files.append(backup_path)
            print(f"✓ تم نسخ {file} إلى {backup_path}")
    
    return backup_files

def remove_sqlite_files():
    """حذف ملفات SQLite"""
    print("\nجاري حذف ملفات SQLite...")
    
    sqlite_files = [
        "carpet_cleaning.db",
        "carpet_db.db"
    ]
    
    removed_files = []
    for file in sqlite_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                removed_files.append(file)
                print(f"✓ تم حذف {file}")
            except Exception as e:
                print(f"✗ فشل في حذف {file}: {str(e)}")
    
    return removed_files

def update_config_file():
    """تحديث ملف التكوين لاستخدام MySQL فقط"""
    print("\nجاري تحديث ملف التكوين...")
    
    config_file = "app_config.json"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # تأكيد استخدام MySQL
            config['db_type'] = 'mysql'
            
            # إضافة تعليق للتأكيد
            config['_note'] = 'تم تحديث البرنامج لاستخدام MySQL فقط - لا يدعم SQLite بعد الآن'
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            print(f"✓ تم تحديث {config_file}")
            return True
            
        except Exception as e:
            print(f"✗ فشل في تحديث {config_file}: {str(e)}")
            return False
    else:
        print(f"✗ ملف التكوين {config_file} غير موجود")
        return False

def update_database_models():
    """تحديث ملف models.py لإزالة دعم SQLite"""
    print("\nجاري تحديث ملف models.py...")
    
    models_file = "db/models.py"
    
    if not os.path.exists(models_file):
        print(f"✗ ملف {models_file} غير موجود")
        return False
    
    try:
        # قراءة الملف الحالي
        with open(models_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إنشاء نسخة احتياطية
        backup_file = models_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ تم إنشاء نسخة احتياطية: {backup_file}")
        
        # تحديث المحتوى
        new_content = """import os
import time

# استخدام PyMySQL بدلاً من mysql-connector-python
try:
    import pymysql
    from pymysql import Error
    USING_PYMYSQL = True
except ImportError:
    # الرجوع إلى mysql-connector-python إذا لم يكن PyMySQL متاحًا
    import mysql.connector
    from mysql.connector import Error
    USING_PYMYSQL = False
from sqlalchemy import create_engine, Column, Integer, String, Float, ForeignKey, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import datetime
from utils.config import load_config

Base = declarative_base()

class Order(Base):
    \"\"\"نموذج الطلبات\"\"\"
    __tablename__ = 'orders'

    id = Column(Integer, primary_key=True)
    phone = Column(String, nullable=False)
    phone2 = Column(String)  # رقم الهاتف الثاني
    address = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)
    receipt_number = Column(String)  # رقم الوصل
    total_area = Column(Float, default=0)  # المساحة الكلية
    total_price = Column(Float, default=0)  # السعر الإجمالي
    notes = Column(String)  # ملاحظات
    delegate_id = Column(Integer, ForeignKey('delegates.id'), nullable=True)  # معرف المندوب
    delegate_name = Column(String)  # اسم المندوب
    status = Column(String, default='جديد')  # حالة الطلب

    # العلاقة مع أبعاد السجاد
    carpet_dimensions = relationship("CarpetDimension", back_populates="order", cascade="all, delete-orphan")

class CarpetDimension(Base):
    \"\"\"نموذج أبعاد السجاد\"\"\"
    __tablename__ = 'carpet_dimensions'

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id', ondelete='CASCADE'), nullable=False)
    length = Column(Float, nullable=False)
    width = Column(Float, nullable=False)
    total_area = Column(Float, nullable=False)

    # العلاقة مع الطلب
    order = relationship("Order", back_populates="carpet_dimensions")

class Settings(Base):
    \"\"\"نموذج الإعدادات\"\"\"
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    price_per_meter = Column(Float, default=1000.0)
    blanket_price = Column(Float, default=5000.0)  # إضافة حقل سعر البطانية
    delivery_price = Column(Float, default=2000.0)  # إضافة حقل سعر التوصيل
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Employee(Base):
    \"\"\"نموذج الموظفين\"\"\"
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    phone = Column(String, nullable=False)
    salary = Column(Float, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)

class Expense(Base):
    \"\"\"نموذج المصروفات\"\"\"
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    item = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)

class Delegate(Base):
    \"\"\"نموذج المندوبين\"\"\"
    __tablename__ = 'delegates'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

class DelegateOrder(Base):
    \"\"\"نموذج طلبات المندوبين\"\"\"
    __tablename__ = 'delegate_orders'

    id = Column(Integer, primary_key=True)
    receipt_number = Column(String)
    phone_number = Column(String, nullable=False)
    area = Column(String)
    carpet_count = Column(Integer, nullable=False, default=0)
    blanket_count = Column(Integer, default=0)
    total_price = Column(Float, nullable=False, default=0)
    order_id = Column(Integer, ForeignKey('orders.id'))
    delegate_id = Column(Integer, ForeignKey('delegates.id'))
    representative_name = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

class Database:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not Database._initialized:
            # تحميل إعدادات البرنامج
            self.config = load_config()
            self.db_type = 'mysql'  # فقط MySQL - لا دعم لـ SQLite

            # إعدادات الاتصال بـ MySQL
            self.mysql_config = {
                'host': self.config.get('db_host', 'localhost'),
                'user': self.config.get('db_user', 'carpet_user'),
                'password': self.config.get('db_password', '@#57111819752#@'),
                'database': self.config.get('db_name', 'carpet_cleaning_service'),
                'port': int(self.config.get('db_port', '3306')),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'raise_on_warnings': True
            }

            self.update_tables()  # تحديث هيكل الجداول
            Database._initialized = True

    def get_connection(self):
        \"\"\"إنشاء اتصال بقاعدة البيانات MySQL فقط\"\"\"
        try:
            # تعديل إعدادات الاتصال لتجنب مشكلة access violation
            safe_config = {
                'host': self.mysql_config['host'],
                'user': self.mysql_config['user'],
                'password': self.mysql_config['password'],
                'database': self.mysql_config['database'],
                'port': self.mysql_config['port']
            }

            # اتصال بقاعدة بيانات MySQL
            if USING_PYMYSQL:
                # استخدام PyMySQL
                conn = pymysql.connect(**safe_config)
            else:
                # استخدام mysql-connector-python
                conn = mysql.connector.connect(**safe_config)
            return conn
        except Error as e:
            raise Exception(f"خطأ في الاتصال بقاعدة بيانات MySQL: {e}")
        except Exception as e:
            raise Exception(f"خطأ غير متوقع في الاتصال بقاعدة بيانات MySQL: {e}")

    def update_tables(self):
        \"\"\"تحديث هيكل الجداول في MySQL\"\"\"
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود عمود has_delivery_fee في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'has_delivery_fee'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN has_delivery_fee TINYINT(1) DEFAULT 0")
            except Exception as e:
                pass

            # التحقق من وجود عمود delivery_fee في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'delivery_fee'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00")
            except Exception as e:
                pass

            # التحقق من وجود عمود pickup_representative_id في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'pickup_representative_id'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN pickup_representative_id INT DEFAULT NULL")
            except Exception as e:
                pass

            # التحقق من وجود عمود completion_date في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'completion_date'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN completion_date TIMESTAMP NULL DEFAULT NULL")
            except Exception as e:
                pass

            # التحقق من وجود عمود carpet_count في جدول orders
            try:
                cursor.execute("SHOW COLUMNS FROM orders LIKE 'carpet_count'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE orders ADD COLUMN carpet_count INT DEFAULT 0")
            except Exception as e:
                pass

            # حفظ التغييرات
            conn.commit()

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
"""
        
        # كتابة المحتوى الجديد
        with open(models_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✓ تم تحديث {models_file}")
        return True
        
    except Exception as e:
        print(f"✗ فشل في تحديث {models_file}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إزالة SQLite والاعتماد بالكامل على MySQL".center(60))
    print("=" * 60)
    
    try:
        # 1. إنشاء نسخة احتياطية من ملفات SQLite
        backup_files = backup_sqlite_files()
        
        # 2. تحديث ملف التكوين
        config_updated = update_config_file()
        
        # 3. تحديث ملف models.py
        models_updated = update_database_models()
        
        # 4. حذف ملفات SQLite
        removed_files = remove_sqlite_files()
        
        print("\n" + "=" * 60)
        print("ملخص العملية".center(60))
        print("=" * 60)
        
        if backup_files:
            print(f"✓ تم إنشاء نسخة احتياطية من {len(backup_files)} ملف")
        
        if config_updated:
            print("✓ تم تحديث ملف التكوين")
        
        if models_updated:
            print("✓ تم تحديث ملف models.py")
        
        if removed_files:
            print(f"✓ تم حذف {len(removed_files)} ملف SQLite")
        
        print("\n" + "=" * 60)
        print("تم الانتهاء بنجاح!".center(60))
        print("البرنامج الآن يعتمد على MySQL فقط".center(60))
        print("=" * 60)
        
    except Exception as e:
        print(f"\nخطأ في العملية: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
