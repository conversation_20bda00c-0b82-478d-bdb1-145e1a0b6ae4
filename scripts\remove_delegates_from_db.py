#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import sys
import time

# إضافة مسار المشروع الرئيسي إلى مسارات النظام
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def get_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    db_path = 'carpet_cleaning.db'
    # إذا لم يكن الملف موجودًا في المسار الحالي، نفترض أنه في المجلد الرئيسي
    if not os.path.exists(db_path):
        db_path = os.path.join(os.path.dirname(__file__), '..', 'carpet_cleaning.db')
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def remove_delegate_from_orders():
    """إزالة حقول المندوب من جدول الطلبات"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # حذف جدول orders_temp إذا كان موجوداً
        cursor.execute("DROP TABLE IF EXISTS orders_temp")
        
        # التحقق مما إذا كان العمود موجودًا
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        pickup_rep_exists = any(col['name'] == 'pickup_representative_id' for col in columns)
        delivery_rep_exists = any(col['name'] == 'delivery_representative_id' for col in columns)
        
        if pickup_rep_exists or delivery_rep_exists:
            print("جاري إزالة حقول المندوب من جدول الطلبات...")
            
            # إنشاء جدول مؤقت بدون أعمدة المندوب
            cursor.execute("""
                CREATE TABLE orders_temp (
                    id INTEGER PRIMARY KEY,
                    phone TEXT NOT NULL,
                    address TEXT NOT NULL,
                    carpet_count INTEGER NOT NULL DEFAULT 0,
                    status TEXT DEFAULT 'جديد',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    receipt_number TEXT,
                    total_area REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    region TEXT,
                    blanket_count INTEGER DEFAULT 0,
                    notes TEXT,
                    phone2 TEXT
                )
            """)
            
            # نقل البيانات من الجدول القديم إلى الجدول المؤقت
            cursor.execute("""
                INSERT INTO orders_temp (
                    id, phone, address, carpet_count, status, created_at, 
                    receipt_number, total_area, total_price, region, 
                    blanket_count, notes, phone2
                )
                SELECT 
                    id, phone, address, carpet_count, status, created_at, 
                    receipt_number, total_area, total_price, region, 
                    blanket_count, notes, phone2
                FROM orders
            """)
            
            # حذف الجدول القديم
            cursor.execute("DROP TABLE orders")
            
            # إعادة تسمية الجدول المؤقت
            cursor.execute("ALTER TABLE orders_temp RENAME TO orders")
            
            if pickup_rep_exists:
                print("تم إزالة عمود مندوب الاستلام بنجاح.")
            if delivery_rep_exists:
                print("تم إزالة عمود مندوب التوصيل بنجاح.")
        else:
            print("حقول المندوب غير موجودة في جدول الطلبات.")

        conn.commit()
        print("تم الانتهاء من إزالة حقول المندوب من قاعدة البيانات.")
    except Exception as e:
        conn.rollback()
        print(f"حدث خطأ أثناء عملية التعديل: {str(e)}")
    finally:
        conn.close()

def main():
    print("بدء عملية إزالة حقول المندوب من قاعدة البيانات...")
    remove_delegate_from_orders()
    print("تم الانتهاء من عملية إزالة حقول المندوب.")

if __name__ == "__main__":
    main()
