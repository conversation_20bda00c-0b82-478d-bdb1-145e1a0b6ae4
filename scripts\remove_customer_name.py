import sqlite3
import os
import sys
from pathlib import Path

# Add the project root directory to Python path in a more robust way
project_root = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(project_root))

from db.models import Database

def remove_customer_name():
    """حذف عمود اسم العميل من جدول delegate_orders"""
    try:
        # الحصول على مسار السكريبت SQL
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sql_file = os.path.join(script_dir, 'remove_customer_name.sql')
        
        # قراءة محتوى ملف SQL
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
            
        # الاتصال بقاعدة البيانات
        db = Database()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # تنفيذ السكريبت
        cursor.executescript(sql_script)
        
        # حفظ التغييرات
        conn.commit()
        print("تم حذف عمود اسم العميل من جدول delegate_orders بنجاح")
        
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    remove_customer_name()