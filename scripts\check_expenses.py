import sqlite3

# Connect to database
conn = sqlite3.connect("carpet_db.db")
cursor = conn.cursor()

# Check expenses table structure
print("Expenses table structure:")
cursor.execute("PRAGMA table_info(expenses)")
columns = cursor.fetchall()
for col in columns:
    print(f"Column: {col[1]}, Type: {col[2]}, Required: {col[3]}")

# Check existing data in expenses table
print("\nExisting data in expenses table:")
cursor.execute("SELECT * FROM expenses")
expenses = cursor.fetchall()
if expenses:
    for expense in expenses:
        print(f"Data: {expense}")
else:
    print("No data in expenses table")

# Test insert query
print("\nTrying to insert test data:")
try:
    cursor.execute("""
    INSERT INTO expenses (name, item, amount, date, user_id) 
    VALUES (?, ?, ?, ?, ?)
    """, ("Test Name", "Test Item", 100.0, "2025-03-02", 1))
    conn.commit()
    print("Test data inserted successfully")
    
    # Check data after insertion
    print("\nData after insertion:")
    cursor.execute("SELECT * FROM expenses")
    expenses = cursor.fetchall()
    for expense in expenses:
        print(f"Data: {expense}")
except Exception as e:
    print(f"Error inserting data: {e}")

conn.close()
