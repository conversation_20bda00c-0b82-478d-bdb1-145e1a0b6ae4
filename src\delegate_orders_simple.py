#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نافذة استلام المندوبين
"""

import traceback
import sip
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QGridLayout,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QGroupBox, QDialog, QApplication, QLineEdit, QSizePolicy, QCheckBox)
from PyQt5.QtCore import Qt, QDateTime, QTimer, QObject, QEvent
from PyQt5.QtGui import QFont, QTextDocument, QPixmap, QPainter
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
import os
from db.models import Database

class DelegateOrdersWindow(QMainWindow):
    """نافذة استلام المندوبين """

    # قياسات النافذة الرئيسية
    WINDOW_WIDTH = 850
    WINDOW_HEIGHT = 650

    # قياسات الهوامش والمسافات
    MAIN_MARGIN = 3  # تقليل الهامش العام أكثر
    DELEGATES_GROUP_MARGIN = (8, 8, 8, 8)  # تقليل هوامش مجموعة المندوبين
    SPACING = 6  # تقليل المسافة بين العناصر

    # قياسات الأزرار
    DELEGATE_BTN_WIDTH = 100
    DELEGATE_BTN_HEIGHT = 90
    CONTROL_BTN_MIN_WIDTH = 120
    CONTROL_BTN_MAX_WIDTH = 200

    # قياسات نافذة عرض الطلبات
    ORDERS_DIALOG_WIDTH = 1350
    ORDERS_DIALOG_HEIGHT = 650

    def __init__(self, parent=None, db=None):
        super(DelegateOrdersWindow, self).__init__(parent)
        self.db = db
        self.current_delegate_name = None
        self.current_orders_table = None  # إضافة مرجع للجدول الحالي
        self.current_dialog = None  # إضافة مرجع للنافذة الحالية
        self.initUI()
        self.load_delegates()

    def initUI(self):
        # إعداد النافذة الرئيسية
        self.setWindowTitle('نظام إدارة طلبات المندوبين')

        # تعيين حجم النافذة
        self.resize(self.WINDOW_WIDTH, self.WINDOW_HEIGHT)

        # وضع النافذة في وسط الشاشة
        desktop = QApplication.desktop()
        screen_geometry = desktop.screenGeometry(desktop.primaryScreen())
        x = (screen_geometry.width() - self.WINDOW_WIDTH) // 2
        y = (screen_geometry.height() - self.WINDOW_HEIGHT) // 4
        self.move(x, y)

        self.setStyleSheet('background-color: #f5f5f5; font-family: Arial;')
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, self.MAIN_MARGIN)
        main_layout.setSpacing(0)

        # إضافة عنوان للصفحة
        title = QLabel('إدارة طلبات المندوبين')
        title.setFixedHeight(60)  # زيادة ارتفاع العنوان إلى 60
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet('''
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 0;
                margin: 0;
                border: none;
                line-height: 60px;
            }
        ''')

        # إنشاء حاوية للمحتوى
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(self.MAIN_MARGIN, -25, self.MAIN_MARGIN, 0)  # تقريب الأزرار للأعلى
        content_layout.setSpacing(0)

        # إضافة مجموعة المندوبين إلى حاوية المحتوى
        self.delegates_group = QGroupBox()
        self.delegates_group.setStyleSheet('''
            QGroupBox {
                border: none;
                margin-top: -25px;  /* تقريب الأزرار للأعلى */
                padding: 0;
            }
        ''')

        # تعديل تخطيط أزرار المندوبين
        self.delegates_layout = QGridLayout(self.delegates_group)
        self.delegates_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهامش العلوي
        self.delegates_layout.setSpacing(5)  # مسافات صغيرة بين الأزرار

        content_layout.addWidget(self.delegates_group)

        # إضافة زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: {self.CONTROL_BTN_MIN_WIDTH}px;
                max-width: {self.CONTROL_BTN_MAX_WIDTH}px;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
        """)
        close_button.clicked.connect(self.close)
        content_layout.addWidget(close_button, 0, Qt.AlignCenter)

        # إضافة العناصر إلى التخطيط الرئيسي بالترتيب المطلوب
        main_layout.addWidget(title)
        main_layout.addWidget(content)

        self.setCentralWidget(central_widget)

    def get_delegates(self):
        """الحصول على قائمة المندوبين من قاعدة البيانات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # طباعة إضافية للتشخيص
            print("\nبدء استعلام جلب المندوبين...")

            # جلب المندوبين من جدول delegates الأساسي مع حساب عدد الطلبات من جدول delegate_orders
            # تم تعديل الاستعلام لجلب المندوبين الذين لديهم طلبات فقط (عدد الطلبات > 0)
            cursor.execute('''
                SELECT
                    d.name as delegate_name,
                    COUNT(do.id) as orders_count
                FROM delegates d
                LEFT JOIN delegate_orders do ON d.name = do.representative_name
                GROUP BY d.name
                HAVING COUNT(do.id) > 0
                ORDER BY d.name
            ''')

            delegates = []
            for row in cursor.fetchall():
                delegate_name, orders_count = row
                delegates.append((delegate_name, orders_count))
                print(f"مندوب: {delegate_name}, عدد الطلبات: {orders_count}")

            # جلب عدد السجلات في جدول delegate_orders للتشخيص
            cursor.execute("SELECT COUNT(*) FROM delegate_orders")
            total_orders = cursor.fetchone()[0]
            print(f"إجمالي عدد الطلبات في جدول delegate_orders: {total_orders}")

            # طباعة بعض سجلات جدول delegate_orders للتشخيص
            cursor.execute("SELECT id, representative_name, order_id FROM delegate_orders LIMIT 5")
            sample_orders = cursor.fetchall()
            print("\nعينة من سجلات جدول delegate_orders:")
            for order in sample_orders:
                print(f"معرف: {order[0]}, اسم المندوب: {order[1]}, معرف الطلب: {order[2]}")

            conn.close()
            return delegates

        except Exception as e:
            print(f"خطأ في جلب قائمة المندوبين: {str(e)}")
            return []

    def load_delegates(self):
        """تحميل المندوبين من قاعدة البيانات"""
        try:
            # حذف المندوبين الذين ليس لديهم طلبات
            self.delete_delegates_without_orders()

            # مسح أزرار المندوبين الحالية
            self.clear_delegates_layout()

            # جلب قائمة المندوبين
            delegates = self.get_delegates()

            # طباعة قائمة المندوبين
            print("\nقائمة المندوبين المعروضة:")
            for i, delegate in enumerate(delegates, 1):
                delegate_name, orders_count = delegate
                print(f"{i}. {delegate_name} - عدد الطلبات: {orders_count}")

            if not delegates:
                no_delegates = QLabel('لا توجد مندوبين مسجلين بطلبات حالية')
                no_delegates.setAlignment(Qt.AlignCenter)
                no_delegates.setStyleSheet('font-size: 14pt; color: #7f8c8d; margin: 20px 0px;')
                self.delegates_layout.addWidget(no_delegates, 0, 0, 1, 6)
            else:
                # إنشاء أزرار للمندوبين
                row = 0
                col = 0
                for delegate in delegates:
                    delegate_name, orders_count = delegate
                    if delegate_name and delegate_name.strip():
                        # إنشاء حاوية للزر والمعلومات
                        delegate_widget = QWidget()
                        delegate_layout = QVBoxLayout(delegate_widget)
                        delegate_layout.setContentsMargins(5, 5, 5, 5)

                        # إنشاء الزر مع اسم المندوب وعدد الطلبات
                        button_text = f"{delegate_name} ({orders_count})"
                        delegate_button = QPushButton(button_text)
                        delegate_button.setStyleSheet('''
                            QPushButton {
                                background-color: #3498db;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                padding: 8px;
                                font-size: 11pt;
                                font-weight: bold;
                                text-align: center;
                                min-width: 100px;
                                max-width: 100px;
                                min-height: 90px;
                                max-height: 90px;
                            }
                            QPushButton:hover {
                                background-color: #2980b9;
                            }
                        ''')

                        delegate_button.clicked.connect(
                            lambda checked, d_name=delegate_name:
                            self.show_delegate_orders(d_name)
                        )

                        # اضافة زر المندوب
                        delegate_layout.addWidget(delegate_button)

                        self.delegates_layout.addWidget(delegate_widget, row, col)

                        # انتقال للعمود التالي، أو للصف التالي إذا وصلنا إلى آخر عمود
                        col += 1
                        if col >= 6:  # 6 أزرار في كل صف
                            col = 0
                            row += 1

        except Exception as e:
            print(f"خطأ في تحميل المندوبين: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل المندوبين: {str(e)}")

    def clear_delegates_layout(self):
        # مسح أزرار المندوبين الحالية
        if hasattr(self, 'delegates_layout') and self.delegates_layout:
            while self.delegates_layout.count():
                child = self.delegates_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

    def show_delegate_orders(self, delegate_name):
        """عرض طلبات المندوب المحدد - تعديل جذري ليعتمد على جدول orders مباشرة"""
        try:
            # تخزين المندوب الحالي
            self.current_delegate_name = delegate_name

            conn = self.db.get_connection()
            cursor = conn.cursor()

            print(f"\nجلب طلبات المندوب: {delegate_name}")

            # استعلام جديد تماماً: استرجاع البيانات من جدول orders مباشرة حيث delegate_name يطابق
            # هذا سيحل المشكلة الجذرية بعدم عرض البيانات المحدثة
            if self.db.db_type == 'mysql':
                # استخدام %s للمعاملات في MySQL
                cursor.execute("""
                    SELECT
                        o.id,
                        o.receipt_number,
                        o.phone,
                        o.address,
                        o.carpet_count,
                        o.blanket_count,
                        o.total_price,
                        o.created_at,
                        o.status,
                        o.notes
                    FROM orders o
                    WHERE o.delegate_name = %s
                    ORDER BY o.created_at DESC
                """, (delegate_name,))
            else:
                # استخدام ? للمعاملات في SQLite
                cursor.execute("""
                    SELECT
                        o.id,
                        o.receipt_number,
                        o.phone,
                        o.address,
                        o.carpet_count,
                        o.blanket_count,
                        o.total_price,
                        o.created_at,
                        o.status,
                        o.notes
                    FROM orders o
                    WHERE o.delegate_name = ?
                    ORDER BY o.created_at DESC
                """, (delegate_name,))

            orders_from_main_table = cursor.fetchall()

            # طباعة نتائج الاستعلام المباشر من جدول orders
            print(f"عدد الطلبات المسترجعة مباشرة من جدول orders: {len(orders_from_main_table)}")
            if orders_from_main_table:
                print("أول 3 طلبات من جدول orders:")
                for i, order in enumerate(orders_from_main_table[:min(3, len(orders_from_main_table))]):
                    print(f"طلب {i+1} (ID: {order[0]}): receipt_number='{order[1]}', carpet_count={order[4]}, blanket_count={order[5]}, total_price={order[6]}")

            # إذا لم تكن هناك طلبات في orders، نجرب البحث عن طريق delegate_orders
            if not orders_from_main_table:
                print("لا توجد طلبات في جدول orders للمندوب، سنبحث في جدول delegate_orders")

                # أولاً نحصل على معرفات الطلبات من جدول delegate_orders
                if self.db.db_type == 'mysql':
                    # استخدام %s للمعاملات في MySQL
                    cursor.execute("""
                        SELECT id, order_id FROM delegate_orders WHERE representative_name = %s
                    """, (delegate_name,))
                else:
                    # استخدام ? للمعاملات في SQLite
                    cursor.execute("""
                        SELECT id, order_id FROM delegate_orders WHERE representative_name = ?
                    """, (delegate_name,))

                delegate_order_ids = cursor.fetchall()
                print(f"عدد الطلبات في delegate_orders: {len(delegate_order_ids)}")

                # نستخدم هذه المعرفات للبحث في جدول orders
                order_ids = []
                for do_id, order_id in delegate_order_ids:
                    if order_id and order_id > 0:
                        order_ids.append(str(order_id))
                    else:
                        order_ids.append(str(do_id))

                if order_ids:
                    # استرجاع هذه الطلبات من جدول orders بناءً على معرفات الطلبات
                    orders_id_str = ','.join(order_ids)
                    cursor.execute(f"""
                        SELECT
                            id,
                            receipt_number,
                            phone,
                            address,
                            carpet_count,
                            blanket_count,
                            total_price,
                            created_at,
                            status,
                            notes
                        FROM orders
                        WHERE id IN ({orders_id_str})
                        ORDER BY created_at DESC
                    """)

                    orders_from_main_table = cursor.fetchall()
                    print(f"عدد الطلبات المسترجعة من جدول orders بناءً على معرفات delegate_orders: {len(orders_from_main_table)}")

                    if orders_from_main_table:
                        print("أول 3 طلبات من الاستعلام البديل:")
                        for i, order in enumerate(orders_from_main_table[:min(3, len(orders_from_main_table))]):
                            print(f"طلب {i+1} (ID: {order[0]}): receipt_number='{order[1]}', carpet_count={order[4]}, blanket_count={order[5]}, total_price={order[6]}")

            conn.close()

            # إذا لم يكن هناك أي طلبات، نظهر رسالة
            if not orders_from_main_table:
                QMessageBox.information(self, "معلومات", f"لا توجد طلبات للمندوب {delegate_name}")
                return

            # نستخدم النتيجة النهائية لإنشاء واجهة العرض
            orders = orders_from_main_table

            # إنشاء نافذة عرض الطلبات
            orders_dialog = QDialog(self)
            orders_dialog.setWindowTitle(f"طلبات المندوب: {delegate_name}")
            orders_dialog.setModal(True)
            orders_dialog.setMinimumSize(self.ORDERS_DIALOG_WIDTH, self.ORDERS_DIALOG_HEIGHT)

            # تهيئة الجدول وعرض الطلبات
            self.init_orders_table(orders_dialog, orders)

            # إضافة معالج أحداث المفاتيح للنافذة
            self.setup_keyboard_navigation(orders_dialog)

            orders_dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض طلبات المندوب: {str(e)}")
            print(traceback.format_exc())  # طباعة تفاصيل الخطأ كاملة
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض طلبات المندوب: {str(e)}")

    def init_orders_table(self, orders_dialog, orders):
        """تهيئة جدول الطلبات"""
        layout = QVBoxLayout()
        orders_dialog.setLayout(layout)

        # تخزين مرجع للحوار الحالي
        self.current_dialog = orders_dialog

        # إضافة حقل البحث في تخطيط منفصل
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setStyleSheet('font-size: 12pt; font-weight: bold;')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث برقم الهاتف...")
        self.search_input.setStyleSheet('''
            QLineEdit {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12pt;
                min-width: 250px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        ''')

        # إضافة اتصال حقل البحث مع دالة التصفية
        self.search_input.textChanged.connect(lambda text: self.filter_orders(orders_table, text))

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        layout.addLayout(search_layout)

        # إضافة زر تحديد/إلغاء تحديد الكل وزر الطباعة في تخطيط منفصل
        select_layout = QHBoxLayout()

        # زر تحديد الطلبات
        self.select_all_button = QPushButton("تحديد الطلبات")
        self.select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_all_button.clicked.connect(lambda: self.toggle_all_selections())
        select_layout.addWidget(self.select_all_button)

        # زر طباعة الطلبات المحددة
        self.print_button = QPushButton("طباعة الطلبات")
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #219653;
            }
        """)
        self.print_button.clicked.connect(self.print_selected_orders)
        select_layout.addWidget(self.print_button)

        select_layout.addStretch()
        layout.addLayout(select_layout)

        # إضافة متغير لتتبع حالة التحديد
        self.all_selected = False

        # تحديث قائمة الأعمدة لتشمل عمود التحديد
        columns = [
            "تحديد", "ID", "رقم الوصل", "رقم الهاتف", "العنوان", "عدد السجاد",
            "عدد البطانية", "المساحة (م²)", "السعر الإجمالي",
            "تاريخ الإنشاء", "الحالة", "ملاحظات", "النقل", "تفاصيل", "تعديل", "إرجاع"
        ]

        # إنشاء جدول الطلبات
        orders_table = QTableWidget()
        self.current_orders_table = orders_table
        orders_table.setColumnCount(16)  # زيادة عدد الأعمدة لإضافة عمود التحديد
        orders_table.setRowCount(len(orders))

        # تعيين سلوك التحديد للجدول
        orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        orders_table.setSelectionMode(QTableWidget.SingleSelection)

        # تمكين تظليل الصفوف عند النقر عليها
        orders_table.setAlternatingRowColors(True)

        # تعيين رؤوس الأعمدة - تغيير ترتيب التفاصيل والتعديل
        for i, column in enumerate(columns):
            header_item = QTableWidgetItem(column)
            orders_table.setHorizontalHeaderItem(i, header_item)

        # تنسيق الجدول
        orders_table.setStyleSheet('''
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11pt;
                alternate-background-color: #f2f2f2;
            }
            QHeaderView::section {
                background-color: #3498db;
                padding: 5px;
                border: 1px solid #bdc3c7;
                font-size: 11pt;
                font-weight: bold;
                color: white;
            }
            QTableWidget::item {
                border: 1px solid #bdc3c7;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        ''')

        # تعيين عرض الأعمدة بشكل دقيق (بالبكسل)
        column_widths = {
            0: 50,   # عمود التحديد
            1: 0,    # ID
            2: 80,   # رقم الوصل
            3: 110,  # رقم الهاتف
            4: 170,  # العنوان
            5: 70,   # عدد السجاد
            6: 70,   # عدد البطانية
            7: 80,   # المساحة (م²)
            8: 80,   # السعر الإجمالي
            9: 100,  # تاريخ الإنشاء
            10: 0,   # الحالة
            11: 130, # ملاحظات
            12: 60,  # رسوم النقل
            13: 90,  # تفاصيل
            14: 90,  # تعديل
            15: 90   # إرجاع
        }

        # تطبيق عرض محدد لكل عمود
        for col, width in column_widths.items():
            orders_table.setColumnWidth(col, width)

        # إخفاء عمود ID وعمود الحالة
        orders_table.hideColumn(1)   # إخفاء عمود ID
        orders_table.hideColumn(10)  # إخفاء عمود الحالة

        # استعداد للحساب المساحة التقريبية لكل طلب
        conn = self.db.get_connection()
        cursor = conn.cursor()
        total_area_dict = {}

        # جلب أبعاد السجاد لكل الطلبات مرة واحدة
        order_ids = [str(order[0]) for order in orders]
        if order_ids:
            try:
                order_ids_str = ",".join(order_ids)
                cursor.execute(f"""
                    SELECT order_id, SUM(total_area)
                    FROM carpet_dimensions
                    WHERE order_id IN ({order_ids_str})
                    GROUP BY order_id
                """)
                area_results = cursor.fetchall()
                for result in area_results:
                    order_id, area = result
                    total_area_dict[order_id] = area
            except Exception as e:
                print(f"خطأ في جلب أبعاد السجاد: {str(e)}")

        # إضافة بيانات الطلبات إلى الجدول
        for row, order in enumerate(orders):
            for col, value in enumerate(order):
                if value is None:
                    value = ""

                # حساب عمود الإدخال بناءً على ترتيب البيانات
                col_index = col + 1

                # معالجة عدد السجاد - العمود الرابع في الاستعلام
                if col == 4:  # عدد السجاد
                    carpet_count_item = QTableWidgetItem(str(value))
                    carpet_count_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 5, carpet_count_item)

                # معالجة عدد البطانية - العمود الخامس في الاستعلام
                elif col == 5:  # عدد البطانية
                    blanket_count_item = QTableWidgetItem(str(value))
                    blanket_count_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 6, blanket_count_item)

                # معالجة المساحة - المساحة هي في العمود area وليس كقيمة مباشرة
                elif col == 3:  # العنوان (المساحة تحسب منه)
                    area = value or ""

                    # وضع العنوان في العمود الثالث
                    address_item = QTableWidgetItem(str(area))
                    address_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 4, address_item)

                    # حساب المساحة من جدول carpet_dimensions
                    order_id = order[0]
                    if order_id in total_area_dict:
                        total_area = float(total_area_dict[order_id])
                        # تقريب المساحة حسب القاعدة المطلوبة
                        decimal_part = total_area - int(total_area)
                        if decimal_part >= 0.5:  # إذا كان الكسر 0.5 أو أكثر
                            total_area = int(total_area) + 1
                        else:  # إذا كان الكسر أقل من 0.5
                            total_area = int(total_area)

                        carpet_area = str(total_area)
                    else:
                        carpet_area = "0"

                    carpet_area_item = QTableWidgetItem(carpet_area)
                    carpet_area_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 7, carpet_area_item)

                # معالجة السعر الإجمالي
                elif col == 6:  # السعر الإجمالي (العمود السادس في الاستعلام)
                    formatted_price = self.format_price(value)
                    price_item = QTableWidgetItem(formatted_price)
                    price_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 8, price_item)

                # معالجة التاريخ
                elif col == 7:  # التاريخ
                    try:
                        dt = QDateTime.fromString(str(value), "yyyy-MM-dd HH:mm:ss")
                        value = dt.toString("yyyy-MM-dd")
                    except:
                        pass
                    date_item = QTableWidgetItem(str(value))
                    date_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 9, date_item)

                # نقل العمود الثامن (الحالة) إلى العمود العاشر
                elif col == 8:  # الحالة
                    status_item = QTableWidgetItem(str(value))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 10, status_item)

                # نقل العمود التاسع (الملاحظات) إلى العمود الحادي عشر
                elif col == 9:  # الملاحظات
                    notes_item = QTableWidgetItem(str(value))
                    notes_item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, 11, notes_item)

                # معالجة رقم الهاتف
                elif col == 2:  # رقم الهاتف
                    phone_widget = QWidget()
                    phone_layout = QVBoxLayout(phone_widget)
                    phone_layout.setContentsMargins(0, 0, 0, 0)  # تقليل الهوامش
                    phone_layout.setSpacing(0)  # تقليل المسافة بين الأرقام

                    # رقم الهاتف الأول
                    if value:
                        phone1_label = QLabel(str(value))
                        phone1_label.setAlignment(Qt.AlignCenter)
                        phone1_label.setStyleSheet("""
                            QLabel {
                                font-size: 9pt;
                                padding: 2px;
                                margin: 0px;
                                color: #2c3e50;
                            }
                        """)
                        phone_layout.addWidget(phone1_label)

                    # التحقق من وجود رقم هاتف ثاني في قاعدة البيانات
                    if self.db.db_type == 'mysql':
                        cursor.execute("SELECT phone2 FROM orders WHERE id = %s", (order[0],))
                    else:
                        cursor.execute("SELECT phone2 FROM orders WHERE id = ?", (order[0],))
                    phone2_result = cursor.fetchone()
                    if phone2_result and phone2_result[0]:
                        phone2_label = QLabel(str(phone2_result[0]))
                        phone2_label.setAlignment(Qt.AlignCenter)
                        phone2_label.setStyleSheet("""
                            QLabel {
                                font-size: 9pt;
                                padding: 2px;
                                margin: 0px;
                                color: #2c3e50;
                            }
                        """)
                        phone_layout.addWidget(phone2_label)
                        # تحديث ارتفاع الصف عند وجود رقمين
                        orders_table.setRowHeight(row, 45)
                    else:
                        # تحديث ارتفاع الصف عند وجود رقم واحد
                        orders_table.setRowHeight(row, 40)

                    orders_table.setCellWidget(row, 3, phone_widget)

                # إضافة بقية الأعمدة بشكل طبيعي
                elif col in [0, 1]:  # الأعمدة من 0 إلى 1
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    orders_table.setItem(row, col + 1, item)

            # إضافة عمود رسوم النقل إذا وجدت
            order_id = order[0]
            # استخدام delivery_fee في جميع قواعد البيانات
            delivery_fee_query = """
                SELECT delivery_fee FROM orders WHERE id = {}
            """.format('%s' if self.db.db_type == 'mysql' else '?')

            cursor.execute(delivery_fee_query, (order_id,))
            delivery_fee_result = cursor.fetchone()
            delivery_fee = delivery_fee_result[0] if delivery_fee_result else 0

            # إضافة قيمة رسوم النقل
            if delivery_fee and float(delivery_fee) > 0:
                transport_item = QTableWidgetItem("تم")
            else:
                transport_item = QTableWidgetItem("")

            transport_item.setTextAlignment(Qt.AlignCenter)
            orders_table.setItem(row, 12, transport_item)

            # إضافة زر التعديل في العمود 13
            edit_container = QWidget()
            edit_layout = QHBoxLayout(edit_container)
            edit_layout.setContentsMargins(2, 2, 2, 2)
            edit_layout.setAlignment(Qt.AlignCenter)

            # جلب معرف الطلب من العمود الأول
            order_id = int(orders_table.item(row, 1).text())

            edit_btn = QPushButton("تعديل")
            edit_btn.setProperty("order_id", order_id)  # تخزين معرف الطلب في خصائص الزر
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 6px;
                    font-size: 9pt;
                    min-width: 60px;
                }
                QPushButton:hover {
                    background-color: #d35400;
                }
            """)
            edit_btn.clicked.connect(lambda checked, id=order_id: self.edit_order(id))
            edit_layout.addWidget(edit_btn)

            orders_table.setCellWidget(row, 14, edit_container)

            # إضافة زر التفاصيل في العمود المخصص (12)
            details_container = QWidget()
            details_layout = QHBoxLayout(details_container)
            details_layout.setContentsMargins(2, 2, 2, 2)
            details_layout.setAlignment(Qt.AlignCenter)

            details_btn = QPushButton("تفاصيل")
            details_btn.setProperty("order_id", order[0])  # تخزين معرف الطلب
            details_btn.setStyleSheet(
                "QPushButton { background-color: #3498db; color: white; border-radius: 10px; padding: 6px; font-size: 9pt; min-width: 60px; }"
                "QPushButton:hover { background-color: #2980b9; }"
            )
            details_btn.clicked.connect(lambda checked, id=order[0]: self.show_order_details(id))
            details_layout.addWidget(details_btn)
            orders_table.setCellWidget(row, 13, details_container)  # تعيين زر التفاصيل في العمود 12

            # إضافة زر الإرجاع في العمود 14
            return_container = QWidget()
            return_layout = QHBoxLayout(return_container)
            return_layout.setContentsMargins(1, 1, 1, 1)
            return_layout.setAlignment(Qt.AlignCenter)

            order_id = int(orders_table.item(row, 1).text())
            return_btn = QPushButton("إرجاع")
            return_btn.setProperty("order_id", order_id)
            return_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 6px;
                    font-size: 9pt;
                    min-width: 60px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            return_btn.clicked.connect(lambda checked, id=order_id: self.return_to_warehouse(id))
            return_layout.addWidget(return_btn)
            orders_table.setCellWidget(row, 15, return_container)

            # إضافة مربعات التحديد في العمود الأول
            checkbox = QCheckBox()
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            orders_table.setCellWidget(row, 0, checkbox_widget)

        conn.close()

        # تعيين المحاذاة لرؤوس الأعمدة
        for i in range(orders_table.columnCount()):
            orders_table.horizontalHeaderItem(i).setTextAlignment(Qt.AlignCenter)

        # تكبير الخط في رؤوس الأعمدة
        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(10)
        for i in range(orders_table.columnCount()):
            orders_table.horizontalHeaderItem(i).setFont(header_font)

        # تمييز الأعمدة المهمة بلون مختلف
        orders_table.horizontalHeaderItem(5).setBackground(Qt.lightGray)  # عدد السجاد
        orders_table.horizontalHeaderItem(6).setBackground(Qt.lightGray)  # عدد البطانية
        orders_table.horizontalHeaderItem(7).setBackground(Qt.lightGray)  # المساحة
        orders_table.horizontalHeaderItem(12).setBackground(Qt.lightGray) # رسوم النقل
        orders_table.horizontalHeaderItem(14).setBackground(Qt.yellow)    # تعديل

        # السماح للمستخدم بتغيير عرض الأعمدة يدوياً
        orders_table.horizontalHeader().setSectionsMovable(True)
        orders_table.horizontalHeader().setStretchLastSection(True)

        # إضافة الجدول إلى التخطيط
        layout.addWidget(orders_table)

    def toggle_all_selections(self):
        """تبديل حالة تحديد جميع الطلبات"""
        if self.current_orders_table:
            self.all_selected = not self.all_selected

            # تغيير نص الزر
            self.select_all_button.setText("إلغاء تحديد الطلبات" if self.all_selected else "تحديد الطلبات")

            # تغيير لون خلفية الزر
            self.select_all_button.setStyleSheet("""
                QPushButton {
                    background-color: """ + ("#e74c3c" if self.all_selected else "#3498db") + """;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-size: 11pt;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: """ + ("#c0392b" if self.all_selected else "#2980b9") + """;
                }
            """)

            # تحديث حالة مربعات التحديد
            for row in range(self.current_orders_table.rowCount()):
                checkbox_widget = self.current_orders_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(self.all_selected)

    def get_selected_orders(self):
        """الحصول على قائمة معرفات الطلبات المحددة"""
        selected_orders = []
        if self.current_orders_table:
            for row in range(self.current_orders_table.rowCount()):
                checkbox_widget = self.current_orders_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        order_id_item = self.current_orders_table.item(row, 1)  # معرف الطلب في العمود الثاني
                        if order_id_item:
                            selected_orders.append(int(order_id_item.text()))
        return selected_orders

    def filter_orders(self, table, search_text):
        """تصفية الطلبات حسب رقم الهاتف"""
        search_text = search_text.strip()

        for row in range(table.rowCount()):
            show_row = True

            if search_text:
                phone_widget = table.cellWidget(row, 3)  # عمود رقم الهاتف
                if phone_widget:
                    found_match = False
                    # البحث في كل الأرقام الموجودة في الخلية
                    for phone_label in phone_widget.findChildren(QLabel):
                        phone_number = phone_label.text().strip()
                        if phone_number:
                            # تحويل كل من نص البحث ونص الهاتف إلى سلسلة نصية للمقارنة
                            search_str = str(search_text)
                            phone_str = str(phone_number)

                            # البحث في بداية أو نهاية الرقم
                            if phone_str.startswith(search_str) or phone_str.endswith(search_str):
                                found_match = True
                                break

                    show_row = found_match
                else:
                    show_row = False

            table.setRowHidden(row, not show_row)

    def delete_delegates_without_orders(self):
        """حذف المندوبين الذين ليس لديهم طلبات من قاعدة البيانات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # التعرف على المندوبين الذين ليس لديهم طلبات
            cursor.execute('''
                SELECT d.name, d.id
                FROM delegates d
                LEFT JOIN delegate_orders do ON d.name = do.representative_name
                GROUP BY d.name, d.id
                HAVING COUNT(do.id) = 0
            ''')

            delegates_to_delete = [(row[0], row[1]) for row in cursor.fetchall()]

            if delegates_to_delete:
                print(f"\nحذف المندوبين الذين ليس لديهم طلبات: {', '.join([d[0] for d in delegates_to_delete])}")

                # قبل الحذف، نزيل العلاقات من جدول orders
                for _, delegate_id in delegates_to_delete:
                    # حذف سجلات الطلبات المرتبطة بالمندوب قبل حذف المندوب
                    if self.db.db_type == 'mysql':
                        cursor.execute("UPDATE orders SET delegate_id = NULL, delegate_name = NULL WHERE delegate_id = %s", (delegate_id,))
                    else:
                        cursor.execute("UPDATE orders SET delegate_id = NULL, delegate_name = NULL WHERE delegate_id = ?", (delegate_id,))

                # الآن حذف المندوبين
                for delegate_name, _ in delegates_to_delete:
                    if self.db.db_type == 'mysql':
                        cursor.execute("DELETE FROM delegates WHERE name = %s", (delegate_name,))
                    else:
                        cursor.execute("DELETE FROM delegates WHERE name = ?", (delegate_name,))

                conn.commit()
                print(f"تم حذف {len(delegates_to_delete)} مندوب من قاعدة البيانات")
            else:
                print("لا يوجد مندوبين بدون طلبات للحذف")

            # الإغلاق الآمن للاتصال
            conn.close()
        except Exception as e:
            # معالجة الخطأ وإعادة تعيين الاتصال
            try:
                if conn:
                    conn.rollback()
                    conn.close()
            except:
                pass
            print(f"خطأ في حذف المندوبين الذين ليس لديهم طلبات: {str(e)}")

    def edit_order(self, order_id):
        """فتح نافذة تعديل الطلب البسيطة"""
        try:
            from src.simple_edit_order import SimpleEditOrderDialog

            # إنشاء كائن من نافذة التعديل البسيطة
            edit_dialog = SimpleEditOrderDialog(parent=self, order_id=order_id)

            # عرض نافذة التعديل والانتظار للنتيجة
            result = edit_dialog.exec_()

            # إذا تم حفظ التعديلات، نقوم بتحديث الواجهة
            if result == QDialog.Accepted:
                # عرض رسالة نجاح
                QMessageBox.information(self, "تم التعديل", f"تم تعديل الطلب رقم {order_id} بنجاح وتم نقله إلى المخزن")

                # التحقق من حالة الطلب بعد التعديل
                conn = self.db.get_connection()
                cursor = conn.cursor()
                if self.db.db_type == 'mysql':
                    cursor.execute("SELECT status FROM orders WHERE id = %s", (order_id,))
                else:
                    cursor.execute("SELECT status FROM orders WHERE id = ?", (order_id,))
                status_result = cursor.fetchone()
                conn.close()

                # إذا تم نقل الطلب إلى المخزن، نقوم بإزالته من الجدول
                if status_result and status_result[0] == 'warehouse':
                    if self.current_orders_table:
                        # البحث عن الصف المطابق لمعرف الطلب وإزالته
                        for row in range(self.current_orders_table.rowCount()):
                            id_item = self.current_orders_table.item(row, 1)
                            if id_item and int(id_item.text()) == order_id:
                                self.current_orders_table.removeRow(row)
                                print(f"تم إزالة الطلب رقم {order_id} من الجدول بعد نقله إلى المخزن")
                                break
                else:
                    # إذا لم يتم نقل الطلب إلى المخزن، نقوم بتحديث الصف فقط
                    if self.current_orders_table:
                        print(f"تحديث الصف للطلب {order_id} في الجدول الحالي")
                        self.update_single_order_row(self.current_orders_table, order_id)
                    else:
                        # إذا لم يكن الجدول متاحاً، نعيد تحميل المندوب
                        print(f"لم يتم العثور على الجدول الحالي، سيتم إعادة تحميل طلبات المندوب")
                        if self.current_delegate_name:
                            self.show_delegate_orders(self.current_delegate_name)

        except Exception as e:
            print(f"خطأ في فتح نافذة تعديل الطلب: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة تعديل الطلب: {str(e)}")

    def update_single_order_row(self, table, order_id):
        """تحديث صف واحد فقط في جدول الطلبات بعد التعديل"""
        try:
            print(f"تحديث صف الطلب رقم {order_id} فقط...")

            # 1. البحث عن الصف المطابق لمعرف الطلب في الجدول الحالي
            target_row = -1
            for row in range(table.rowCount()):
                id_item = table.item(row, 1)
                if id_item and int(id_item.text()) == order_id:
                    target_row = row
                    break

            if target_row == -1:
                print(f"لم يتم العثور على الطلب رقم {order_id} في الجدول")
                return False

            # 2. جلب البيانات المحدثة من قاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # جلب البيانات المحدثة من جدول orders
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT
                        receipt_number,
                        phone,
                        address,
                        carpet_count,
                        blanket_count,
                        total_price,
                        status,
                        notes,
                        delivery_fee
                    FROM orders WHERE id = %s
                """, (order_id,))
            else:
                cursor.execute("""
                    SELECT
                        receipt_number,
                        phone,
                        address,
                        carpet_count,
                        blanket_count,
                        total_price,
                        status,
                        notes,
                        delivery_fee
                    FROM orders WHERE id = ?
                """, (order_id,))

            order_data = cursor.fetchone()
            if not order_data:
                print(f"لم يتم العثور على الطلب رقم {order_id} في قاعدة البيانات")
                conn.close()
                return False

            receipt_number, phone, address, carpet_count, blanket_count, total_price, status, notes, delivery_fee = order_data

            # جلب المساحة من carpet_dimensions
            if self.db.db_type == 'mysql':
                cursor.execute("""
                    SELECT COALESCE(SUM(total_area), 0) FROM carpet_dimensions WHERE order_id = %s
                """, (order_id,))
            else:
                cursor.execute("""
                    SELECT COALESCE(SUM(total_area), 0) FROM carpet_dimensions WHERE order_id = ?
                """, (order_id,))

            total_area_result = cursor.fetchone()
            total_area = total_area_result[0] if total_area_result and total_area_result[0] is not None else 0

            conn.close()

            # 3. تحديث الخلايا في الجدول
            # رقم الوصل - العمود 1
            receipt_item = QTableWidgetItem(str(receipt_number) if receipt_number is not None else '')
            receipt_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 2, receipt_item)

            # رقم الهاتف - العمود 2
            phone_item = QTableWidgetItem(str(phone) if phone is not None else '')
            phone_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 3, phone_item)

            # العنوان - العمود 3
            address_item = QTableWidgetItem(str(address) if address is not None else '')
            address_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 4, address_item)

            # عدد السجاد - العمود 4
            carpet_item = QTableWidgetItem(str(carpet_count) if carpet_count is not None else '0')
            carpet_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 5, carpet_item)

            # عدد البطانية - العمود 5
            blanket_item = QTableWidgetItem(str(blanket_count) if blanket_count is not None else '0')
            blanket_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 6, blanket_item)

            # المساحة - العمود 6
            area_item = QTableWidgetItem(f"{total_area:.2f}")
            area_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 7, area_item)

            # السعر الإجمالي - العمود 7 - استخدام دالة تنسيق السعر
            price_item = QTableWidgetItem(self.format_price(total_price) if total_price is not None else '0')
            price_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 8, price_item)

            # الحالة - العمود 9
            status_item = QTableWidgetItem(str(status) if status is not None else '')
            status_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 10, status_item)

            # الملاحظات - العمود 10
            notes_item = QTableWidgetItem(str(notes) if notes is not None else '')
            notes_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 11, notes_item)

            # رسوم النقل - العمود 11
            if delivery_fee and float(delivery_fee) > 0:
                delivery_fee_item = QTableWidgetItem("تم")
            else:
                delivery_fee_item = QTableWidgetItem("")

            delivery_fee_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(target_row, 12, delivery_fee_item)

            print(f"تم تحديث صف الطلب رقم {order_id} بنجاح")
            return True

        except Exception as e:
            print(f"خطأ في تحديث صف الطلب: {str(e)}")
            print(traceback.format_exc())
            return False

    def format_price(self, price):
        """تنسيق السعر بإضافة فواصل كل 3 أرقام وحذف النقطة العشرية"""
        try:
            # التأكد من أن القيمة رقمية
            if isinstance(price, str):
                price = price.replace(',', '')  # إزالة أي فواصل موجودة مسبقاً
                try:
                    price = float(price)
                except ValueError:
                    return price  # إرجاع النص الأصلي إذا لم يكن رقم

            # تحويل الرقم إلى صحيح (حذف النقطة العشرية)
            price_int = int(float(price))

            # إضافة فواصل كل 3 أرقام
            formatted_price = "{:,}".format(price_int)

            return formatted_price
        except Exception as e:
            print(f"خطأ في تنسيق السعر: {str(e)}")
            return str(price)

    def setup_keyboard_navigation(self, orders_dialog):
        """إعداد التنقل باستخدام لوحة المفاتيح في نافذة طلبات المندوب"""
        # تعيين سياسة التركيز للجدول
        self.current_orders_table.setFocusPolicy(Qt.StrongFocus)

        # تعيين سلوك التحديد للجدول
        self.current_orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.current_orders_table.setSelectionMode(QTableWidget.SingleSelection)

        # تعيين التركيز على الجدول
        self.current_orders_table.setFocus()

        # تحديد الصف الأول إن وجد
        if self.current_orders_table.rowCount() > 0:
            self.current_orders_table.setCurrentCell(0, 0)
            self.current_orders_table.selectRow(0)  # تظليل الصف الأول

        # حفظ مرجع للنافذة
        self.orders_dialog = orders_dialog

        # إنشاء دالة لمعالجة مفتاح المسافة
        def handle_space_key():
            current_row = self.current_orders_table.currentRow()
            if current_row >= 0 and not self.current_orders_table.isRowHidden(current_row):
                checkbox_container = self.current_orders_table.cellWidget(current_row, 0)
                if checkbox_container:
                    checkbox = checkbox_container.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(not checkbox.isChecked())
                        self.current_orders_table.viewport().update()

        # إنشاء اختصارات لوحة المفاتيح
        from PyQt5.QtGui import QKeySequence
        from PyQt5.QtWidgets import QShortcut

        # اختصار مفتاح المسافة
        space_shortcut = QShortcut(QKeySequence(Qt.Key_Space), orders_dialog)
        space_shortcut.activated.connect(handle_space_key)

        # حفظ مراجع للاختصارات لمنع حذفها بواسطة جامع القمامة
        self.keyboard_shortcuts = [space_shortcut]

    def show_order_details(self, order_id):
        """عرض تفاصيل الطلب في نافذة منبثقة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # استعلام مبسط لجلب البيانات الأساسية
            if self.db.db_type == 'mysql':
                # استخدام delivery_fee في MySQL
                query = """
                SELECT
                    o.id,
                    o.receipt_number,
                    o.phone,
                    o.phone2,
                    o.address,
                    o.carpet_count,
                    o.blanket_count,
                    o.total_price,
                    o.created_at,
                    o.notes,
                    o.delivery_fee,
                    COALESCE((SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = o.id), 0) as total_area,
                    (SELECT GROUP_CONCAT(CONCAT(length, 'x', width, '=', total_area) SEPARATOR ', ')
                     FROM carpet_dimensions
                     WHERE order_id = o.id) as dimensions
                FROM orders o
                WHERE o.id = %s
                """
            else:
                # استخدام delivery_fee في SQLite أيضاً
                query = """
                SELECT
                    o.id,
                    o.receipt_number,
                    o.phone,
                    o.phone2,
                    o.address,
                    o.carpet_count,
                    o.blanket_count,
                    o.total_price,
                    o.created_at,
                    o.notes,
                    o.delivery_fee,
                    COALESCE((SELECT SUM(total_area) FROM carpet_dimensions WHERE order_id = o.id), 0) as total_area,
                    (SELECT GROUP_CONCAT(length || 'x' || width || '=' || total_area, ', ')
                     FROM carpet_dimensions
                     WHERE order_id = o.id) as dimensions
                FROM orders o
                WHERE o.id = ?
                """

            if self.db.db_type == 'mysql':
                cursor.execute(query, (order_id,))
            else:
                cursor.execute(query, (order_id,))
            result = cursor.fetchone()

            if not result:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب")
                conn.close()
                return

            # تنسيق السعر
            try:
                total_price = float(result[7] or 0)  # total_price هو العمود الثامن في الاستعلام
                formatted_price = f"{int(total_price):,}".replace(",", "٬") + " د.ع"
            except (ValueError, TypeError):
                formatted_price = "0 د.ع"

            # تنسيق المساحة والأبعاد
            dimensions_text = ""
            total_area = float(result[11] or 0)  # total_area هو العمود الثاني عشر
            dimensions = result[12]  # dimensions هو العمود الثالث عشر

            if dimensions:
                dimensions_list = dimensions.split(", ")
                for idx, dim in enumerate(dimensions_list, 1):
                    parts = dim.split("=")
                    if len(parts) == 2:
                        dimensions_text += f"السجادة {idx}: {parts[0]}م, المساحة = {float(parts[1]):.2f}م²<br>"
            else:
                dimensions_text = "لا توجد أبعاد مسجلة"

            # تحضير النص النهائي
            details = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial; font-size: 12pt; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                    th, td {{ padding: 8px; border: 1px solid #ddd; text-align: right; }}
                    th {{ background-color: #f5f5f5; font-weight: bold; }}
                </style>
            </head>
            <body>
                <h2 style="text-align: center;">تفاصيل الطلب</h2>
                <table>
                    <tr>
                        <th>رقم التسلسل:</th>
                        <td>{result[0]}</td>
                    </tr>
                    <tr>
                        <th>رقم الوصل:</th>
                        <td>{result[1] or ''}</td>
                    </tr>
                    <tr>
                        <th>رقم الهاتف:</th>
                        <td>{result[2] or ''}{' / ' + str(result[3]) if result[3] else ''}</td>
                    </tr>
                    <tr>
                        <th>العنوان:</th>
                        <td>{result[4] or ''}</td>
                    </tr>
                    <tr>
                        <th>عدد السجاد:</th>
                        <td>{result[5] or 0}</td>
                    </tr>
                    <tr>
                        <th>عدد البطانية:</th>
                        <td>{result[6] or 0}</td>
                    </tr>
                    <tr>
                        <th>المساحة الكلية:</th>
                        <td>{total_area:.2f} م²</td>
                    </tr>
                    <tr>
                        <th>تفاصيل الأبعاد:</th>
                        <td>{dimensions_text}</td>
                    </tr>
                    <tr>
                        <th>السعر الإجمالي:</th>
                        <td>{formatted_price}</td>
                    </tr>
                    <tr>
                        <th>رسوم النقل:</th>
                        <td>{"تم" if result[10] and float(result[10]) > 0 else "لا يوجد"}</td>
                    </tr>
                    <tr>
                        <th>ملاحظات:</th>
                        <td>{result[9] or ''}</td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # عرض رسالة التفاصيل
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تفاصيل الطلب")
            msg_box.setTextFormat(Qt.RichText)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setText(details)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()

            conn.close()

        except Exception as e:
            print(f"خطأ في عرض تفاصيل الطلب: {str(e)}")
            print(traceback.format_exc())  # طباعة تفاصيل الخطأ كاملة
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل الطلب: {str(e)}")

    def print_selected_orders(self):
        """طباعة الطلبات المحددة"""
        try:
            # الحصول على قائمة الطلبات المحددة
            selected_orders = self.get_selected_orders()

            if not selected_orders:
                QMessageBox.information(self, "تنبيه", "الرجاء تحديد طلب واحد على الأقل للطباعة")
                return

            # الاتصال بقاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # استعلام للحصول على بيانات الطلبات المحددة
            order_ids_str = ",".join([str(order_id) for order_id in selected_orders])
            query = f"""
                SELECT
                    id,
                    receipt_number,
                    phone,
                    phone2,
                    address,
                    notes
                FROM orders
                WHERE id IN ({order_ids_str})
                ORDER BY id
            """

            # استخدام الاستعلام مباشرة لأنه يحتوي على قائمة معرفات الطلبات
            cursor.execute(query)
            orders = cursor.fetchall()

            # إنشاء مستند HTML للطباعة
            html_content = self.create_print_html(orders)

            # إنشاء مستند نصي للطباعة
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            # تقليل هوامش الصفحة
            printer.setPageMargins(1, 1, 1, 1, QPrinter.Millimeter)

            # إنشاء نافذة معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle("معاينة طباعة الطلبات")
            preview_dialog.setMinimumSize(900, 600)

            # ربط حدث الطباعة بالمستند
            preview_dialog.paintRequested.connect(lambda p: document.print_(p))

            # عرض نافذة المعاينة
            preview_dialog.exec_()

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

        except Exception as e:
            print(f"خطأ في طباعة الطلبات: {str(e)}")
            print(traceback.format_exc())  # طباعة تفاصيل الخطأ كاملة
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة الطلبات: {str(e)}")

    def create_print_html(self, orders):
        """إنشاء محتوى HTML للطباعة"""
        # مسار الشعار
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icons", "al-waleed tower1.png")

        # تحميل الصورة وتغيير حجمها باستخدام QPixmap
        logo_pixmap = QPixmap(logo_path)
        # تغيير حجم الصورة إلى 100x100 بكسل مع الحفاظ على النسبة
        logo_pixmap = logo_pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        # حفظ الصورة المعدلة مؤقتًا
        temp_logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icons", "temp_logo.png")
        logo_pixmap.save(temp_logo_path, "PNG")

        # استخدام المسار المؤقت للصورة المعدلة
        logo_path = temp_logo_path.replace("\\", "/")  # تحويل المسار لتنسيق HTML

        # التاريخ الحالي
        current_date = QDateTime.currentDateTime().toString("yyyy-MM-dd")

        # بداية مستند HTML
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 0.5cm; }}
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 15px; }}
                .logo {{ width: 100px; height: auto; display: block; margin: 0 auto 5px auto; object-fit: contain; }}
                .company-name {{ font-size: 20pt; font-weight: bold; margin: 5px 0; color: #2c3e50; }}
                .title {{ font-size: 10pt; font-weight: bold; margin: 3px 0; text-align: left; }}
                .date {{ font-size: 12pt; margin-bottom: 10px; text-align: left; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th {{ background-color: #3498db; color: white; font-weight: bold; padding: 8px; text-align: center; border: 1px solid #ddd; }}
                td {{ padding: 8px; text-align: center; border: 1px solid #ddd; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .phone {{ color: #e74c3c; font-weight: bold; font-size: 12pt; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 15pt; color: #7f8c8d; }}
            </style>
        </head>
        <body>
            <div class="header">
                <img src="{logo_path}" class="logo" alt="شعار">
                <div class="company-name">شركة الوليد لغسيل السجاد</div>
                <h1 class="title">قائمة طلبات المندوب: {self.current_delegate_name}</h1>
                <div class="date">التاريخ: &nbsp;&nbsp;{current_date}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th width="40%">الملاحظات</th>
                        <th width="25%">الهاتف</th>
                        <th width="30%">العنوان</th>
                        <th width="5%">ت</th>
                    </tr>
                </thead>
                <tbody>
        """

        # ترتيب الطلبات حسب العنوان أبجدياً
        sorted_orders = sorted(orders, key=lambda order: order[4] or '') # ترتيب حسب العنوان (الفهرس 4)

        # إضافة صفوف الطلبات
        for index, order in enumerate(sorted_orders, 1):
            order_id, receipt_number, phone, phone2, address, notes = order

            # تنسيق أرقام الهاتف
            phone_html = f'<span class="phone">{phone}</span>'
            if phone2:
                phone_html += f'<br><span class="phone">{phone2}</span>'

            # إضافة صف للطلب
            html += f"""
                <tr>
                    <td>{notes or ''}</td>
                    <td>{phone_html}</td>
                    <td>{address or ''}</td>
                    <td>{index}</td>
                </tr>
            """

        # إغلاق الجدول والمستند
        html += """
                </tbody>
            </table>

            <div class="footer">
                <p>برج الوليد لغسيل السجاد</p>
            </div>
        </body>
        </html>
        """

        return html

    def return_to_warehouse(self, order_id):
        """ارجاع الطلب الى قائمة الطلبات الواردة"""
        try:
            # تأكيد العملية
            result = QMessageBox.question(
                self,
                "تأكيد الإرجاع",
                "هل أنت متأكد من إرجاع الطلب إلى قائمة الطلبات الواردة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if result == QMessageBox.Yes:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # تحديث حالة الطلب في جدول orders
                if self.db.db_type == 'mysql':
                    cursor.execute("""
                        UPDATE orders
                        SET status = 'جديد',
                            delegate_name = NULL,
                            delegate_id = NULL
                        WHERE id = %s
                    """, (order_id,))

                    # حذف الطلب من جدول delegate_orders
                    cursor.execute("DELETE FROM delegate_orders WHERE order_id = %s", (order_id,))
                else:
                    cursor.execute("""
                        UPDATE orders
                        SET status = 'جديد',
                            delegate_name = NULL,
                            delegate_id = NULL
                        WHERE id = ?
                    """, (order_id,))

                    # حذف الطلب من جدول delegate_orders
                    cursor.execute("DELETE FROM delegate_orders WHERE order_id = ?", (order_id,))

                conn.commit()
                conn.close()

                # تحديث واجهة المستخدم
                if self.current_orders_table:
                    for row in range(self.current_orders_table.rowCount()):
                        item = self.current_orders_table.item(row, 1)
                        if item and int(item.text()) == order_id:
                            self.current_orders_table.removeRow(row)
                            break

                QMessageBox.information(self, "تم الإرجاع", "تم إرجاع الطلب إلى قائمة الطلبات الواردة بنجاح")

        except Exception as e:
            print(f"خطأ في ارجاع الطلب الى قائمة الطلبات الواردة: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إرجاع الطلب إلى قائمة الطلبات الواردة: {str(e)}")
