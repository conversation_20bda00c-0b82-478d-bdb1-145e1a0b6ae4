import sqlite3

def check_schema():
    conn = sqlite3.connect('carpet_cleaning.db')
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print("Tables in database:")
    for table in tables:
        table_name = table[0]
        print(f"\nTable: {table_name}")
        
        # Get columns for each table
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("Columns:")
        for col in columns:
            # Print only column index, name and type to avoid encoding issues
            print(f"  {col[0]}: {col[1]} ({col[2]})")
    
    conn.close()

if __name__ == "__main__":
    check_schema()
